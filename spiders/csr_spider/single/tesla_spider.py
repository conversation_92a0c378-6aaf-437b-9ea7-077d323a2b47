import time

from spiders.core_spider.base_spider import Base<PERSON>pider
import logging
from concurrent.futures import ThreadPoolExecutor, as_completed
from datetime import datetime

from utils.http_utils.http_client import HTTPClient


class TeslaSpider(BaseSpider):
    def __init__(self, company_name, base_url, company_url, cwiq_code):
        super().__init__(company_name, base_url, company_url, cwiq_code)
        self.company_name = company_name
        self.company_url = company_url
        self.cwiq_code = cwiq_code
        self.headers = {
    'accept': 'application/json, text/plain, */*',
    'referer': 'https://www.tesla.com/careers/search/?site=US',
    'sec-ch-ua': '"Google Chrome";v="135", "Not-A.Brand";v="8", "Chromium";v="135"',
    'sec-ch-ua-mobile': '?0',
    'sec-ch-ua-platform': '"Windows"',
    'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36',
    }
        self.base_url = "https://www.tesla.com/cua-api/apps/careers/state"
        self.job_url = "https://www.tesla.com/cua-api/careers/job/"

        # 创建 HTTP 客户端
        self.client = HTTPClient()

    @classmethod
    def create_spider(cls, company_name, base_url, company_url, cwiq_code):
        """工厂方法创建 MedtronicSpider 实例"""
        return cls(company_name, base_url, company_url, cwiq_code)

    def make_request(self, method, url, company_name, requset_count,**kwargs):
        """发送请求的通用方法"""
        kwargs['headers'] = self.headers
        return self.client.request(method, url, company_name, requset_count,log_ip=False, **kwargs)

    def parse_job(self, response):
        """
        解析职位数据的抽象方法实现
        这个方法是为了满足BaseSpider的抽象方法要求
        实际的解析工作在fetch_job_details中完成
        """
        return response

    def build_city_to_country_map(self, data):
        """构建城市到国家的映射"""
        try:
            city_to_country_map = {}
            for geo_feature in data["geo"]:
                for site in geo_feature["sites"]:
                    country_id = site["id"]
                    if "states" in site:
                        for state in site["states"]:
                            for city, city_ids in state.get("cities", {}).items():
                                for city_id in city_ids:
                                    city_to_country_map[city_id] = country_id
                    elif "cities" in site:
                        if isinstance(site["cities"], dict):
                            for city_ids in site["cities"].values():
                                for city_id in city_ids:
                                    city_to_country_map[city_id] = country_id
            return city_to_country_map
        except Exception as e:
            logging.error(f"Failed to build city to country map due to {e} for {self.company_name}")

    def fetch_job_details(self, job_id, area_id, city_to_country_map):
        """获取职位详细信息"""
        failed_requests_list = []
        data = {}
        try:
            result_url = self.job_url + job_id
            result = self.make_request('GET', result_url, self.company_name,requset_count=3, )
            data = result.json()

        except Exception as e:
            logging.error(f"Failed to fetch job details for job_id {self.company_name} due to {e}")
            failed_requests_list.append({
                "method": 'GET',
                "url": self.job_url + job_id,
                "company_name": self.company_name,
            })
            # if failed_requests_list:
            #     for failed_request in failed_requests_list:
            #         data = self.retry_request(failed_request['method'], failed_request['url'],
            #                                   failed_request['company_name'], retries=3, delay=5,
            #                                   )

        try:
            state = data.get("location", "").split(",")[1] if data.get("location", "") and len(
                data.get("location", "").split(",")) == 3 else ""
            job_description = (
                    data.get("jobDescription", "") +
                    data.get("jobResponsibilities", "") +
                    data.get("jobRequirements", "") +
                    data.get("jobCompensationAndBenefits", "")
            )
            logging.info(f"Found tesla job {job_id} successfully")
            return {
                "jobId": job_id,
                "company": self.company_name,
                "cwiq_code": self.cwiq_code,
                "companyUrl": self.company_url,
                "country": city_to_country_map.get(area_id),
                "city": data.get("location", ""),
                "jobTitle": data.get("title", ""),
                "department": data.get("department", ""),
                "location": state,
                "jobDescription": job_description,
                "jobUrl": "https://www.tesla.com" + data.get("url", ""),
                "created_time": None,
                "updated_time": None,
                "post_time": datetime.now(),
            }

        except Exception as e:
            logging.error(f"Error fetching job details for {self.company_name}: {e}")
            return None

    def fetch_jobs(self, max_workers):
        """获取所有职位信息并显示进度"""
        try:
            response = self.make_request('GET', self.base_url, self.company_name, requset_count=3,)
            data = response.json()

            listings = data["listings"]
            logging.info(f"Found {len(listings)} job listings")

            city_to_country_map = self.build_city_to_country_map(data)

            jobs = {}
            total_jobs = len(listings)  # 总的职位数
            if total_jobs == 0:
                logging.info(f"No jobs found {self.company_name}")
            with ThreadPoolExecutor(max_workers=max_workers) as executor:
                futures = [
                    executor.submit(
                        self.fetch_job_details,
                        j["id"],
                        j["l"],
                        city_to_country_map,
                    )
                    for j in listings
                ]

                completed_jobs = 0  # 已完成的职位数
                for future in as_completed(futures):
                    job_data = future.result()
                    if job_data:
                        jobs[job_data["jobId"]] = job_data
                        completed_jobs += 1
                        # 打印进度
                        progress = (completed_jobs / total_jobs) * 100
                        logging.info(f"Progress: {completed_jobs}/{total_jobs} jobs fetched ({progress:.2f}%)")

            logging.info(f"Finished fetching all Tesla jobs.")
            return list(jobs.values()),"no total_jobs"

        except Exception as e:
            logging.error(f"Error fetching Tesla jobs: {e} for {self.company_name}")
            return [],0

    # def retry_request(self, method, url, company_name, retries=3, delay=5, **kwargs):
    #     attempt = 0
    #     while attempt < retries:
    #         try:
    #             result = self.make_request(method, url, company_name, **kwargs)
    #             data = result.json()
    #
    #             return data
    #         except Exception as e:
    #             attempt += 1
    #             logging.error(f"Error on attempt {attempt} for {company_name}: {e}")
    #             if attempt < retries:
    #                 logging.info(f"Retrying in {delay} seconds...")
    #             else:
    #                 logging.error(f"All {retries} attempts failed for {company_name}")
    #                 return None

    def __del__(self):
        """确保 HTTP 客户端被正确关闭"""
        if hasattr(self, 'client'):
            self.client.close()
