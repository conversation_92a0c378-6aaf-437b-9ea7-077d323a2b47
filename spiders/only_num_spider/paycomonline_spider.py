import json
import random
import time
from datetime import datetime
import logging
from urllib.parse import urlparse
from concurrent.futures import ThreadPoolExecutor, as_completed
from spiders.core_spider.base_spider import BaseSpider
from utils.country_utils import CountryCodeUtils
from utils.http_utils.http_client import HTTPClient
from bs4 import BeautifulSoup
import re
from lxml import etree
class PaycomonlineSpider(BaseSpider):
    def __init__(self, company_name, base_url, company_url, cwiq_code):
        super().__init__(company_name, base_url, company_url, cwiq_code)
        self.company_name = company_name
        self.company_url = company_url
        self.cwiq_code = cwiq_code
        self.country_utils = CountryCodeUtils()
        self.headers = {
    'Accept': '*/*',
    'Accept-Language': 'zh-CN,zh;q=0.9',
    'Cache-Control': 'no-cache',
    'Connection': 'keep-alive',
    'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
    'Origin': 'https://www.paycomonline.net',
    'Pragma': 'no-cache',
    'Referer': 'https://www.paycomonline.net/v4/ats/web.php/jobs?clientkey=E52B20C642EFD2F1B34D5B54FA07B56C',
    'Sec-Fetch-Dest': 'empty',
    'Sec-Fetch-Mode': 'cors',
    'Sec-Fetch-Site': 'same-origin',
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36',
    'X-Requested-With': 'XMLHttpRequest',
    'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"',
    'sec-ch-ua-mobile': '?0',
    'sec-ch-ua-platform': '"Windows"',
    # 'Cookie': 'clientkey=E52B20C642EFD2F1B34D5B54FA07B56C; TS01cfbc0e=014586c95aba6614669c5ea89306d2c3c4502a1a716a926cdb92a118bf194420fb362cbe3af9f1ca0d77523818c9102d93904b9b49; appv2ClientSess=kje58svbptuidn4dep9t8junur; pd_on=1; pd_weight=6; use_utf8=true; TS01a1b895=014586c95aba6614669c5ea89306d2c3c4502a1a716a926cdb92a118bf194420fb362cbe3af9f1ca0d77523818c9102d93904b9b49; cookie_secure_5min=!mNDR9hxUAQVZ3x8znTypU2FMc3kDxaVdfnFYBje79Rx282uYr3XeSMaRiSwz/3tPFvNBCshOSw==; Secure=!3mEg+7QG8LEVl4cznTypU2FMc3kDxd3vSi/tsy+PiO7gl26/DFDF/Yja0EPl8sj8CT4ScdnVaQ==',
}

        # 创建 HTTP 客户端
        self.client = HTTPClient()

    @classmethod
    def create_spider(cls, company_name, base_url, company_url, cwiq_code):
        """工厂方法创建 MedtronicSpider 实例"""
        return cls(company_name, base_url, company_url, cwiq_code)

    def make_request(self, method, url, company_name, requset_count, **kwargs):
        """发送请求的通用方法"""
        kwargs['headers'] = self.headers
        return self.client.request(method, url, company_name, requset_count, log_ip=False, **kwargs)

    def get_total_jobs(self):
        """获取总职位数"""
        try:
            params = {
                'partial': '1',
                'clientkey': 'E52B20C642EFD2F1B34D5B54FA07B56C',
            }

            data = {
                'clientkey': 'E52B20C642EFD2F1B34D5B54FA07B56C',
                'jpt': '',
                'selectedAllFilters': 'false',
                'sortListings': '',
                'jobCount': '10',
                'locationRadios': '0',
                'shareJobSearchSettingsByUrl': '',
            }
            response = self.make_request('POST', self.base_url, self.company_name, requset_count=3,params=params, data=data,)
            total = len(response.json()['jobs'])
            return total
        except Exception as e:
            logging.error(f"Error getting total jobs: {e} for {self.company_name}")
            return 0

    def parse_job(self, job_data):
        """解析职位数据"""
        pass

    def fetch_jobs(self,max_workers):
        """获取所有职位信息"""
        try:
            total_jobs = self.get_total_jobs()
            if total_jobs == 0:
                logging.info(f"No jobs found {self.company_name}")
                return [], 0

            logging.info(f"Found {total_jobs} total jobs")

            return [], total_jobs

        except Exception as e:
            logging.error(f"Error in fetch_jobs: {e}:{self.company_name}")
            return [], 0


    def __del__(self):
        """确保 HTTP 客户端被正确关闭"""
        if hasattr(self, 'client'):
            self.client.close()

