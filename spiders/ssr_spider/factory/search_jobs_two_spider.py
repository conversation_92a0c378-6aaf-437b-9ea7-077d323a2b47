import json
import random
import time
from datetime import datetime
import logging
from urllib.parse import urlparse
from concurrent.futures import ThreadPoolExecutor, as_completed
from spiders.core_spider.base_spider import BaseSpider
from utils.country_utils import CountryCodeUtils
from utils.http_utils.http_client import HTTPClient
from bs4 import BeautifulSoup
import re
def _simplify_job_data(job):
    """
    将原始职位数据简化为只包含必要信息的格式

    Args:
        job (str): 职位链接

    Returns:
        dict: 简化后的职位数据，只包含ID和路径信息
    """
    try:
        externalPath = job
        id = externalPath.split('/')[-1]
    except Exception as e:
        logging.error(f"Error parsing job data{e}: {job}")
        return {
            'id': None,  # REQ号作为ID
            'externalPath': None
        }
    return {
        'id': id,
        'externalPath': externalPath
    }
class SearchJobsTwoSpider(BaseSpider):
    def __init__(self, company_name, base_url, company_url, cwiq_code):
        super().__init__(company_name, base_url, company_url, cwiq_code)
        self.company_name = company_name
        self.company_url = company_url
        self.cwiq_code = cwiq_code
        self.country_utils = CountryCodeUtils()
        self.headers = {
            'Accept': '*/*',
            'Accept-Language': 'zh-CN,zh;q=0.9',
            'Connection': 'keep-alive',
            'Content-Type': 'application/json; charset=utf-8',
            'Sec-Fetch-Dest': 'empty',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Site': 'same-origin',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/134.0.0.0 Safari/537.36 Edg/134.0.0.0',
            'X-Requested-With': 'XMLHttpRequest',
            'sec-ch-ua': '"Chromium";v="134", "Not:A-Brand";v="24", "Microsoft Edge";v="134"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"Windows"',
        }
        parsed_url = urlparse(base_url)
        self.job_url_base = f'{parsed_url.scheme}://{parsed_url.netloc}'
        # 创建 HTTP 客户端
        self.client = HTTPClient()

    @classmethod
    def create_spider(cls, company_name, base_url, company_url, cwiq_code):
        """工厂方法创建 MedtronicSpider 实例"""
        return cls(company_name, base_url, company_url, cwiq_code)

    def make_request(self, method, url, company_name, requset_count, **kwargs):
        """发送请求的通用方法"""
        kwargs['headers'] = self.headers
        return self.client.request(method, url, company_name, requset_count, log_ip=False, **kwargs)

    def get_total_jobs(self):
        """获取总职位数"""
        params = {
            'ActiveFacetID': '0',
            'CurrentPage': '1',
            'RecordsPerPage': '500',
            'Distance': '50',
            'RadiusUnitType': '0',
            'ShowRadius': 'False',
            'IsPagination': 'False',
            'FacetType': '0',
            'SearchResultsModuleName': 'Search Results',
            'SearchFiltersModuleName': 'Search Filters',
            'SortCriteria': '0',
            'SortDirection': '0',
            'SearchType': '5',
            'PostalCode': '',
            'ResultsType': '0',
        }
        try:
            response = self.make_request('GET', self.base_url, self.company_name, requset_count=3, params=params)
            text = response.json()['results']
            soup = BeautifulSoup(text, 'html.parser')
            section_tag = soup.find('section', id='search-results')
            total_jobs = int(section_tag['data-total-job-results'])
            return total_jobs
        except Exception as e:
            logging.error(f"Error getting total jobs: {e}")
            return 0

    def parse_job(self, job_data):
        """解析职位数据"""
        try:
            if job_data:
                job_url = job_data.get("url", "")
                job_id = job_url.split('/')[-1]
                # 解析地点信息
                location = job_data.get("jobLocation", [])
                address = location[0].get("address", {}) if location else {}
                country = address.get("addressCountry", "") if address else ""
                country_code = self.country_utils.get_alpha3(country)
                city = address.get("addressLocality", "") if address else ""
                state = address.get("addressRegion", "") if address else ""
                return {
                    "jobId": job_id,
                    "company": self.company_name,
                    "cwiq_code": self.cwiq_code,
                    "companyUrl": self.company_url,
                    "country": country_code,
                    "city": city,
                    "jobTitle": job_data.get("title", ""),
                    # "department": job_info.get("hiringOrganization", {}).get("name", ""),
                    "location": state,
                    "jobDescription": job_data.get("description", ""),
                    "jobUrl": job_url,
                    "created_time": None,
                    "updated_time": None,
                    "post_time": datetime.now(),
                }
        except Exception as e:
            logging.error(f"Error parsing job data: {e} for {self.company_name}")
            return None

    def fetch_job_details(self, external_path):
        """获取职位详细信息"""
        try:
            time.sleep(random.uniform(0.5, 1))
            job_url = f"{self.job_url_base}{external_path}"
            response = self.make_request('GET', job_url, self.company_name, requset_count=3)

            soup = BeautifulSoup(response.text, 'html.parser')
            script_tag = soup.find('script', type='application/ld+json')
            if script_tag:
                script_text = script_tag.text.strip()
                # 将实际的制表符替换为转义序列 "\\t"
                script_text = script_text.replace("\t", "\\t")
                # 使用正则表达式移除所有控制字符
                script_text = re.sub(r'[\x00-\x1F]+', '', script_text)
                job_data = json.loads(script_text)
            else:
                country = ""
                city = ""
                state = ""
                span_tag = soup.find('span', class_='job-country job-info')
                if span_tag:
                    country = span_tag.find('b').next_sibling.strip()
                span_tag = soup.find('span', class_='job-city job-info')
                if span_tag:
                    city = span_tag.find('b').next_sibling.strip()
                span_tag = soup.find('span', class_='job-region job-info')
                if span_tag:
                    state = span_tag.find('b').next_sibling.strip()
                address = {
                    'addressCountry': country,
                    'addressLocality': city,
                    'addressRegion': state,
                }
                address1 = {
                    'address': address
                }
                location = []
                location.append(address1)
                div_tag = soup.find("div",class_="title-box")
                h1_tag = div_tag.find("h1")
                title = h1_tag.text.strip()
                div_tag = soup.find("div",class_="ats-description")
                description = div_tag.text.strip()
                job_data = {
                    'url': job_url,
                    'jobLocation': location,
                    'title': title,
                    'description': description,
                }
            return job_data
        except Exception as e:
            logging.error(f"Error fetching job details for {self.company_name}: {e}")
            return None

    def get_state_list(self):
        """获取state列表"""
        try:
            response = self.make_request('GET', self.company_url, self.company_name, requset_count=3)
            soup = BeautifulSoup(response.text, 'html.parser')
            section_tag = soup.find('section', {'data-filter-id': '3'})
            ul_tag = section_tag.find('ul', class_='search-filter-list')
            input_tags = ul_tag.find_all('input')
            state_list = []
            for input_tag in input_tags:
                state = {
                    'data-facet-type': input_tag['data-facet-type'],
                    'data-id': input_tag['data-id'],
                    'data-count': input_tag['data-count'],
                    'data-display': input_tag['data-display']
                }
                # 将该元组添加到列表中
                state_list.append(state)
            return state_list
        except Exception as e:
            logging.error(f"Error getting state list: {e} for {self.company_name}")
            return []

    def fetch_jobs(self,max_workers):
        """获取所有职位信息"""
        try:
            total_jobs = self.get_total_jobs()
            if total_jobs == 0:
                logging.info(f"No jobs found {self.company_name}")
                return [],0

            logging.info(f"Found {total_jobs} total jobs")

            # 获取上一批次的职位数据
            latest_batch = self.db_ops.get_latest_batch(self.company_url)
            batch_id = latest_batch.batchId if latest_batch else 0
            previous_job_ids = set(self.db_ops.get_job_ids_by_batch(
                self.company_url,
                batch_id,
                status=[0, 1]
            ))

            state_list = self.get_state_list()
            all_jobs = []

            with ThreadPoolExecutor(max_workers=max_workers) as executor:
                futures = []

                # 将每个任务提交给线程池
                for state in state_list:
                    futures.append(executor.submit(self._fetch_job_listings, state))

                # 等待所有任务完成并收集结果
                for future in as_completed(futures):
                    try:
                        jobs = future.result()
                        all_jobs.extend(jobs)
                        logging.info(f"Progress: {len(all_jobs)}/{total_jobs} jobs fetched")
                    except Exception as e:
                        logging.error(f"Error processing job state {self.company_name}: {e}")

            # 从现有职位中提取所有job_ids
            current_job_ids = {
                job.get('id', '')
                for job in all_jobs
            }

            # 找出重复的job_ids
            repeated_job_ids = current_job_ids.intersection(previous_job_ids)
            new_job_ids = current_job_ids - previous_job_ids

            # 一次性获取所有重复职位的数据
            repeated_jobs_dict = self.db_ops.get_jobs_by_ids(list(repeated_job_ids))

            # 分离新职位和重复职位
            new_jobs = [
                job for job in all_jobs
                if job.get('id', '') in new_job_ids
            ]
            repeated_jobs = list(repeated_jobs_dict.values())

            logging.info(f"Found {len(new_jobs)} new jobs and {len(repeated_jobs)} repeated jobs")

            # 只处理新职位的详细信息
            processed_jobs = []
            if new_jobs:
                with ThreadPoolExecutor(max_workers=max_workers) as executor:
                    future_to_job = {
                        executor.submit(self.fetch_job_details, job.get('externalPath', '')): job
                        for job in new_jobs
                    }

                    completed = 0
                    total_new = len(new_jobs)
                    for future in as_completed(future_to_job):
                        job_details = future.result()
                        if job_details:
                            parsed_job = self.parse_job(job_details)
                            if parsed_job:
                                processed_jobs.append(parsed_job)
                        completed += 1
                        progress = (completed / total_new) * 100
                        logging.info(f"Processing new job details: {completed}/{total_new} ({progress:.2f}%)")

            # 合并新处理的职位和重复的职位
            all_processed_jobs = processed_jobs + repeated_jobs
            logging.info(f"Successfully processed {len(all_processed_jobs)} total jobs")
            return all_processed_jobs,total_jobs

        except Exception as e:
            logging.error(f"Error in fetch_jobs: {e}:{self.company_name}")
            return [],0

    def _fetch_job_listings(self, state):
        """获取所有职位列表"""
        try:
            jobs_per_request = 500
            total_jobs = int(state['data-count'])
            total_requests = (total_jobs // jobs_per_request) + (1 if total_jobs % jobs_per_request else 0)
            all_jobs = []

            with ThreadPoolExecutor(max_workers=25) as executor:
                futures = []
                for i in range(total_requests):
                    params = {
                        'ActiveFacetID': state['data-id'],
                        'CurrentPage': i+1,
                        'RecordsPerPage': jobs_per_request,
                        'TotalContentResults': '',
                        'Distance': '50',
                        'RadiusUnitType': '0',
                        'Keywords': '',
                        'Location': '',
                        'ShowRadius': 'False',
                        'IsPagination': 'False',
                        'CustomFacetName': '',
                        'FacetTerm': '',
                        'FacetType': '0',
                        'FacetFilters[0].ID': state['data-id'],
                        'FacetFilters[0].FacetType': state['data-facet-type'],
                        'FacetFilters[0].Count': state['data-count'],
                        'FacetFilters[0].Display': state['data-display'],
                        'FacetFilters[0].IsApplied': 'true',
                        'FacetFilters[0].FieldName': '',
                        'SearchResultsModuleName': 'Search Results',
                        'SearchFiltersModuleName': 'Search Filters',
                        'SortCriteria': '0',
                        'SortDirection': '0',
                        'SearchType': '5',
                        'PostalCode': '',
                        'ResultsType': '0',
                        'fc': '',
                        'fl': '',
                        'fcf': '',
                        'afc': '',
                        'afl': '',
                        'afcf': '',
                        'TotalContentPages': 'NaN',
                    }
                    futures.append(
                        executor.submit(
                            self.make_request,
                            'GET',
                            self.base_url,
                            self.company_name,
                            requset_count=3,
                            params=params
                        )
                    )

                for future in as_completed(futures):
                    try:
                        response = future.result()
                        jobs = []

                        text = response.json()['results']
                        soup = BeautifulSoup(text, 'html.parser')
                        list_tag = soup.find(id='search-results-list')
                        li_tags = list_tag.find_all('li', class_='branded-list__list-item')
                        for li_tag in li_tags:
                            a_tag = li_tag.find('a')
                            externalPath = a_tag['href']
                            jobs.append(externalPath)

                        # 使用新函数处理每个职位数据
                        simplified_jobs = [_simplify_job_data(job) for job in jobs]
                        all_jobs.extend(simplified_jobs)
                    except Exception as e:
                        logging.error(f"Error processing job batch: {e}:{self.company_name}")

            return all_jobs
        except Exception as e:
            logging.error(f"Error fetching jobs for {self.company_name}: {e}")


    def __del__(self):
        """确保 HTTP 客户端被正确关闭"""
        if hasattr(self, 'client'):
            self.client.close()
