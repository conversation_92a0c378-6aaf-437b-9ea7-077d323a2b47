#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Marzaha 爬虫项目安装配置
"""

from setuptools import setup, find_packages
import os

# 读取README文件
def read_readme():
    readme_path = os.path.join(os.path.dirname(__file__), 'README.md')
    if os.path.exists(readme_path):
        with open(readme_path, 'r', encoding='utf-8') as f:
            return f.read()
    return "Marzaha - 企业级分布式爬虫系统"

# 读取requirements文件
def read_requirements(filename):
    requirements_path = os.path.join(os.path.dirname(__file__), filename)
    if os.path.exists(requirements_path):
        with open(requirements_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()
            # 过滤掉注释和空行
            requirements = []
            for line in lines:
                line = line.strip()
                if line and not line.startswith('#') and not line.startswith('-r'):
                    requirements.append(line)
            return requirements
    return []

setup(
    name="marzaha",
    version="1.0.0",
    description="企业级分布式爬虫系统，专门用于抓取招聘信息",
    long_description=read_readme(),
    long_description_content_type="text/markdown",
    author="Marzaha Team",
    author_email="<EMAIL>",
    url="https://github.com/marzaha/marzaha",
    
    # 包配置
    packages=find_packages(),
    include_package_data=True,
    
    # Python版本要求
    python_requires=">=3.8,<4.0",
    
    # 依赖包
    install_requires=read_requirements('requirements-prod.txt'),
    
    # 额外依赖
    extras_require={
        'dev': read_requirements('requirements-dev.txt'),
        'full': read_requirements('requirements.txt'),
    },
    
    # 分类信息
    classifiers=[
        "Development Status :: 5 - Production/Stable",
        "Intended Audience :: Developers",
        "License :: OSI Approved :: MIT License",
        "Operating System :: OS Independent",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Topic :: Internet :: WWW/HTTP :: Indexing/Search",
        "Topic :: Software Development :: Libraries :: Python Modules",
    ],
    
    # 关键词
    keywords="spider crawler jobs recruitment scraping distributed",
    
    # 项目URL
    project_urls={
        "Bug Reports": "https://github.com/marzaha/marzaha/issues",
        "Source": "https://github.com/marzaha/marzaha",
        "Documentation": "https://marzaha.readthedocs.io/",
    },
    
    # 命令行入口
    entry_points={
        'console_scripts': [
            'marzaha=spider:main',
            'marzaha-scheduler=spider:run_scheduler',
        ],
    },
    
    # 包数据
    package_data={
        'config': ['*.yaml', '*.json'],
        'models': ['Sql/*.sql'],
    },
    
    # 数据文件
    data_files=[
        ('config', ['config/spiders.yaml', 'config/gcp.json']),
    ],
    
    # 许可证
    license="MIT",
    
    # ZIP安全
    zip_safe=False,
)
