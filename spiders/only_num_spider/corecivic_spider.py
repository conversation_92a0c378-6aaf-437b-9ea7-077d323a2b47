import json
import random
import time
from datetime import datetime
import logging
from urllib.parse import urlparse
from concurrent.futures import ThreadPoolExecutor, as_completed
from spiders.core_spider.base_spider import BaseSpider
from utils.country_utils import CountryCodeUtils
from utils.http_utils.http_client import HTTPClient
from bs4 import BeautifulSoup
import re
from lxml import etree
class CorecivicSpider(BaseSpider):
    def __init__(self, company_name, base_url, company_url, cwiq_code):
        super().__init__(company_name, base_url, company_url, cwiq_code)
        self.company_name = company_name
        self.company_url = company_url
        self.cwiq_code = cwiq_code
        self.country_utils = CountryCodeUtils()
        self.headers = {
    'accept': '*/*',
    'accept-language': 'zh-CN,zh;q=0.9',
    'cache-control': 'no-cache',
    'content-type': 'application/json',
    'origin': 'https://jobs.corecivic.com',
    'pragma': 'no-cache',
    'priority': 'u=1, i',
    'referer': 'https://jobs.corecivic.com/us/en/search-results',
    'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"',
    'sec-ch-ua-mobile': '?0',
    'sec-ch-ua-platform': '"Windows"',
    'sec-fetch-dest': 'empty',
    'sec-fetch-mode': 'cors',
    'sec-fetch-site': 'same-origin',
    'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36',
    'x-csrf-token': '26ce9c383e1f4ded8017b8056f402d04',
    # 'cookie': 'PLAY_SESSION=eyJhbGciOiJIUzI1NiJ9.eyJkYXRhIjp7IkpTRVNTSU9OSUQiOiJjYTE5Y2EyNS00MGJhLTQ1MGQtYmVhOS04MDZjNzVlZTY1OGEiLCJjc3JmVG9rZW4iOiIyNmNlOWMzODNlMWY0ZGVkODAxN2I4MDU2ZjQwMmQwNCJ9LCJuYmYiOjE3NDgyNTQ4OTAsImlhdCI6MTc0ODI1NDg5MH0.2dwzpB_YgIhp11x5qVviAgGUiGy99gMyLMe6kZgKqD4; PHPPPE_ACT=ca19ca25-40ba-450d-bea9-806c75ee658a; VISITED_LANG=en; VISITED_COUNTRY=us; Per_UniqueID=1970c1cb62b1631-384000-6caf-1970c1cb62cdb9; ext_trk=pjid%3Dca19ca25-40ba-450d-bea9-806c75ee658a&uid%3D1970c1cb62b1631-384000-6caf-1970c1cb62cdb9&p_lang%3Den_us&refNum%3DCCRCUS; PHPPPE_GCC=a; PHPPPE_NPS=a',
}

        # 创建 HTTP 客户端
        self.client = HTTPClient()

    @classmethod
    def create_spider(cls, company_name, base_url, company_url, cwiq_code):
        """工厂方法创建 MedtronicSpider 实例"""
        return cls(company_name, base_url, company_url, cwiq_code)

    def make_request(self, method, url, company_name, requset_count, **kwargs):
        """发送请求的通用方法"""
        kwargs['headers'] = self.headers
        return self.client.request(method, url, company_name, requset_count, log_ip=False, **kwargs)

    def get_total_jobs(self):
        """获取总职位数"""
        try:
            json_data = {
                'lang': 'en_us',
                'deviceType': 'desktop',
                'country': 'us',
                'pageName': 'search-results',
                'ddoKey': 'refineSearch',
                'sortBy': 'Most recent',
                'subsearch': '',
                'from': 0,
                'jobs': True,
                'counts': True,
                'all_fields': [
                    'category',
                    'facilityName',
                    'state',
                    'type',
                    'city',
                    'country',
                ],
                'size': 10,
                'clearAll': False,
                'jdsource': 'facets',
                'isSliderEnable': False,
                'pageId': 'page25',
                'siteType': 'external',
                'keywords': '',
                'global': True,
                'selected_fields': {},
                'sort': {
                    'order': 'desc',
                    'field': 'postedDate',
                },
                'locationData': {},
            }

            response = self.make_request('POST', self.base_url, self.company_name, requset_count=3,json=json_data)
            total = response.json()['refineSearch']['totalHits']
            return total
        except Exception as e:
            logging.error(f"Error getting total jobs: {e} for {self.company_name}")
            return 0

    def parse_job(self, job_data):
        """解析职位数据"""
        pass

    def fetch_jobs(self,max_workers):
        """获取所有职位信息"""
        try:
            total_jobs = self.get_total_jobs()
            if total_jobs == 0:
                logging.info(f"No jobs found {self.company_name}")
                return [], 0

            logging.info(f"Found {total_jobs} total jobs")

            return [], total_jobs

        except Exception as e:
            logging.error(f"Error in fetch_jobs: {e}:{self.company_name}")
            return [], 0


    def __del__(self):
        """确保 HTTP 客户端被正确关闭"""
        if hasattr(self, 'client'):
            self.client.close()

