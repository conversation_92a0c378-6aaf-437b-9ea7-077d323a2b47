import json
import random
import time
from datetime import datetime
import logging
from urllib.parse import urlparse
from concurrent.futures import ThreadPoolExecutor, as_completed
from spiders.core_spider.base_spider import BaseSpider
from utils.country_utils import CountryCodeUtils
from utils.http_utils.http_client import HTTPClient
from bs4 import BeautifulSoup
import re
from lxml import etree
class <PERSON><PERSON><PERSON><PERSON>er(BaseSpider):
    def __init__(self, company_name, base_url, company_url, cwiq_code):
        super().__init__(company_name, base_url, company_url, cwiq_code)
        self.company_name = company_name
        self.company_url = company_url
        self.cwiq_code = cwiq_code
        self.country_utils = CountryCodeUtils()
        self.headers = {
    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
    'Accept-Language': 'zh-CN,zh;q=0.9',
    'Cache-Control': 'max-age=0',
    'Connection': 'keep-alive',
    'Referer': 'https://jobs.aarons.com/job/houston/customer-accounts-advisor/1618/***********',
    'Sec-Fetch-Dest': 'document',
    'Sec-Fetch-Mode': 'navigate',
    'Sec-Fetch-Site': 'same-origin',
    'Sec-Fetch-User': '?1',
    'Upgrade-Insecure-Requests': '1',
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36',
    'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"',
    'sec-ch-ua-mobile': '?0',
    'sec-ch-ua-platform': '"Windows"',
    # 'Cookie': "CookieBehavior=US|Implied; SearchVisitorId=9e70f424-7787-4cde-50b6-ef9f2602f683; SearchSessionId={%22SearchSessionId%22:%2264a09d05-9be8-c39f-0bde-4ceac3c18719%22%2C%22ImpressionParentId%22:%22%22%2C%22ViewParentId%22:%22%22%2C%22GoogleSearchRequestId%22:%22%22%2C%22GoogleJobId%22:%22%22%2C%22Created%22:%***************%22}; CookieConsent=0|1|2|3; PersonalizationCookie=[{%22Locations%22:[{%22Path%22:%*********-5417618-5424092-5411311%22%2C%22FacetType%22:4%2C%22GeolocationLatitude%22:38.77749%2C%22GeolocationLongitude%22:-106.49225%2C%22LocationName%22:%22Abbeyville%252C%2520CO%22%2C%22GeoType%22:%22ip2ifnohtml5%22%2C%22SetByHtml5%22:true}]%2C%22Categories%22:[]%2C%22PersonalizationType%22:0%2C%22DateCreated%22:%222025-05-16T06:19:59.331Z%22%2C%22CustomFacets%22:[]%2C%22TenantId%22:9%2C%22OnetCode%22:null%2C%22Served%22:false}%2C{%22Locations%22:[{%22Path%22:%*********%22%2C%22FacetType%22:2}]%2C%22Categories%22:[%2270745%22]%2C%22PersonalizationType%22:1%2C%22DateCreated%22:%222025-05-16T06:19:59.331Z%22%2C%22CustomFacets%22:[{%22CustomFacetValue%22:%22Aaron's%20Family%20of%20Companies%22%2C%22CustomFacetTerm%22:%22company_name%22}]%2C%22TenantId%22:9}]",
}

        # 创建 HTTP 客户端
        self.client = HTTPClient()

    @classmethod
    def create_spider(cls, company_name, base_url, company_url, cwiq_code):
        """工厂方法创建 MedtronicSpider 实例"""
        return cls(company_name, base_url, company_url, cwiq_code)

    def make_request(self, method, url, company_name, requset_count, **kwargs):
        """发送请求的通用方法"""
        kwargs['headers'] = self.headers
        return self.client.request(method, url, company_name, requset_count, log_ip=False, **kwargs)

    def get_total_jobs(self):
        """获取总职位数"""
        try:
            response = self.make_request('GET', self.base_url, self.company_name, requset_count=3)
            tree = etree.HTML(response.text)
            total = tree.xpath(
                "//section[@class='search-results-filters']/div[@class='search-container']/div[@class='item'][1]/section[@id='search-results']/h1[@class='total-count']//text()")[
                0]
            result = re.findall(r'\d+', total)
            total = int(result[0])
            return total
        except Exception as e:
            logging.error(f"Error getting total jobs: {e} for {self.company_name}")
            return 0

    def parse_job(self, job_data):
        """解析职位数据"""
        pass

    def fetch_jobs(self,max_workers):
        """获取所有职位信息"""
        try:
            total_jobs = self.get_total_jobs()
            if total_jobs == 0:
                logging.info(f"No jobs found {self.company_name}")
                return [], 0

            logging.info(f"Found {total_jobs} total jobs")

            return [], total_jobs

        except Exception as e:
            logging.error(f"Error in fetch_jobs: {e}:{self.company_name}")
            return [], 0


    def __del__(self):
        """确保 HTTP 客户端被正确关闭"""
        if hasattr(self, 'client'):
            self.client.close()

