from spiders.core_spider.base_spider import BaseSpider
from utils.country_utils import Country<PERSON>odeUtils
import logging
from datetime import datetime
from concurrent.futures import ThreadPoolExecutor, as_completed

from utils.http_utils.http_client import HTTPClient

class BadcockSpider(BaseSpider):
    def __init__(self, company_name, base_url, company_url, cwiq_code):
        super().__init__(company_name, base_url, company_url, cwiq_code)
        self.company_name = company_name
        self.company_url = company_url
        self.cwiq_code = cwiq_code
        self.country_utils = CountryCodeUtils()

        self.headers = {
    'accept': 'application/json, text/plain, */*',
    'accept-language': 'zh-CN,zh;q=0.9',
    'priority': 'u=1, i',
    'referer': 'https://careers.badcock.com/jobs',
    'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"',
    'sec-ch-ua-mobile': '?0',
    'sec-ch-ua-platform': '"Windows"',
    'sec-fetch-dest': 'empty',
    'sec-fetch-mode': 'cors',
    'sec-fetch-site': 'same-origin',
    'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36',
    # 'cookie': 'i18n=en-US; searchSource=external; jrasession=20c28efb-26db-40e4-912c-b88b6a000603; jasession=s%3AGMKF6unOG0ZBdIALiSVr65PgZkmRS7Wq.Qx82dJmXFiqgQWNd%2B5M1n109gEbC%2BT63yLk1Ib5WYoM; _janalytics_ses.da2c=*; _janalytics_id.da2c=069f9ca9-ca43-4929-bda9-928075e46713.1748057551.1.1748057566.1748057551.9ae04a14-7f03-401e-9d96-abc3c2c96684',
}

        self.clientNamespace = self.company_url.split('/')[-2]
        self.jobBoardCode = self.company_url.split('/')[-1]
        self.cultureCode = self.company_url.split('/')[-3]

        # 创建 HTTP 客户端，使用全局代理配置
        self.client = HTTPClient()

    @classmethod
    def create_spider(cls, company_name, base_url, company_url, cwiq_code):
        """工厂方法创建 MedtronicSpider 实例"""
        return cls(company_name, base_url, company_url, cwiq_code)

    def make_request(self, method, url,company_name, requset_count, **kwargs):
        """发送请求的通用方法"""
        kwargs['headers'] = self.headers
        return self.client.request(method, url, company_name, requset_count, log_ip=False, **kwargs)

    def get_total_jobs(self):
        """获取总工作数"""
        try:
            cookies = {
                'i18n': 'en-US',
                'searchSource': 'external',
                'jrasession': '20c28efb-26db-40e4-912c-b88b6a000603',
                'jasession': 's%3AGMKF6unOG0ZBdIALiSVr65PgZkmRS7Wq.Qx82dJmXFiqgQWNd%2B5M1n109gEbC%2BT63yLk1Ib5WYoM',
                '_janalytics_ses.da2c': '*',
                '_janalytics_id.da2c': '069f9ca9-ca43-4929-bda9-928075e46713.1748057551.1.1748057566.1748057551.9ae04a14-7f03-401e-9d96-abc3c2c96684',
            }
            params = {
                'page': '1',
                'sortBy': 'relevance',
                'descending': 'false',
                'internal': 'false',
            }
            response = self.make_request('POST', self.base_url, self.company_name, requset_count=3, params=params,cookies=cookies)
            return response.json()['totalCount']
        except Exception as e:
            logging.error(f"Error getting total jobs: {e} for {self.company_name}")
            return 0

    def parse_job(self, job_data):
        """解析职位数据"""
        try:
            if job_data:
                job_id = str(job_data.get("jobPostingId", ""))
                # 解析地点信息
                location = job_data.get("postingLocations", [])
                if location:
                    location = location[0]
                    country = location.get("isoCountryCode", "")
                    city = location.get("cityName", "")
                    state = location.get("stateCode", "")
                else:
                    country = ""
                    city = ""
                    state = ""

                country_code = self.country_utils.get_alpha3(country)
                return {
                    "jobId": job_id,
                    "company": self.company_name,
                    "cwiq_code": self.cwiq_code,
                    "companyUrl": self.company_url,
                    "country": country_code,
                    "city": city,
                    "jobTitle": job_data.get("jobTitle", ""),
                    # "department": job_info.get("hiringOrganization", {}).get("name", ""),
                    "location": state,
                    "jobDescription": job_data.get("jobDescription", ""),
                    "jobUrl": self.company_url + "/jobs/" + job_id,
                    "created_time": None,
                    "updated_time": None,
                    "post_time": datetime.now(),
                }
        except Exception as e:
            logging.error(f"Error parsing job data: {e} for {self.company_name}")
            return None

    def fetch_jobs(self,max_workers):
        """获取所有职位信息"""
        try:
            # 获取工作总数量
            total_jobs = self.get_total_jobs()
            if total_jobs == 0:
                logging.info(f"No jobs found {self.company_name}")
                return [],0

            logging.info(f"Found {total_jobs} total jobs")

            # 遍历每一页
            jobs_per_request = 25
            total_requests = (total_jobs // jobs_per_request) + (1 if total_jobs % jobs_per_request else 0)
            all_jobs = []
            processed_jobs = []

            with ThreadPoolExecutor(max_workers=25) as executor:
                futures = []
                # 提交所有请求任务
                for i in range(total_requests):
                    json_payload = {
                        'clientNamespace': self.clientNamespace,
                        'jobBoardCode': self.jobBoardCode,
                        'cultureCode': self.cultureCode,
                        'distanceUnit': 0,
                        'paginationStart': i * jobs_per_request,
                    }
                    futures.append(
                        executor.submit(
                            self.make_request,
                            'POST',
                            self.base_url,
                            self.company_name,
                            requset_count=3,
                            json=json_payload
                        )
                    )

                # 处理所有任务的结果
                for future in as_completed(futures):
                    try:
                        response = future.result()
                        jobs = response.json().get('jobPostings', [])
                        all_jobs.extend(jobs)

                        # 解析每条数据
                        for job in jobs:
                            parsed_job = self.parse_job(job)
                            if parsed_job:
                                processed_jobs.append(parsed_job)

                        # 显示进度
                        if total_jobs > 0:  # 避免除以零
                            progress = (len(all_jobs) / total_jobs) * 100
                            logging.info(f"Progress: {len(all_jobs)}/{total_jobs} jobs fetched ({progress:.2f}%)")

                    except Exception as e:
                        logging.error(f"Error processing job batch for {self.company_name}: {e}")

            logging.info(f"Successfully fetched {len(processed_jobs)} jobs")
            return processed_jobs,total_jobs

        except Exception as e:
            logging.error(f"Error in fetch_jobs: {e}:{self.company_name}")
            return [],0


def __del__(self):
        """确保 HTTP 客户端被正确关闭"""
        if hasattr(self, 'client'):
            self.client.close()
