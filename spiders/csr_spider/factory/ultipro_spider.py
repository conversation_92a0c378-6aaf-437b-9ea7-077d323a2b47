from spiders.core_spider.base_spider import BaseSpider
from utils.country_utils import CountryCodeUtils
import logging
from datetime import datetime

from utils.http_utils.http_client import HTTPClient


# ultipro的模板方法
class UltiproSpider(BaseSpider):
    def __init__(self, company_name, base_url, company_url, cwiq_code):
        super().__init__(company_name, base_url, company_url, cwiq_code)
        self.company_name = company_name
        self.company_url = company_url
        self.cwiq_code = cwiq_code
        self.country_utils = CountryCodeUtils()
        self.headers = {
            "Content-Type": "application/json; charset=UTF-8",
            "Accept": "application/json, text/javascript, */*; q=0.01",
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
        }
        self.parsed_url = self.base_url.rsplit('/', 2)[0]
        self.apply_base_url = self.parsed_url + "/OpportunityDetail?opportunityId="
        # 创建 HTTP 客户端，使用全局代理配置
        self.client = HTTPClient()

    @classmethod
    def create_spider(cls, company_name, base_url, company_url, cwiq_code):
        """工厂方法创建 MedtronicSpider 实例"""
        return cls(company_name, base_url, company_url, cwiq_code)

    def make_request(self, method, url, company_name, requset_count,**kwargs):
        """发送请求的通用方法"""
        kwargs['headers'] = self.headers
        return self.client.request(method, url, company_name, requset_count,log_ip=False, **kwargs)

    def get_total_jobs(self):
        """获取总职位数"""
        try:
            payload = {
                "opportunitySearch": {
                    "Top": 50,
                    "Skip": 0,
                    "QueryString": "",
                    "OrderBy": [
                        {
                            "Value": "postedDateDesc",
                            "PropertyName": "PostedDate",
                            "Ascending": False
                        }
                    ],
                    "Filters": [
                        {"t": "TermsSearchFilterDto", "fieldName": 4, "extra": None, "values": []},
                        {"t": "TermsSearchFilterDto", "fieldName": 5, "extra": None, "values": []},
                        {"t": "TermsSearchFilterDto", "fieldName": 6, "extra": None, "values": []},
                        {"t": "TermsSearchFilterDto", "fieldName": 37, "extra": None, "values": []}
                    ]
                },
                "matchCriteria": {
                    "PreferredJobs": [],
                    "Educations": [],
                    "LicenseAndCertifications": [],
                    "Skills": [],
                    "hasNoLicenses": False,
                    "SkippedSkills": []
                }
            }
            response = self.make_request('POST', self.base_url, self.company_name,requset_count=3, json=payload)
            return response.json()['totalCount']
        except Exception as e:
            logging.error(f"Error getting total jobs: {e} for {self.company_name}")
            return 0

    def parse_job(self, job_data):
        """解析职位数据"""
        try:
            if job_data:
                job_id = job_data.get("Id", "")
                # 解析地点信息
                location = job_data.get("Locations", [])
                address = location[0].get("Address", {})
                country = address.get("Country", {})
                country_name = country.get("Name", "") if country else ""
                country_code = self.country_utils.get_alpha3(country_name)
                city = address.get("City", "") if address else ""
                state = address.get("State", {})
                state_name = state.get("Name", "") if state else ""
                return {
                    "jobId": job_id,
                    "company": self.company_name,
                    "cwiq_code": self.cwiq_code,
                    "companyUrl": self.company_url,
                    "country": country_code,
                    "city": city,
                    "jobTitle": job_data.get("Title", ""),
                    # "department": job_info.get("hiringOrganization", {}).get("name", ""),
                    "location": state_name,
                    "jobDescription": job_data.get("BriefDescription", ""),
                    "jobUrl": self.apply_base_url + job_id,
                    "created_time": None,
                    "updated_time": None,
                    "post_time": datetime.now(),
                }
        except Exception as e:
            logging.error(f"Error parsing job data: {e} for {self.company_name}")
            return None

    def fetch_jobs(self, max_workers):
        """获取所有职位信息"""
        try:
            # 获取工作总数量
            total_jobs = self.get_total_jobs()
            if total_jobs == 0:
                logging.info(f"No jobs found {self.company_name}")
                return [],0

            logging.info(f"Found {total_jobs} total jobs")

            # 遍历每一页
            jobs_per_request = 50
            total_requests = (total_jobs // jobs_per_request) + (1 if total_jobs % jobs_per_request else 0)
            all_jobs = []
            processed_jobs = []
            failed_requests_list = []
            fail_jobs = []

            for i in range(total_requests):
                payload = {
                    "opportunitySearch": {
                        "Top": jobs_per_request,
                        "Skip": i * jobs_per_request,
                        "QueryString": "",
                        "OrderBy": [
                            {
                                "Value": "postedDateDesc",
                                "PropertyName": "PostedDate",
                                "Ascending": False
                            }
                        ],
                        "Filters": [
                            {"t": "TermsSearchFilterDto", "fieldName": 4, "extra": None, "values": []},
                            {"t": "TermsSearchFilterDto", "fieldName": 5, "extra": None, "values": []},
                            {"t": "TermsSearchFilterDto", "fieldName": 6, "extra": None, "values": []},
                            {"t": "TermsSearchFilterDto", "fieldName": 37, "extra": None, "values": []}
                        ]
                    },
                    "matchCriteria": {
                        "PreferredJobs": [],
                        "Educations": [],
                        "LicenseAndCertifications": [],
                        "Skills": [],
                        "hasNoLicenses": False,
                        "SkippedSkills": []
                    }
                }

                # 发送请求
                try:
                    response = self.make_request('POST', self.base_url, self.company_name,requset_count=3, json=payload)
                    jobs = response.json().get('opportunities', [])
                    all_jobs.extend(jobs)

                    # 解析每条数据
                    for job in jobs:
                        parsed_job = self.parse_job(job)
                        if parsed_job:
                            processed_jobs.append(parsed_job)

                    # 显示进度
                    if total_jobs > 0:  # 避免除以零
                        progress = (len(all_jobs) / total_jobs) * 100
                        logging.info(f"Progress: {len(all_jobs)}/{total_jobs} jobs fetched ({progress:.2f}%)")

                except Exception as e:
                    logging.error(f"Error processing job batch ：{self.company_name}{i + 1}: {e}")
                    failed_requests_list.append({
                        "method": 'POST',
                        "url": self.base_url,
                        "company_name": self.company_name,
                        "payload": payload,
                    })

            if failed_requests_list:
                for failed_request in failed_requests_list:
                    fail_jobs = self.retry_request(failed_request['method'], failed_request['url'],
                                                   failed_request['company_name'], retries=3, delay=5,
                                                   json=failed_request['payload'])

            processed_jobs = processed_jobs + fail_jobs
            logging.info(f"Successfully fetched {len(processed_jobs)} Medtronic jobs")
            return processed_jobs,total_jobs

        except Exception as e:
            logging.error(f"Error in fetch_jobs: {e}:{self.company_name}")
            return [],0

    def retry_request(self, method, url, company_name, retries=3, delay=5, **kwargs):
        attempt = 0
        fail_jobs = []

        while attempt < retries:
            try:
                response = self.make_request(method, url, company_name,requset_count=1, **kwargs)
                jobs = response.json().get('opportunities', [])

                # 解析每条数据
                for job in jobs:
                    parsed_job = self.parse_job(job)
                    if parsed_job:
                        fail_jobs.append(parsed_job)
                return fail_jobs
            except Exception as e:
                attempt += 1
                logging.error(f"Error on attempt {attempt} for {company_name}: {e}")
                if attempt < retries:
                    logging.info(f"Retrying in {delay} seconds...")
                else:
                    logging.error(f"All {retries} attempts failed for {company_name}")
                    return None

    def __del__(self):
        """确保 HTTP 客户端被正确关闭"""
        if hasattr(self, 'client'):
            self.client.close()
