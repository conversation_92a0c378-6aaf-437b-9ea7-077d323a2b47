
import re

from spiders.core_spider.base_spider import BaseSpider
from utils.country_utils import CountryCodeUtils
import logging
from datetime import datetime
from lxml import etree
from utils.http_utils.http_client import HTTPClient


# ultipro的模板方法
class BjsrestaurantsSpider(BaseSpider):
    def __init__(self, company_name, base_url, company_url, cwiq_code):
        super().__init__(company_name, base_url, company_url, cwiq_code)
        self.company_name = company_name
        self.company_url = company_url
        self.cwiq_code = cwiq_code
        self.country_utils = CountryCodeUtils()
        self.headers = {
    'accept': 'application/json, text/plain, */*',
    'accept-language': 'zh-CN,zh;q=0.9',
    'priority': 'u=1, i',
    'referer': 'https://careers.bjsrestaurants.com/jobs',
    'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"',
    'sec-ch-ua-mobile': '?0',
    'sec-ch-ua-platform': '"Windows"',
    'sec-fetch-dest': 'empty',
    'sec-fetch-mode': 'cors',
    'sec-fetch-site': 'same-origin',
    'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    # 'cookie': 'i18n=en-US; searchSource=external; jrasession=b040a651-1b41-40c7-b622-93c5e8e656c0; jasession=s%3AYz6vCx4gyUOY0EXhyAI3txKTRvXmPBqr.U1IJ8ppTKv8%2FdFnSBVeaP04Mlo358BazUKlQPRx%2FVvw; _janalytics_ses.29e2=*; pixel_consent=%7B%22cookie%22%3A%22pixel_consent%22%2C%22type%22%3A%22cookie_notice%22%2C%22value%22%3Atrue%2C%22timestamp%22%3A%222025-05-22T03%3A36%3A43.242Z%22%7D; _janalytics_id.29e2=963b506b-1fc1-4a61-98af-c4f959a99597.1747884990.1.1747885033.1747884990.7bb8169a-066e-4ace-b8d1-97be4f366723',
}
        # 创建 HTTP 客户端，使用全局代理配置
        self.client = HTTPClient()

    @classmethod
    def create_spider(cls, company_name, base_url, company_url, cwiq_code):
        """工厂方法创建 MedtronicSpider 实例"""
        return cls(company_name, base_url, company_url, cwiq_code)

    def make_request(self, method, url, company_name, requset_count,**kwargs):
        """发送请求的通用方法"""
        kwargs['headers'] = self.headers
        return self.client.request(method, url, company_name, requset_count,log_ip=False, **kwargs)

    def get_total_jobs(self):
        """获取总职位数"""
        try:
            params = {
                'page': '1',
                'sortBy': 'relevance',
                'descending': 'false',
                'internal': 'false',
            }
            cookies = {
                'i18n': 'en-US',
                'searchSource': 'external',
                'jrasession': 'b040a651-1b41-40c7-b622-93c5e8e656c0',
                'jasession': 's%3AYz6vCx4gyUOY0EXhyAI3txKTRvXmPBqr.U1IJ8ppTKv8%2FdFnSBVeaP04Mlo358BazUKlQPRx%2FVvw',
                '_janalytics_ses.29e2': '*',
                'pixel_consent': '%7B%22cookie%22%3A%22pixel_consent%22%2C%22type%22%3A%22cookie_notice%22%2C%22value%22%3Atrue%2C%22timestamp%22%3A%222025-05-22T03%3A36%3A43.242Z%22%7D',
                '_janalytics_id.29e2': '963b506b-1fc1-4a61-98af-c4f959a99597.1747884990.1.1747885033.1747884990.7bb8169a-066e-4ace-b8d1-97be4f366723',
            }
            response = self.make_request('GET', self.base_url, self.company_name,requset_count=3,params=params,cookies=cookies)
            total = response.json()['totalCount']
            return total
        except Exception as e:
            logging.error(f"Error getting total jobs: {e} for {self.company_name}")
            return 0

    def parse_job(self, job_data):
        """解析职位数据"""
        pass

    def fetch_jobs(self, max_workers):
        """获取所有职位信息"""
        try:
            total_jobs = self.get_total_jobs()
            if total_jobs == 0:
                logging.info(f"No jobs found {self.company_name}")
                return [], 0

            logging.info(f"Found {total_jobs} total jobs")
            return [], total_jobs
        except Exception as e:
            logging.error(f"Error in fetch_jobs: {e}:{self.company_name}")
            return [], 0

    def __del__(self):
        """确保 HTTP 客户端被正确关闭"""
        if hasattr(self, 'client'):
            self.client.close()
