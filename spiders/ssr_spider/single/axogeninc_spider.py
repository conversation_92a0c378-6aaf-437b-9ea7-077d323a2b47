import json
import random
import time
from datetime import datetime
import logging
from urllib.parse import urlparse
from concurrent.futures import ThreadPoolExecutor, as_completed
from spiders.core_spider.base_spider import BaseSpider
from utils.country_utils import CountryCodeUtils
from utils.http_utils.http_client import HTTPClient
from bs4 import BeautifulSoup
import re
from lxml import etree

class AxogenincSpider(BaseSpider):
    def __init__(self, company_name, base_url, company_url, cwiq_code):
        super().__init__(company_name, base_url, company_url, cwiq_code)
        self.company_name = company_name
        self.company_url = company_url
        self.cwiq_code = cwiq_code
        self.country_utils = CountryCodeUtils()
        self.headers = {
            'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
            'accept-language': 'zh-CN,zh;q=0.9',
            'cache-control': 'no-cache',
            'pragma': 'no-cache',
            'priority': 'u=0, i',
            'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"Windows"',
            'sec-fetch-dest': 'iframe',
            'sec-fetch-mode': 'navigate',
            'sec-fetch-site': 'cross-site',
            'sec-fetch-storage-access': 'active',
            'upgrade-insecure-requests': '1',
            'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36',
        }
        # 创建 HTTP 客户端
        self.client = HTTPClient()

    @classmethod
    def create_spider(cls, company_name, base_url, company_url, cwiq_code):
        """工厂方法创建 MedtronicSpider 实例"""
        return cls(company_name, base_url, company_url, cwiq_code)

    def make_request(self, method, url, company_name,requset_count, **kwargs):
        """发送请求的通用方法"""
        kwargs['headers'] = self.headers
        return self.client.request(method, url, company_name, requset_count,log_ip=False, **kwargs)

    def get_total_jobs(self):
        try:
            params = {
                'for': 'axogen',
            }
            if self.company_name == "CLOVER HEALTH INVESTMENTS CO":
                params = {
                    'for': 'cloverhealth',
                    'error': 'true',
                }
            if self.company_name == "FUBOTV INC":
                params = {
                    'for': 'fubotv',
                    'error': 'true',
                }
            if self.company_name == "COMPASS PATHWAYS PLC":
                params = {
                    'for': 'compasspathways',
                    'error': 'true',
                }
            if self.company_name == 'EVERQUOTE INC - CLASS A':
                params = {
                    'for': 'everquote',
                    'error': 'true',
                }
            if self.company_name == 'AGORA INC-ADR':
                params = {
                    'for': 'agoralabinc',
                    'error': 'true',
                }
            if self.company_name == 'AST SPACEMOBILE INC':
                params = {
                    'for': 'astspacemobile',
                    'error': 'true',
                }
            if self.company_name == 'SENECA FOODS CORP - CL A':
                params = {
                    'for': 'stitchfix',
                    'error': 'true',
                }
            if self.company_name == 'NEXTDOOR HOLDINGS INC':
                params = {
                    'for': 'nextdoor',
                    'error': 'true',
                }
            if self.company_name == 'HERON THERAPEUTICS INC':
                params = {
                    'for': 'herontherapeutics',
                }
            if self.company_name == 'SEER INC':
                params = {
                    'for': 'seer',
                    'error': 'true',
                }
            if self.company_name == 'SKILLZ INC':
                params = {
                    'for': 'allbirds',
                    'error': 'true',
                }
            if self.company_name == 'ALLBIRDS INC-CL A':
                params = {
                    'for': 'skillzinc',
                    'error': 'true',
                }
            if self.company_name == 'VERITONE INC':
                params = {
                    'for': 'greenhouse',
                }
            if self.company_name == 'TRUECAR INC':
                params = {
                    'for': 'truecar',
                    'error': 'true',
                }
            if self.company_name == 'ABSCI CORP':
                params = {
                    'for': 'absci',
                    'error': 'true',
                }
            if self.company_name == 'GROUPON INC':
                params = {}
            if self.company_name == '1STDIBS.COM INC':
                params = {
                    'for': '1stdibscom',
                }
            if self.company_name == 'BLACKSKY TECHNOLOGY INC':
                params = {
                    'for': 'blacksky',
                }
            if self.company_name == 'BRIGHTON HEALTHCARE INC':
                params = {
                    'for': 'vitalfarms',
                }
            if self.company_name == 'VIVID SEATS INC - CLASS A':
                params = {
                    'for': 'vividseatsllc',
                }
            if self.company_name == 'INDIE SEMICONDUCTOR INC-A':
                params = {
                    'for': 'indiesemiconductor',
                }
            if self.company_name == 'ADAPTIVE BIOTECHNOLOGIES':
                params = {
                    'for': 'adaptivebiotechnologies',
                    'error': 'true',
                }
            if self.company_name == 'FIGS INC-CLASS A':
                params = {
                    'for': 'figs15',
                    'error': 'true',
                }
            response = self.make_request('GET', self.base_url, self.company_name, requset_count=3, params=params)
            tree = etree.HTML(response.text)
            total = tree.xpath('//div[@class="padding"]/h2//text()')[0]
            result = re.findall(r'\d+', total)
            total = int(result[0])
            return total
        except Exception as e:
            logging.error(f"Error getting total jobs: {e} for {self.company_name}")
            return 0

    def parse_job(self, job_data):
        """解析职位数据"""
        pass

    def fetch_jobs(self,max_workers):
        """获取职位信息"""
        try:
            total_jobs = self.get_total_jobs()
            if total_jobs == 0:
                logging.info(f"No jobs found {self.company_name}")
                return [], 0

            logging.info(f"Found {total_jobs} total jobs")
            return [], total_jobs
        except Exception as e:
            logging.error(f"Error in fetch_jobs: {e}:{self.company_name}")
            return [],0

    def __del__(self):
        """确保 HTTP 客户端被正确关闭"""
        if hasattr(self, 'client'):
            self.client.close()
