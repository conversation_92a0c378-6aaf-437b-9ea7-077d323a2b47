import json
import random
import time
from datetime import datetime
import logging
from urllib.parse import urlparse
from concurrent.futures import ThreadPoolExecutor, as_completed
from spiders.core_spider.base_spider import BaseSpider
from utils.country_utils import CountryCodeUtils
from utils.http_utils.http_client import HTTPClient
from bs4 import BeautifulSoup

# from utils.get_fail_length import append_to_csv,append_to_detail_csv
import re

class AssuredSpider(BaseSpider):
    def __init__(self, company_name, base_url, company_url, cwiq_code):
        super().__init__(company_name, base_url, company_url, cwiq_code)
        self.company_name = company_name
        self.company_url = company_url
        self.cwiq_code = cwiq_code
        self.country_utils = CountryCodeUtils()
        self.headers = {
            'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36',
        }
        self.client = HTTPClient()

    @classmethod
    def create_spider(cls, company_name, base_url, company_url, cwiq_code):
        """工厂方法创建 MedtronicSpider 实例"""
        return cls(company_name, base_url, company_url, cwiq_code)

    def make_request(self, method, url, company_name,requset_count,**kwargs):
        """发送请求的通用方法"""
        kwargs['headers'] = self.headers
        return self.client.request(method, url,company_name, requset_count,log_ip=False, **kwargs)

    def parse_job(self, job_data):
        """解析职位数据"""
        # 解析地点信息
        try:
            if job_data:
                return {
                    "jobId": str(job_data.get("id", None)),
                    "company": self.company_name,
                    "cwiq_code": self.cwiq_code,
                    "companyUrl": self.company_url,
                    "country": '',
                    "city": '',
                    "jobTitle": job_data.get("title", None),
                    "location": job_data.get("location", None),
                    "jobDescription": job_data.get("content", None),
                    "jobUrl": job_data.get("absolute_url", None),
                    "created_time": None,
                    "updated_time": None,
                    "post_time": datetime.now(),
                }
        except Exception as e:
            logging.error(f"Error parsing job data: {e} for {self.company_name}")
            return None

    def fetch_jobs(self,max_workers):
        """获取所有职位信息"""
        try:
            processed_jobs = []
            response = self.make_request('GET', self.base_url, self.company_name, requset_count=3)
            soup = BeautifulSoup(response.text, "html.parser")

            # 查找所有 <script> 标签
            script_tags = soup.find_all("script")

            # 遍历 <script> 标签，寻找包含 "window.__remixContext =" 的内容
            remix_context_content = None
            for script in script_tags:
                if script.string and "window.__remixContext =" in script.string:
                    remix_context_content = script.string
                    break

            if remix_context_content:
                try:
                    # 提取 "window.__remixContext =" 后的 JSON 数据
                    match = re.search(r"window\.\__remixContext = (\{.*\});", remix_context_content)
                    if match:
                        json_content = match.group(1)  # 提取 JSON 内容部分

                        # 将 JSON 内容解析为 Python 对象
                        remix_context_data = json.loads(json_content)['state']['loaderData'].get('routes/embed.job_board')['jobPosts']['data']
                        for job in remix_context_data:
                            parsed_job = self.parse_job(job)
                            if parsed_job:
                                processed_jobs.append(parsed_job)
                except Exception as e:
                    logging.error(f"Error processing job batch for {self.company_name}: {e}")

            logging.info(f"Successfully fetched {len(processed_jobs)} jobs")
            return processed_jobs, "no total_jobs"

        except Exception as e:
            logging.error(f"Error in fetch_jobs: {e}:{self.company_name}")
            return [],0

    def __del__(self):
        """确保 HTTP 客户端被正确关闭"""
        if hasattr(self, 'client'):
            self.client.close()
