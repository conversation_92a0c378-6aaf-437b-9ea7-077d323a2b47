import json
import random
import time
from datetime import datetime
import logging
from urllib.parse import urlparse
from concurrent.futures import ThreadPoolExecutor, as_completed
from spiders.core_spider.base_spider import BaseSpider
from utils.country_utils import CountryCodeUtils
from utils.http_utils.http_client import HTTPClient
from bs4 import BeautifulSoup

# from utils.get_fail_length import append_to_csv,append_to_detail_csv
import re

class GreenHouseOtherSpider(BaseSpider):
    def __init__(self, company_name, base_url, company_url, cwiq_code):
        super().__init__(company_name, base_url, company_url, cwiq_code)
        self.company_name = company_name
        self.company_url = company_url
        self.cwiq_code = cwiq_code
        self.country_utils = CountryCodeUtils()
        self.headers = {
            'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36',
        }
        self.client = HTTPClient()

    @classmethod
    def create_spider(cls, company_name, base_url, company_url, cwiq_code):
        """工厂方法创建 MedtronicSpider 实例"""
        return cls(company_name, base_url, company_url, cwiq_code)

    def make_request(self, method, url, company_name,requset_count,**kwargs):
        """发送请求的通用方法"""
        kwargs['headers'] = self.headers
        return self.client.request(method, url,company_name, requset_count,log_ip=False, **kwargs)

    def get_total_jobs(self):
        """获取总职位数"""
        params = {
            'page': '1',
            '_data': 'routes/$url_token',
        }
        try:
            response = self.make_request('GET', self.base_url, self.company_name,requset_count=3,params=params)
            if response.json().get('jobPosts',''):
                return response.json()['jobPosts']['total']
            else:
                return 0
        except Exception as e:
            logging.error(f"Error getting total jobs: {e} for {self.company_name}")
            return 0

    def parse_job(self, job_data):
        """解析职位数据"""
        # 解析地点信息
        try:
            if job_data:
                return {
                    "jobId": str(job_data.get("id", None)),
                    "company": self.company_name,
                    "cwiq_code": self.cwiq_code,
                    "companyUrl": self.company_url,
                    "country": '',
                    "city": '',
                    "jobTitle": job_data.get("job_title", None),
                    "location": job_data.get("location", None),
                    "jobDescription": job_data.get("content", None),
                    "jobUrl": job_data.get("absolute_url", None),
                    "created_time": None,
                    "updated_time": None,
                    "post_time": datetime.now(),
                }
        except Exception as e:
            logging.error(f"Error parsing job data: {e} for {self.company_name}")
            return None

    def fetch_jobs(self,max_workers):
        """获取所有职位信息"""
        try:
            total_jobs = self.get_total_jobs()
            if total_jobs == 0:
                logging.info(f"No jobs found {self.company_name}")
                return [],0

            logging.info(f"Found {total_jobs} total jobs")

            # 遍历每一页
            jobs_per_request = 50
            total_requests = (total_jobs // jobs_per_request) + (1 if total_jobs % jobs_per_request else 0)
            all_jobs = []
            processed_jobs = []

            with ThreadPoolExecutor(max_workers=10) as executor:
                futures = []
                # 提交所有请求任务
                for i in range(1,total_requests+1):
                    params = {
                        'page': f'{i}',
                        '_data': 'routes/$url_token',
                    }
                    futures.append(
                        executor.submit(
                            self.make_request,
                            'GET',
                            self.base_url,
                            self.company_name,
                            requset_count=3,
                            params=params
                        )
                    )
                    # 每个请求之间随机延时 0.1 到 0.5 秒
                    time.sleep(0.1)

                # 处理所有任务的结果
                for future in as_completed(futures):
                    try:
                        response = future.result()
                        jobs = response.json().get('jobPosts', '').get('data', '')
                        all_jobs.extend(jobs)

                        # 解析每条数据
                        for job in jobs:
                            parsed_job = self.parse_job(job)
                            if parsed_job:
                                processed_jobs.append(parsed_job)

                        # 显示进度
                        if total_jobs > 0:
                            progress = (len(all_jobs) / total_jobs) * 100
                            logging.info(f"Progress: {len(all_jobs)}/{total_jobs} jobs fetched ({progress:.2f}%)")
                    except Exception as e:
                        logging.error(f"Error processing job batch for {self.company_name}: {e}")

            logging.info(f"Successfully fetched {len(processed_jobs)} jobs")
            return processed_jobs,total_jobs

        except Exception as e:
            logging.error(f"Error in fetch_jobs: {e}:{self.company_name}")
            return [],0

    # def get_detail_fail_timeout_length(self,num_job_1):
    #     model = 'detail'
    #     if len(num_job_1) == 1:
    #         append_to_detail_csv(self.company_name, num_job_1[0], "NaN", "NaN",  model)
    #     if len(num_job_1) == 2:
    #         append_to_detail_csv(self.company_name, num_job_1[0], num_job_1[1], "NaN",  model)
    #     if len(num_job_1) == 3:
    #         append_to_detail_csv(self.company_name, num_job_1[0], num_job_1[1], num_job_1[2],  model)

    # def get_fail_timeout_length(self, job_details, fail_detail_jobs, fail_detail_two_jobs):
    #     model = 'detail'
    #     timeout_size = len(job_details)
    #     first_success = len(fail_detail_jobs)
    #     second_success = len(fail_detail_two_jobs)
    #     second_timeout_size = timeout_size - first_success
    #     final_timeout_size = second_timeout_size - second_success
    #     append_to_csv(self.company_name, timeout_size, second_timeout_size, final_timeout_size, model)

    # def retry_request(self, method, url, company_name, retries=3, delay=5, **kwargs):
    #     attempt = 0
    #     fail_jobs  =[]
    #
    #     while attempt < retries:
    #         try:
    #             response = self.make_request(method, url, company_name, **kwargs)
    #             response.raise_for_status()
    #
    #             soup = BeautifulSoup(response.text, 'html.parser')
    #             opening_divs = soup.find_all('div', class_='opening')
    #
    #             # 提取职位URL并简化数据
    #             for div in opening_divs:
    #                 try:
    #                     job_link = div.find('a')
    #                     if job_link and job_link.get('href'):
    #                         job_url = job_link.get('href')
    #                         simplified_job = _simplify_job_data(job_url)
    #                         fail_jobs.append(simplified_job)
    #                 except Exception as e:
    #                     logging.error(f"解析单个job链接失败: {e}")
    #                     continue
    #             return fail_jobs
    #         except Exception as e:
    #             attempt += 1
    #             logging.error(f"Error on attempt {attempt} for {company_name}: {e}")
    #             if attempt < retries:
    #                 logging.info(f"Retrying in {delay} seconds...")
    #             else:
    #                 logging.error(f"All {retries} attempts failed for {company_name}")
    #                 return None

    def retry_failed_details_requests(self, method, url, job_data,retries=3, delay=5, **kwargs):
        fail_two_list = []
        attempt = 0
        while attempt < retries:
            try:
                job_id = job_data.split('/')[-1]
                response = self.make_request(method, url, self.company_name,requset_count=1,)
                if response is None:
                    return None

                # Parse the HTML content
                soup = BeautifulSoup(response.text, 'html.parser')

                # Extract relevant data
                title = soup.find(class_='app-title').get_text(strip=True) if soup.find(class_='app-title') else None
                location = soup.find(class_='location').get_text(strip=True) if soup.find(class_='location') else None
                content = soup.find(id='content').get_text(strip=True) if soup.find(id='content') else None

                # Convert to JSON format
                job_details = {
                    "job_id": job_id,
                    "job_url": url,
                    "job_title": title,
                    "location": location,
                    "content": content
                }
                return job_details

            except Exception as e:
                attempt += 1
                logging.error(f"Error on attempt {attempt} for {self.company_name}: {e}")
                if attempt < retries:
                    logging.info(f"Retrying in {delay} seconds...")
                else:
                    logging.error(f"All {retries} attempts failed for {self.company_name}")
                    fail_two_list.append({
                        "method": method,
                        "url": url,
                        "company_name": self.company_name,
                        "job_data":job_data
                    })
                    return fail_two_list

    def __del__(self):
        """确保 HTTP 客户端被正确关闭"""
        if hasattr(self, 'client'):
            self.client.close()
