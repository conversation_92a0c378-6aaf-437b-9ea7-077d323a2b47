import json
import random
import time
from datetime import datetime
import logging
from urllib.parse import urlparse
from concurrent.futures import ThreadPoolExecutor, as_completed
from spiders.core_spider.base_spider import BaseSpider
from utils.country_utils import CountryCodeUtils
from utils.http_utils.http_client import HTTPClient
from bs4 import BeautifulSoup
import re
from lxml import etree
class ApponeSpider(BaseSpider):
    def __init__(self, company_name, base_url, company_url, cwiq_code):
        super().__init__(company_name, base_url, company_url, cwiq_code)
        self.company_name = company_name
        self.company_url = company_url
        self.cwiq_code = cwiq_code
        self.country_utils = CountryCodeUtils()
        self.headers = {
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
                'Accept-Language': 'zh-CN,zh;q=0.9',
                'Cache-Control': 'max-age=0',
                'Connection': 'keep-alive',
                'Content-Type': 'application/x-www-form-urlencoded',
                'Origin': 'https://www2.appone.com',
                'Referer': 'https://www2.appone.com/Search/Search.aspx?ServerVar=astronics.appone.com',
                'Sec-Fetch-Dest': 'document',
                'Sec-Fetch-Mode': 'navigate',
                'Sec-Fetch-Site': 'same-origin',
                'Sec-Fetch-User': '?1',
                'Upgrade-Insecure-Requests': '1',
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36',
                'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"',
                'sec-ch-ua-mobile': '?0',
                'sec-ch-ua-platform': '"Windows"',
                # 'Cookie': 'ASP.NET_SessionId=3ryrhhfc1k4jpesxeibzauyo; tsPartner=1334-Astronics; WebServerNumber=h12; ADRUM=s=1747210658714&r=https%3A%2F%2Fwww2.appone.com%2FSearch%2FSearch.aspx%3F-*********',
            }

        # 创建 HTTP 客户端
        self.client = HTTPClient()

    @classmethod
    def create_spider(cls, company_name, base_url, company_url, cwiq_code):
        """工厂方法创建 MedtronicSpider 实例"""
        return cls(company_name, base_url, company_url, cwiq_code)

    def make_request(self, method, url, company_name, requset_count, **kwargs):
        """发送请求的通用方法"""
        kwargs['headers'] = self.headers
        return self.client.request(method, url, company_name, requset_count, log_ip=False, **kwargs)

    def get_total_jobs(self):
        """获取总职位数"""
        try:
            params = {
                'ServerVar': 'astronics.appone.com',
            }

            data = {
                '__EVENTTARGET': '',
                '__EVENTARGUMENT': '',
                '__VIEWSTATE': '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',
                '__VIEWSTATEGENERATOR': '56CCD8F3',
                'ctl00$cphBody$txtKeyword': '',
                'ctl00$cphBody$rbKeyword': 'all',
                'ctl00$cphBody$ddlCategory': '461',
                'ctl00$cphBody$hfCategory': '',
                'ctl00$cphBody$ddlLocation': 'Any, Remote',
                'ctl00$cphBody$hfLocation': '',
                'ctl00$cphBody$ddlPosition': '552035',
                'ctl00$cphBody$hfPosition': '',
                'ctl00$cphBody$btnSearch': 'Search for Jobs',
                'ctl00$ucFooter$txtBrandingId': '83',
                'ctl00$sst': 'newfull2-white',
                'ctl00$CountryID': '3',
                'ctl00$fb': '',
                'ctl00$ServerVar': 'astronics.appone.com',
                'ctl00$LanguageID': '2',
            }

            response = self.make_request('POST', self.base_url, self.company_name, requset_count=3,params=params,data=data)
            tree = etree.HTML(response.text)
            total_jobs = tree.xpath("//td[@id='cphBody_tdFoundText']/b//text()")[0]
            result = re.findall(r'\d+', total_jobs)
            num = int(result[0])
            return num
        except Exception as e:
            logging.error(f"Error getting total jobs: {e} for {self.company_name}")
            return 0

    def parse_job(self, job_data):
        """解析职位数据"""
        pass

    def fetch_jobs(self,max_workers):
        """获取所有职位信息"""
        try:
            total_jobs = self.get_total_jobs()
            if total_jobs == 0:
                logging.info(f"No jobs found {self.company_name}")
                return [], 0

            logging.info(f"Found {total_jobs} total jobs")

            return [], total_jobs

        except Exception as e:
            logging.error(f"Error in fetch_jobs: {e}:{self.company_name}")
            return [], 0


    def __del__(self):
        """确保 HTTP 客户端被正确关闭"""
        if hasattr(self, 'client'):
            self.client.close()

