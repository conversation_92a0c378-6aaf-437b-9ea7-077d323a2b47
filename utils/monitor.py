import time
import threading
import psutil
import os
import logging
import functools
import gc
import csv
from datetime import datetime
from concurrent.futures import Thread<PERSON>oolExecutor, TimeoutError as FutureTimeout

MONITOR_CSV_PATH = "/mnt/data1/ctrl/spider_monitor_log.csv"
os.makedirs(os.path.dirname(MONITOR_CSV_PATH), exist_ok=True)

def write_monitor_record(record: dict):
    is_new = not os.path.exists(MONITOR_CSV_PATH)
    with open(MONITOR_CSV_PATH, "a", newline='', encoding="utf-8") as f:
        writer = csv.DictWriter(f, fieldnames=record.keys())
        if is_new:
            writer.writeheader()
        writer.writerow(record)


def monitor_spider(timeout=1800):
    """
    装饰器：监控爬虫运行耗时 + 内存 + 线程数，并记录结果到 CSV
    超过 timeout（秒）则跳过，返回 {"status": "timeout", ...}
    """
    def decorator(func):
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            process = psutil.Process(os.getpid())
            start_time = time.time()
            start_rss = process.memory_info().rss / 1024 / 1024
            start_threads = threading.active_count()

            company = kwargs.get("company", args[0] if args else "UNKNOWN_COMPANY")
            logging.info(f"Spider [{company}] started | Threads: {start_threads}, Memory: {start_rss:.2f} MB")

            result = None
            status = "unknown"

            try:
                with ThreadPoolExecutor(max_workers=1) as executor:
                    future = executor.submit(func, *args, **kwargs)
                    result = future.result(timeout=timeout)
                    status = "success"
            except FutureTimeout:
                status = "timeout"
                result = {
                    "status": "timeout",
                    "company": company,
                    "duration": timeout
                }
                logging.warning(f"Spider [{company}] 超过 {timeout} 秒未完成，自动跳过！")
            except Exception as e:
                status = "error"
                result = {
                    "status": "error",
                    "company": company,
                    "error": str(e)
                }
                logging.error(f"Spider [{company}] 异常: {e}")
            finally:
                gc.collect()
                end_time = time.time()
                end_rss = process.memory_info().rss / 1024 / 1024
                end_threads = threading.active_count()
                duration = round(end_time - start_time, 2)

                logging.info(
                    f"Spider [{company}] finished in {duration:.2f} sec | "
                    f"Threads: {end_threads}, Memory: {end_rss:.2f} MB (+{end_rss - start_rss:.2f})"
                )

                # 写入日志记录
                write_monitor_record({
                    "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                    "company": company,
                    "status": status,
                    "duration_sec": duration,
                    "memory_mb": round(end_rss, 2),
                    "thread_count": end_threads
                })

            return result

        return wrapper
    return decorator
