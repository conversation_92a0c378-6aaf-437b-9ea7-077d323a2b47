from spiders.core_spider.base_spider import BaseSpider
from utils.country_utils import Country<PERSON>odeUtils
import logging
from datetime import datetime

from utils.http_utils.http_client import HTTPClient


# ultipro的模板方法
class SmartrecruitersTotalSpider(BaseSpider):
    def __init__(self, company_name, base_url, company_url, cwiq_code):
        super().__init__(company_name, base_url, company_url, cwiq_code)
        self.company_name = company_name
        self.company_url = company_url
        self.cwiq_code = cwiq_code
        self.country_utils = CountryCodeUtils()
        self.headers = {
    'Accept': 'application/json, text/plain, */*',
    'Accept-Language': 'zh-CN,zh;q=0.9',
    'Connection': 'keep-alive',
    'If-None-Match': 'W/"12942-/lAwBzHpHEMH9vsrvX5IfN2FevU"',
    'Referer': 'https://jobs.smartrecruiters.com/',
    'Sec-Fetch-Dest': 'empty',
    'Sec-Fetch-Mode': 'cors',
    'Sec-Fetch-Site': 'same-origin',
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"',
    'sec-ch-ua-mobile': '?0',
    'sec-ch-ua-platform': '"Windows"',
    # 'Cookie': '__q_state_9mKbWqe9AD4b35NL=eyJ1dWlkIjoiMzhhZmNhODQtYWQ5Ny00MjEyLWIxNTEtZDZkNjUzN2M1NTRhIiwiY29va2llRG9tYWluIjoic21hcnRyZWNydWl0ZXJzLmNvbSIsImFjdGl2ZVNlc3Npb25JZCI6bnVsbCwic2NyaXB0SWQiOiIxNTExNjIwMTk4NDQ4NzkxODQ0Iiwic3RhdGVCeVNjcmlwdElkIjp7IjE1MTE2MjAxOTg0NDg3OTE4NDQiOnsiZGlzbWlzc2VkIjpmYWxzZSwic2Vzc2lvbklkIjpudWxsfX0sIm1lc3NlbmdlckV4cGFuZGVkIjpmYWxzZSwicHJvbXB0RGlzbWlzc2VkIjpmYWxzZSwiY29udmVyc2F0aW9uSWQiOiIxNjA0MDA2MzY4MzM1ODk2ODUwIn0=; cf_clearance=MwuKvWkhdR0c44W1KAZOFSA3z2e_aZytMnuuck46SVU-1747736530-*******-8z7QXzTEeaiD_VY8iCm2CIFhtJTFmlcjklFPOR3ZJUHrEpWGV5kWIkw3.DAd.DB4rBYz160qZzEupMY.gbFpxH.xxtT34Bt5eJViBO7a6JjqeR4.V3Jx.6ZOr2RTCTRn3C51I8bPhCfdyXMxWp0xbirNDIP0trpN3KgOc5lQMxrARHROVJV2cdC6h_L2qJRv7KuaCeIjohf9FcA.oEafDubcqOcyYNjZ_4NYJxwRtjXHJUw9FiY_zsxEDZcw_gSR0yap.4AghlIRXRRF38wtnv0N1u04bdwJpKLMZIbiydZgMkeg1TuCpH.xTz29ddBmwRa.2D_yNHnq60N89lvRyA3e9ekJeJeFp003w1lQhqI; OptanonConsent=isGpcEnabled=0&datestamp=Tue+May+20+2025+18%3A22%3A38+GMT%2B0800+(%E4%B8%AD%E5%9B%BD%E6%A0%87%E5%87%86%E6%97%B6%E9%97%B4)&version=202403.2.0&browserGpcFlag=0&isIABGlobal=false&hosts=&landingPath=NotLandingPage&groups=C0002%3A1%2CC0003%3A1%2CC0004%3A1%2CC0001%3A1&AwaitingReconsent=false; AWSALB=Tiw16yiWTW0fLLeSO4XZWIuW4DmjW9cfOTmrET/UFVm//WKb3ag0Z2Q35oGQ7DTlu7XwATQkBTiasJJ6Sf6CnEC2qKU6lwkytMfJaqxfnSucMYn8CFqvv3+L18tJ; AWSALBCORS=Tiw16yiWTW0fLLeSO4XZWIuW4DmjW9cfOTmrET/UFVm//WKb3ag0Z2Q35oGQ7DTlu7XwATQkBTiasJJ6Sf6CnEC2qKU6lwkytMfJaqxfnSucMYn8CFqvv3+L18tJ',
}
        self.parsed_url = self.base_url.rsplit('/', 2)[0]
        self.apply_base_url = self.parsed_url + "/OpportunityDetail?opportunityId="
        # 创建 HTTP 客户端，使用全局代理配置
        self.client = HTTPClient()

    @classmethod
    def create_spider(cls, company_name, base_url, company_url, cwiq_code):
        """工厂方法创建 MedtronicSpider 实例"""
        return cls(company_name, base_url, company_url, cwiq_code)

    def make_request(self, method, url, company_name, requset_count,**kwargs):
        """发送请求的通用方法"""
        kwargs['headers'] = self.headers
        return self.client.request(method, url, company_name, requset_count,log_ip=False, **kwargs)

    def get_total_jobs(self):
        """获取总职位数"""
        try:
            params = {
                'limit': '100',
            }
            response = self.make_request('GET', self.base_url, self.company_name,requset_count=3, params=params)
            return response.json()['totalFound']
        except Exception as e:
            logging.error(f"Error getting total jobs: {e} for {self.company_name}")
            return 0

    def parse_job(self, job_data):
        """解析职位数据"""
        pass

    def fetch_jobs(self, max_workers):
        """获取所有职位信息"""
        try:
            total_jobs = self.get_total_jobs()
            if total_jobs == 0:
                logging.info(f"No jobs found {self.company_name}")
                return [], 0

            logging.info(f"Found {total_jobs} total jobs")
            return [], total_jobs
        except Exception as e:
            logging.error(f"Error in fetch_jobs: {e}:{self.company_name}")
            return [], 0

    def __del__(self):
        """确保 HTTP 客户端被正确关闭"""
        if hasattr(self, 'client'):
            self.client.close()
