#!/usr/bin/env python3
"""
正确的日志行为测试脚本 - test_correct_logging_behavior.py

测试正确的日志行为：
1. 控制台根据级别过滤输出
2. 如果指定了 log_file，所有级别都写入文件
3. 如果 log_file 为 None，不写入文件，但控制台输出不受影响
"""

import os
import sys
import tempfile

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_console_level_filtering():
    """测试控制台级别过滤"""
    print("🎨 测试控制台级别过滤")
    print("=" * 60)
    
    try:
        from utils.colored_logger import set_console_level, log_info, log_hint, log_warn, log_error, log_success
        
        print("1. 设置控制台级别为 WARN")
        print("   预期：只显示 WARN、ERROR 和 SUCCESS")
        set_console_level('WARN')
        
        print("\n控制台输出测试:")
        log_info("INFO 日志 - 不应在控制台显示")
        log_hint("HINT 日志 - 不应在控制台显示")
        log_success("SUCCESS 日志 - 应该显示（总是显示）")
        log_warn("WARN 日志 - 应该显示")
        log_error("ERROR 日志 - 应该显示")
        
        print("\n2. 设置控制台级别为 INFO")
        print("   预期：显示所有级别")
        set_console_level('INFO')
        
        print("\n控制台输出测试:")
        log_info("INFO 日志 - 应该显示")
        log_hint("HINT 日志 - 应该显示")
        log_success("SUCCESS 日志 - 应该显示")
        log_warn("WARN 日志 - 应该显示")
        log_error("ERROR 日志 - 应该显示")
        
        print("\n✅ 控制台级别过滤测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 控制台级别过滤测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_file_logging_with_console_filtering():
    """测试文件日志 + 控制台过滤"""
    print("\n🎨 测试文件日志 + 控制台过滤")
    print("=" * 60)
    
    try:
        from utils.colored_logger import set_global_log_file, log_info, log_hint, log_warn, log_error, log_success
        
        # 创建临时文件
        temp_file = tempfile.mktemp(suffix='.log', prefix='file_console_')
        print(f"设置日志文件: {temp_file}")
        print("设置控制台级别为 WARN")
        print("预期：控制台只显示 WARN、ERROR、SUCCESS，文件记录所有级别")
        
        set_global_log_file(temp_file, log_add=False, console_level='WARN')
        
        print("\n日志输出测试:")
        log_info("INFO 日志 - 仅文件")
        log_hint("HINT 日志 - 仅文件")
        log_success("SUCCESS 日志 - 控制台+文件")
        log_warn("WARN 日志 - 控制台+文件")
        log_error("ERROR 日志 - 控制台+文件")
        
        # 检查文件内容
        if os.path.exists(temp_file):
            with open(temp_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            print(f"\n文件内容 ({len(lines)} 行):")
            for i, line in enumerate(lines, 1):
                print(f"  {i}: {line.strip()}")
            
            # 验证所有级别都在文件中
            file_content = ''.join(lines)
            levels_in_file = {
                'INFO': 'INFO 日志' in file_content,
                'HINT': 'HINT 日志' in file_content,
                'SUCCESS': 'SUCCESS 日志' in file_content,
                'WARN': 'WARN 日志' in file_content,
                'ERROR': 'ERROR 日志' in file_content
            }
            
            all_levels_found = all(levels_in_file.values())
            if all_levels_found:
                print("✅ 所有级别都写入了文件")
            else:
                print("❌ 部分级别未写入文件")
                for level, found in levels_in_file.items():
                    if not found:
                        print(f"   缺失: {level}")
            
            os.unlink(temp_file)  # 清理文件
            return all_levels_found
        else:
            print("❌ 文件未创建")
            return False
        
    except Exception as e:
        print(f"❌ 文件日志 + 控制台过滤测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_no_file_console_only():
    """测试无文件仅控制台模式"""
    print("\n🎨 测试无文件仅控制台模式")
    print("=" * 60)
    
    try:
        from utils.colored_logger import set_global_log_file, log_info, log_hint, log_warn, log_error, log_success
        
        print("设置 log_file=None，控制台级别为 HINT")
        print("预期：仅控制台输出，显示 HINT、WARN、ERROR、SUCCESS")
        
        set_global_log_file(None, console_level='HINT')
        
        print("\n控制台输出测试:")
        log_info("INFO 日志 - 不应显示")
        log_hint("HINT 日志 - 应该显示")
        log_success("SUCCESS 日志 - 应该显示")
        log_warn("WARN 日志 - 应该显示")
        log_error("ERROR 日志 - 应该显示")
        
        print("\n✅ 无文件仅控制台模式测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 无文件仅控制台模式测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_convenience_function():
    """测试便捷函数"""
    print("\n🎨 测试便捷函数")
    print("=" * 60)
    
    try:
        from utils.colored_logger import set_console_only_logging, log_info, log_hint, log_warn, log_error, log_success
        
        print("使用 set_console_only_logging('WARN')")
        print("预期：仅控制台输出，显示 WARN、ERROR、SUCCESS")
        
        set_console_only_logging('WARN')
        
        print("\n控制台输出测试:")
        log_info("INFO 日志 - 不应显示")
        log_hint("HINT 日志 - 不应显示")
        log_success("SUCCESS 日志 - 应该显示")
        log_warn("WARN 日志 - 应该显示")
        log_error("ERROR 日志 - 应该显示")
        
        print("\n✅ 便捷函数测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 便捷函数测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("📝 正确的日志行为测试")
    print("🎯 核心原则:")
    print("   1. 控制台根据 console_level 过滤输出")
    print("   2. 文件存在时：所有级别写入文件")
    print("   3. 文件不存在时：不影响控制台输出")
    print("   4. SUCCESS 级别总是在控制台显示")
    print()
    
    tests = [
        ("控制台级别过滤", test_console_level_filtering),
        ("文件日志+控制台过滤", test_file_logging_with_console_filtering),
        ("无文件仅控制台", test_no_file_console_only),
        ("便捷函数", test_convenience_function)
    ]
    
    success_count = 0
    total_tests = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🧪 运行测试: {test_name}")
        if test_func():
            success_count += 1
            print(f"✅ {test_name} 测试通过")
        else:
            print(f"❌ {test_name} 测试失败")
    
    print("\n" + "=" * 60)
    print(f"测试结果: {success_count}/{total_tests} 通过")
    
    if success_count == total_tests:
        print("✅ 所有测试通过")
        print("💡 日志系统行为总结:")
        print("   - 控制台：根据级别过滤，SUCCESS 总是显示")
        print("   - 文件：存在时记录所有级别，不存在时不影响控制台")
        print("   - 级别：INFO < HINT < SUCCESS < WARN < ERROR")
    else:
        print("❌ 部分测试失败")

if __name__ == "__main__":
    main()
