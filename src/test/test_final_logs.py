#!/usr/bin/env python3
"""
最终日志功能测试脚本 - test_final_logs.py

测试修改后的日志功能：
1. log_error 替代 log_io_error
2. 日志文件写入功能
3. 追加模式和重新创建模式
"""

import os
import sys
import tempfile

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.colored_logger import (
    set_global_log_file, log_success, log_error, log_warn,
    log_info, log_hint, log_spider_result, log_exception
)


def test_basic_logging():
    """测试基础日志功能"""
    print("\n" + "=" * 60)
    print("测试1: 基础日志功能")
    print("=" * 60)

    # 测试各种类型的日志
    log_success("爬虫系统启动成功")
    log_hint("开始加载配置文件...")
    log_success("已加载 2131 个爬虫配置")
    log_error("数据库连接失败: Connection timeout")
    log_warn("JSON解析错误: Invalid format")
    log_hint("正在重试连接...")
    log_success("数据库连接恢复")
    log_info("系统运行正常")

    print("✅ 基础日志功能测试完成")


def test_log_to_file():
    """测试日志写入文件功能"""
    print("\n" + "=" * 60)
    print("测试2: 日志写入文件功能")
    print("=" * 60)

    # 创建临时文件
    temp_file = tempfile.mktemp(suffix='.log', prefix='spider_test_')
    print(f"日志文件: {temp_file}")

    # 设置全局日志文件 - 重新创建模式
    set_global_log_file(temp_file, log_add=False)

    # 写入各种类型的日志
    log_success("文件日志测试开始")
    log_hint("Processing spiders...")
    log_error("模拟错误: Database connection failed")
    log_warn("模拟解析错误: Invalid JSON")
    log_success("文件日志测试完成")

    # 读取并显示文件内容
    print("\n日志文件内容:")
    with open(temp_file, 'r', encoding='utf-8') as f:
        content = f.read()
        for i, line in enumerate(content.strip().split('\n'), 1):
            print(f"  {i:2d}: {line}")

    # 清理
    os.unlink(temp_file)
    print("✅ 日志写入文件功能测试完成")


def test_log_append_mode():
    """测试日志追加模式"""
    print("\n" + "=" * 60)
    print("测试3: 日志追加模式")
    print("=" * 60)

    # 创建临时文件并写入初始内容
    temp_file = tempfile.mktemp(suffix='.log', prefix='spider_append_')
    print(f"日志文件: {temp_file}")

    with open(temp_file, 'w', encoding='utf-8') as f:
        f.write("2025-01-01 00:00:00 - 这是旧的日志内容\n")

    print("原有文件内容:")
    with open(temp_file, 'r', encoding='utf-8') as f:
        for i, line in enumerate(f.readlines(), 1):
            print(f"  {i:2d}: {line.strip()}")

    # 设置全局日志文件 - 追加模式
    set_global_log_file(temp_file, log_add=True)

    # 写入新的日志
    log_hint("系统重新启动")
    log_success("配置重新加载完成")
    log_error("发现新的错误")

    print("\n追加后文件内容:")
    with open(temp_file, 'r', encoding='utf-8') as f:
        for i, line in enumerate(f.readlines(), 1):
            print(f"  {i:2d}: {line.strip()}")

    # 清理
    os.unlink(temp_file)
    print("✅ 日志追加模式测试完成")


def test_spider_results():
    """测试爬虫结果日志"""
    print("\n" + "=" * 60)
    print("测试4: 爬虫结果日志")
    print("=" * 60)

    # 测试不同状态的爬虫结果
    log_spider_result("Microsoft", "success")
    log_spider_result("Google", "error", "database connection failed")
    log_spider_result("Amazon", "error", "html parse error")
    log_spider_result("Tesla", "timeout", "request timeout after 30s")
    log_spider_result("Apple", "error", "json decode error")
    log_spider_result("Facebook", "error", "file write permission denied")

    print("✅ 爬虫结果日志测试完成")


def test_exception_logging():
    """测试异常日志功能"""
    print("\n" + "=" * 60)
    print("测试5: 异常日志功能")
    print("=" * 60)

    # 测试不同类型的异常
    try:
        import json
        json.loads("invalid json")
    except json.JSONDecodeError as e:
        log_exception("JSON parsing failed", e)

    try:
        open("/nonexistent/file.txt", "r")
    except FileNotFoundError as e:
        log_exception("File operation failed", e)

    # 模拟SQL错误
    class MockSQLError(Exception):
        pass

    try:
        raise MockSQLError("Table 'jobs' doesn't exist")
    except MockSQLError as e:
        log_exception("Database query failed", e)

    print("✅ 异常日志功能测试完成")


def test_comprehensive_scenario():
    """测试综合场景"""
    print("\n" + "=" * 60)
    print("测试6: 综合场景 - 模拟真实爬虫执行")
    print("=" * 60)

    # 创建临时文件
    temp_file = tempfile.mktemp(suffix='.log', prefix='spider_comprehensive_')
    print(f"日志文件: {temp_file}")

    # 设置日志文件
    set_global_log_file(temp_file, log_add=False)

    # 模拟完整的爬虫执行流程
    log_hint("Starting Marzaha spider system...")
    log_success("Loaded 2131 spider configurations")

    # 模拟处理进度
    for i in [500, 1000, 1500, 2000, 2131]:
        log_hint(f"Processing spiders: {i}/2131")

        # 模拟一些错误
        if i == 500:
            log_warn("JSON decode error in response from api.example.com")
        elif i == 1000:
            log_error("Database connection lost, retrying...")
            log_success("Database connection restored")
        elif i == 1500:
            log_error("File write permission denied for /tmp/spider_data.json")

    # 模拟最终结果
    log_success("Database optimization completed")
    log_success("Parquet files generated successfully")
    log_success("All processing completed in 3600.5 seconds")
    log_success("Success rate: 85.6% (1,825/2,131)")

    print(f"\n生成的日志文件大小: {os.path.getsize(temp_file)} 字节")
    print("日志文件内容:")
    with open(temp_file, 'r', encoding='utf-8') as f:
        lines = f.readlines()
        for i, line in enumerate(lines, 1):
            print(f"  {i:2d}: {line.strip()}")

    # 清理
    os.unlink(temp_file)
    print("✅ 综合场景测试完成")


def main():
    """主函数"""
    print("🎨 Marzaha 爬虫系统 - 最终日志功能测试")
    print("📋 更新内容:")
    print("   ✅ log_io_error → log_error")
    print("   ✅ 支持自定义日志文件")
    print("   ✅ 支持追加模式和重新创建模式")
    print("   ✅ 文件创建失败时程序退出")
    print()

    # 运行所有测试
    test_basic_logging()
    test_log_to_file()
    test_log_append_mode()
    test_spider_results()
    test_exception_logging()
    test_comprehensive_scenario()

    print("\n" + "=" * 60)
    print("✅ 所有日志功能测试完成")
    print("💡 使用方法:")
    print("   1. 导入: from utils.colored_logger import set_global_log_file, log_*")
    print("   2. 设置文件: set_global_log_file('/path/to/log.file', log_add=False)")
    print("   3. 使用日志: log_success(), log_error(), log_parse_error() 等")
    print("   4. log_add=True 追加，log_add=False 重新创建（默认）")
    print("=" * 60)


if __name__ == "__main__":
    main()
