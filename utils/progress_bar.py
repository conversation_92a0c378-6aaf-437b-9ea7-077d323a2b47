"""
进度条工具模块 - progress_bar.py

提供进度条显示功能，用于替代冗长的日志输出
"""


def create_progress_bar(percentage: float, width: int = 30) -> str:
    """
    创建进度条字符串
    
    Args:
        percentage: 完成百分比 (0-100)
        width: 进度条宽度（字符数）
    
    Returns:
        str: 格式化的进度条字符串
    
    Examples:
        >>> create_progress_bar(50)
        '[███████████████░░░░░░░░░░░░░░░]'
        >>> create_progress_bar(75, 20)
        '[███████████████░░░░░]'
    """
    if percentage < 0:
        percentage = 0
    elif percentage > 100:
        percentage = 100
        
    filled_width = int(width * percentage / 100)
    bar = '█' * filled_width + '░' * (width - filled_width)
    return f"[{bar}]"


def create_simple_progress_bar(percentage: float, width: int = 30) -> str:
    """
    创建简单的ASCII进度条（兼容性更好）
    
    Args:
        percentage: 完成百分比 (0-100)
        width: 进度条宽度（字符数）
    
    Returns:
        str: 格式化的进度条字符串
    
    Examples:
        >>> create_simple_progress_bar(50)
        '[===============               ]'
        >>> create_simple_progress_bar(75, 20)
        '[===============     ]'
    """
    if percentage < 0:
        percentage = 0
    elif percentage > 100:
        percentage = 100
        
    filled_width = int(width * percentage / 100)
    bar = '=' * filled_width + ' ' * (width - filled_width)
    return f"[{bar}]"


def format_progress_message(current: int, total: int, prefix: str = "Progress") -> str:
    """
    格式化进度消息
    
    Args:
        current: 当前完成数量
        total: 总数量
        prefix: 前缀文本
    
    Returns:
        str: 格式化的进度消息
    
    Examples:
        >>> format_progress_message(150, 200)
        'Progress: [██████████████████████░░░░░░░░] 150/200 (75.0%)'
    """
    if total == 0:
        percentage = 100
    else:
        percentage = (current / total) * 100
    
    progress_bar = create_progress_bar(percentage)
    return f"{prefix}: {progress_bar} {current}/{total} ({percentage:.1f}%)"


# 测试函数
if __name__ == "__main__":
    print("Testing progress bar functions...")
    
    # 测试基本进度条
    print("\n1. Basic progress bar:")
    for i in range(0, 101, 25):
        bar = create_progress_bar(i)
        print(f"  {i:3d}%: {bar}")
    
    # 测试简单进度条
    print("\n2. Simple progress bar:")
    for i in range(0, 101, 25):
        bar = create_simple_progress_bar(i)
        print(f"  {i:3d}%: {bar}")
    
    # 测试格式化消息
    print("\n3. Formatted progress messages:")
    total = 2131
    for current in [0, 500, 1000, 1500, 2000, 2131]:
        message = format_progress_message(current, total, "Processing spiders")
        print(f"  {message}")
    
    print("\nProgress bar test completed!")
