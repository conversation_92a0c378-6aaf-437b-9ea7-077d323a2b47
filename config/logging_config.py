# config/logging_config.py
import logging
import logging.handlers
import os
from datetime import datetime


def setup_logging(log_dir="/mnt/data1/leo"):
    """
    配置日志系统
    只处理文件日志，控制台输出由彩色日志系统处理
    """
    # 创建日志目录
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)

    # 生成日志文件名
    log_filename = os.path.join(
        log_dir,
        f"spider_{datetime.now().strftime('%Y%m')}.log"
    )

    # 创建格式化器
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )

    # 创建文件处理器（按天滚动）
    file_handler = logging.handlers.TimedRotatingFileHandler(
        filename=log_filename,
        when='midnight',
        interval=1,
        backupCount=30,  # 保留30天的日志
        encoding='utf-8'
    )
    file_handler.setFormatter(formatter)
    file_handler.setLevel(logging.INFO)

    # 配置根日志记录器 - 只添加文件处理器，不添加控制台处理器
    root_logger = logging.getLogger()
    root_logger.setLevel(logging.INFO)

    # 清除现有的处理器，避免重复
    root_logger.handlers.clear()

    # 只添加文件处理器
    root_logger.addHandler(file_handler)

    # 配置特定模块的日志级别
    logging.getLogger('urllib3').setLevel(logging.WARNING)
    logging.getLogger('requests').setLevel(logging.WARNING)
    logging.getLogger('celery').setLevel(logging.INFO)
