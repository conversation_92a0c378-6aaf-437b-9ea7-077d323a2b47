# Marzaha 爬虫项目依赖包
# 基于项目源码分析生成的完整依赖列表

# ================================
# 核心框架与调度
# ================================
# 任务调度器
APScheduler==3.10.4

# 异步任务队列
celery==5.3.4
redis==5.0.1

# ================================
# 数据库相关
# ================================
# MySQL数据库连接
PyMySQL==1.1.0
# ORM框架
SQLAlchemy==2.0.23

# ================================
# 数据处理与分析
# ================================
# 数据分析库
pandas==2.1.4
# 数据格式支持
pyarrow==14.0.2
# 数值计算
numpy==1.24.4
# pandas性能优化
bottleneck>=1.3.4


# ================================
# 网页解析
# ================================
# HTML解析器
beautifulsoup4==4.12.2
# XML解析器
lxml==4.9.4

# ================================
# 浏览器自动化 (可选)
# ================================
# Selenium WebDriver
selenium==4.16.0
# Chrome WebDriver管理
webdriver-manager==4.0.1

# ================================
# 配置文件处理
# ================================
# YAML配置文件解析
PyYAML==6.0.1

# ================================
# 地理位置处理
# ================================
# 国家代码处理
pycountry==24.6.1
# 地理编码
geopy==2.4.1

# ================================
# 云存储服务
# ================================
# AWS S3存储
boto3==1.34.0
botocore==1.34.0

# Google Cloud Storage
google-cloud-storage==2.10.0
google-auth==2.25.2

# ================================
# 系统监控
# ================================
# 系统资源监控
psutil==5.9.6

# ================================
# 类型检查与开发工具
# ================================
# 类型提示支持
typing-extensions==4.8.0

# ================================
# 日志与调试
# ================================
# 日志处理增强
colorlog==6.8.0

# ================================
# 并发与多线程
# ================================
# 线程池增强 (Python内置，无需安装)
# concurrent.futures


# ================================
# 性能优化
# ================================
# 内存优化
memory-profiler==0.61.0

# ================================
# 测试框架 (开发环境)
# ================================
# 单元测试框架
pytest==7.4.3
pytest-asyncio==0.21.1
pytest-cov==4.1.0

# ================================
# 代码质量工具 (开发环境)
# ================================
# 代码格式化
black==23.11.0
# 代码检查
flake8==6.1.0
# 导入排序
isort==5.12.0

# ================================
# 文档生成 (开发环境)
# ================================
# 文档生成
sphinx==7.2.6

# ================================
# 环境管理
# ================================
# 环境变量管理
python-dotenv==1.0.0

# ================================
# 数据序列化
# ================================
# JSON处理增强
ujson==5.8.0

# ================================
# 字符串处理
# ================================
# 字符串相似度
fuzzywuzzy==0.18.0
python-Levenshtein==0.23.0

# ================================
# 图像处理 (如需要验证码处理)
# ================================
# 图像处理库
Pillow==10.1.0

# ================================
# 机器学习 (可选，用于数据分析)
# ================================
# 机器学习库
scikit-learn==1.3.2

# ================================
# 版本信息
# ================================
# Python版本要求: >=3.8,<4.0
# 项目兼容性: 支持Linux/Windows/macOS
# 最后更新: 2024-01-15
