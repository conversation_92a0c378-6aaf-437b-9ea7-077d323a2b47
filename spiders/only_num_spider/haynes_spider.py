
import re

from spiders.core_spider.base_spider import BaseSpider
from utils.country_utils import CountryCodeUtils
import logging
from datetime import datetime
from lxml import etree
from utils.http_utils.http_client import HTTPClient


# ultipro的模板方法
class HaynesSpider(BaseSpider):
    def __init__(self, company_name, base_url, company_url, cwiq_code):
        super().__init__(company_name, base_url, company_url, cwiq_code)
        self.company_name = company_name
        self.company_url = company_url
        self.cwiq_code = cwiq_code
        self.country_utils = CountryCodeUtils()
        self.headers = {
    'accept': 'application/json, text/javascript, */*; q=0.01',
    'accept-language': 'zh-CN,zh;q=0.9',
    'content-type': 'application/json; charset=UTF-8',
    'origin': 'https://haynes.rec.pro.ukg.net',
    'priority': 'u=1, i',
    'referer': 'https://haynes.rec.pro.ukg.net/HAY1005HYNI/JobBoard/68a4ae6e-6220-4bb4-85b7-2bc6644e25b3/?q=&o=postedDateDesc&w=&wc=&we=&wpst=',
    'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"',
    'sec-ch-ua-mobile': '?0',
    'sec-ch-ua-platform': '"Windows"',
    'sec-fetch-dest': 'empty',
    'sec-fetch-mode': 'cors',
    'sec-fetch-site': 'same-origin',
    'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    'x-requested-with': 'XMLHttpRequest',
    'x-requestverificationtoken': 'CfDJ8PN7ODvWXMxHkleslK0o9Cl8izOaKxT6JLjIJ5W8-DlHvB_k8JO9rBN9kCtXP1qpTVGMX3vgp144PqB07AHbjjeFc8fRafRKkvOlKWmkn8ol3v6RD83lpwHSV4O8_ZqgX8bUW88mGyyKvGg1ZTPhDiY',
    # 'cookie': '.AspNetCore.Antiforgery.JethAzbTYfI=CfDJ8PN7ODvWXMxHkleslK0o9CmJMoYx8GjPeXqOTyMnzjNF5jdcMtbHbJOQDqjzdIUAEz-_cWZcSnMdrcVHlbz-4Nw87iWwnvifq-h_1rtjqBiTLvUyLHL4ZGtGryOE8RWFyBR5csJfibhTTASuZJTKEGc; nonce=bP1J3C739gsZ1jq-08j9XJlKjUmRMPZex78iRJ9dHw8',
}

        # 创建 HTTP 客户端，使用全局代理配置
        self.client = HTTPClient()

    @classmethod
    def create_spider(cls, company_name, base_url, company_url, cwiq_code):
        """工厂方法创建 MedtronicSpider 实例"""
        return cls(company_name, base_url, company_url, cwiq_code)

    def make_request(self, method, url, company_name, requset_count,**kwargs):
        """发送请求的通用方法"""
        kwargs['headers'] = self.headers
        return self.client.request(method, url, company_name, requset_count,log_ip=False, **kwargs)

    def get_total_jobs(self):
        """获取总职位数"""
        try:
            json_data = {
                'opportunitySearch': {
                    'Top': 50,
                    'Skip': 0,
                    'QueryString': '',
                    'OrderBy': [
                        {
                            'Value': 'postedDateDesc',
                            'PropertyName': 'PostedDate',
                            'Ascending': False,
                        },
                    ],
                    'Filters': [
                        {
                            't': 'TermsSearchFilterDto',
                            'fieldName': 4,
                            'extra': None,
                            'values': [],
                        },
                        {
                            't': 'TermsSearchFilterDto',
                            'fieldName': 5,
                            'extra': None,
                            'values': [],
                        },
                        {
                            't': 'TermsSearchFilterDto',
                            'fieldName': 6,
                            'extra': None,
                            'values': [],
                        },
                        {
                            't': 'TermsSearchFilterDto',
                            'fieldName': 37,
                            'extra': None,
                            'values': [],
                        },
                    ],
                },
                'matchCriteria': {
                    'PreferredJobs': [],
                    'Educations': [],
                    'LicenseAndCertifications': [],
                    'Skills': [],
                    'hasNoLicenses': False,
                    'SkippedSkills': [],
                },
            }
            response = self.make_request('POST', self.base_url, self.company_name,requset_count=3,json=json_data,)
            total = response.json()['totalCount']
            return total
        except Exception as e:
            logging.error(f"Error getting total jobs: {e} for {self.company_name}")
            return 0

    def parse_job(self, job_data):
        """解析职位数据"""
        pass

    def fetch_jobs(self, max_workers):
        """获取所有职位信息"""
        try:
            total_jobs = self.get_total_jobs()
            if total_jobs == 0:
                logging.info(f"No jobs found {self.company_name}")
                return [], 0

            logging.info(f"Found {total_jobs} total jobs")
            return [], total_jobs
        except Exception as e:
            logging.error(f"Error in fetch_jobs: {e}:{self.company_name}")
            return [], 0

    def __del__(self):
        """确保 HTTP 客户端被正确关闭"""
        if hasattr(self, 'client'):
            self.client.close()
