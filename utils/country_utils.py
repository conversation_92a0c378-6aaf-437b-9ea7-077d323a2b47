import pycountry
import logging
from typing import Optional, Dict
from geopy.geocoders import Nominatim
from geopy.exc import GeocoderTimedOut

class CountryCodeUtils:
    def __init__(self):
        # 缓存所有国家代码映射，提高性能
        self._name_to_alpha3: Dict[str, str] = {}
        self._initialize_mapping()

        # 特殊情况映射字典
        self._special_cases = {
            "United States": "USA",
            "United States of America": "USA",
            "United States of America (Virtual)": "USA",
            "USA": "USA",
            "UK": "GBR",
            "United Kingdom": "GBR",
            "United Kingdom (Virtual)": "GBR",
            "United Kingdom (UK) ": "GBR",
            "Russia": "RUS",
            "Russian Federation": "RUS",
            "Korea, Republic of": "KOR",
            "South Korea": "KOR",
            "Taiwan": "TWN",
            "Taiwan, Province of China": "TWN",
            "Vietnam": "VNM",
            "Viet Nam": "VNM",
            "Czech Republic": "CZE",
            "Czechia": "CZE",
            "Turkey": "TUR",
            "Poland (Virtual)": "POL",
            "Казахстан": "KAZ",
            "Россия": "RUS",
            "United Arab Emirates (UAE) ": "ARE"
        }

    def _initialize_mapping(self):
        """初始化国家名称到 alpha3 的映射"""
        for country in pycountry.countries:
            # 存储官方名称映射
            self._name_to_alpha3[country.name.upper()] = country.alpha_3
            # 存储常用名称映射
            if hasattr(country, 'common_name'):
                self._name_to_alpha3[country.common_name.upper()] = country.alpha_3
            # 存储官方名称映射
            if hasattr(country, 'official_name'):
                self._name_to_alpha3[country.official_name.upper()] = country.alpha_3

    def get_alpha3(self, country_name: str) -> Optional[str]:
        """
        将国家名称转换为 ISO 3166-1 alpha-3 代码

        Args:
            country_name: 国家名称

        Returns:
            str: 3位的国家代码，如果未找到则返回None
        """
        if not country_name:
            return None

        # 去除空白字符并转换为大写
        cleaned_name = country_name.strip().upper()

        # 检查特殊情况
        if country_name in self._special_cases:
            return self._special_cases[country_name]

        # 从缓存中查找
        if cleaned_name in self._name_to_alpha3:
            return self._name_to_alpha3[cleaned_name]

        try:
            # 尝试使用 pycountry 进行查找
            country = pycountry.countries.search_fuzzy(country_name)[0]

            # 将结果添加到缓存中
            self._name_to_alpha3[cleaned_name] = country.alpha_3
            return country.alpha_3

        except (LookupError, IndexError) as e:
            logging.warning(f"Could not find alpha-3 code for country: {country_name}")
            return None
        except Exception as e:
            logging.error(f"Error converting country name '{country_name}' to alpha-3: {str(e)}")
            return None

    def convert_alpha2_to_alpha3(self, alpha2: str) -> Optional[str]:
        """
        将 ISO 3166-1 alpha-2 代码转换为 alpha-3 代码

        Args:
            alpha2: 2位的国家代码

        Returns:
            str: 3位的国家代码，如果未找到则返回None
        """
        if not alpha2:
            return None

        try:
            country = pycountry.countries.get(alpha_2=alpha2.upper())
            return country.alpha_3 if country else None
        except Exception as e:
            logging.error(f"Error converting alpha-2 '{alpha2}' to alpha-3: {str(e)}")
            return None

    def get_country_by_city(self,city):
        geolocator = Nominatim(user_agent="city_to_country")

        try:
            # 获取城市的地理信息
            location = geolocator.geocode(city)

            if location:
                # 返回城市所在的国家
                return location.address.split(',')[-1].strip()  # 取地址中的最后一个元素，即国家名
            else:
                logging.warning(f"Could not find country for city: {city}")
                return None
        except GeocoderTimedOut:
            logging.warning(f"Geocoding request timed out for city: {city}")
            return None

