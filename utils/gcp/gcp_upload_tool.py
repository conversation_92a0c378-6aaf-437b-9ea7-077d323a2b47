import logging
from google.cloud import storage
from typing import List, Dict, Optional


class GCPUploader:
    def __init__(self, service_account_json: str, bucket_name: str):
        """
        初始化GCP上传工具类

        Args:
            service_account_json: GCP服务账号JSON路径
            bucket_name: GCS存储桶名称
        """
        self.bucket_name = bucket_name
        self.client = storage.Client.from_service_account_json(service_account_json)
        self.bucket = self.client.bucket(bucket_name)

    def upload_file(self, file_path: str, gcs_folder: Optional[str] = None) -> bool:
        """
        上传文件到GCS指定文件夹

        Args:
            file_path: 本地文件路径
            gcs_folder: GCS目标文件夹，例如 'all_companies_info'

        Returns:
            bool: 上传是否成功
        """
        import os
        try:
            if not os.path.exists(file_path):
                logging.info(f"文件不存在：{file_path}")
                return False

            file_name = os.path.basename(file_path)
            # 构建GCS路径（支持带文件夹的路径）
            gcs_key = f"{gcs_folder}/{file_name}" if gcs_folder else file_name

            blob = self.bucket.blob(gcs_key)
            blob.upload_from_filename(file_path)

            logging.info(f"✅ 文件上传成功：{gcs_key}")
            return True
        except Exception as e:
            logging.error(f"❌ 上传失败：{e}")
            return False

    def list_files(self, folder: Optional[str] = None) -> List[Dict]:
        """
        列出存储桶或指定文件夹中的文件

        Args:
            folder: GCS文件夹路径，例如 'all_companies_info'

        Returns:
            List[Dict]: 文件信息列表
        """
        try:
            prefix = f"{folder}/" if folder else ""
            blobs = self.client.list_blobs(self.bucket_name, prefix=prefix)

            files = []
            for blob in blobs:
                files.append({
                    'key': blob.name,
                    'size': blob.size,
                    'last_modified': blob.updated
                })
                logging.info(f"- {blob.name} ({blob.size} 字节)")

            return files
        except Exception as e:
            logging.error(f"❌ 无法列出文件：{e}")
            return []

    def list_folders(self) -> List[str]:
        """
        列出存储桶中的所有文件夹（通过前缀模拟）

        Returns:
            List[str]: 文件夹列表
        """
        try:
            folders = set()
            blobs = self.client.list_blobs(self.bucket_name)

            for blob in blobs:
                # 通过前缀获取文件夹
                parts = blob.name.split('/')
                if len(parts) > 1:
                    folder = parts[0]
                    folders.add(folder)

            for folder in folders:
                print(f"- {folder}/")

            return list(folders)
        except Exception as e:
            logging.error(f"❌ 无法列出文件夹：{e}")
            return []
