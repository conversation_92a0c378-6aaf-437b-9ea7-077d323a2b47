"""
彩色日志系统 - colored_logger.py

提供彩色控制台输出和文件日志记录功能，使用标准 logging 级别。

主要功能：
1. 彩色控制台输出 - 不同级别使用不同颜色
2. 文件日志记录 - 可选的文件输出功能
3. 标准日志级别 - DEBUG, INFO, WARNING, ERROR
4. 双输出模式 - 同时输出到控制台和文件
5. 配置化管理 - 通过 settings.ini 控制行为

级别定义：
- DEBUG (10): 调试信息，灰色
- INFO (20): 一般信息，蓝色
- WARNING (30): 警告信息，黄色
- ERROR (40): 错误信息，红色

作者：Marzaha Team
版本：2.0
更新时间：2025
"""

import logging
import os
import sys
from datetime import datetime
from typing import Optional

# 导入配置加载器
from config.settings_loader import load_settings

# ANSI 颜色代码
class Colors:
    """ANSI 颜色代码定义"""
    GRAY = '\033[90m'      # 灰色 - DEBUG
    BLUE = '\033[94m'      # 蓝色 - INFO
    YELLOW = '\033[93m'    # 黄色 - WARNING
    RED = '\033[91m'       # 红色 - ERROR
    GREEN = '\033[92m'     # 绿色 - SUCCESS (特殊用途)
    BOLD = '\033[1m'       # 粗体
    RESET = '\033[0m'      # 重置

# 级别到颜色的映射
LEVEL_COLORS = {
    logging.DEBUG: Colors.GRAY,
    logging.INFO: Colors.BLUE,
    logging.WARNING: Colors.YELLOW,
    logging.ERROR: Colors.RED,
}

# 全局配置
_settings = load_settings()
_console_level_str = getattr(_settings.logger, 'console_level', 'INFO')
_log_file = getattr(_settings.logger, 'default_log_file', None)
_log_add = getattr(_settings.logger, 'default_log_add', False)

# 将字符串级别转换为数字
def _get_level_value(level_str: str) -> int:
    """将字符串级别转换为数字值"""
    level_map = {
        'DEBUG': logging.DEBUG,
        'INFO': logging.INFO,
        'WARNING': logging.WARNING,
        'WARN': logging.WARNING,  # 兼容性
        'ERROR': logging.ERROR,
    }
    return level_map.get(level_str.upper(), logging.INFO)

# 获取控制台输出级别
_console_level = _get_level_value(_console_level_str)


def _should_log_to_console(level: int) -> bool:
    """判断是否应该输出到控制台"""
    return level >= _console_level

def _format_message(level: int, message: str) -> str:
    """格式化消息，添加时间戳和级别"""
    timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    level_name = logging.getLevelName(level)
    return f"{timestamp} - {level_name} - {message}"

def _log_to_console(level: int, message: str) -> None:
    """输出到控制台（带颜色）"""
    if not _should_log_to_console(level):
        return

    color = LEVEL_COLORS.get(level, Colors.RESET)
    level_name = logging.getLevelName(level)
    timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

    colored_message = f"{color}{timestamp} - {level_name} - {message}{Colors.RESET}"
    print(colored_message)

def _log_to_file(level: int, message: str) -> None:
    """输出到文件（无颜色）"""
    if not _log_file:
        return

    try:
        # 确保日志目录存在
        log_dir = os.path.dirname(_log_file)
        if log_dir and not os.path.exists(log_dir):
            os.makedirs(log_dir, exist_ok=True)

        # 格式化消息
        formatted_message = _format_message(level, message)

        # 写入文件
        mode = 'a' if _log_add else 'w'
        with open(_log_file, mode, encoding='utf-8') as f:
            f.write(formatted_message + '\n')
            f.flush()  # 确保立即写入
    except Exception as e:
        # 如果文件写入失败，至少输出到控制台
        print(f"{Colors.RED}日志文件写入失败: {e}{Colors.RESET}")

def _log(level: int, message: str) -> None:
    """通用日志记录函数"""
    # 输出到控制台
    _log_to_console(level, message)

    # 输出到文件（如果配置了文件路径）
    _log_to_file(level, message)


# 公共日志函数 - 使用标准 logging 级别
def log_debug(message: str) -> None:
    """记录调试级别日志"""
    _log(logging.DEBUG, message)

def log_info(message: str) -> None:
    """记录信息级别日志"""
    _log(logging.INFO, message)

def log_warning(message: str) -> None:
    """记录警告级别日志"""
    _log(logging.WARNING, message)

def log_warn(message: str) -> None:
    """记录警告级别日志（兼容性别名）"""
    _log(logging.WARNING, message)

def log_error(message: str) -> None:
    """记录错误级别日志"""
    _log(logging.ERROR, message)

def log_success(message: str) -> None:
    """记录成功信息（总是输出，绿色显示）"""
    # 控制台输出（绿色）- SUCCESS 总是显示，不受级别限制
    timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    colored_message = f"{Colors.GREEN}{timestamp} - SUCCESS - {message}{Colors.RESET}"
    print(colored_message)

    # 文件输出（如果配置了文件路径）
    _log_to_file(logging.INFO, f"SUCCESS - {message}")

# 兼容性别名
log_hint = log_info  # HINT 映射到 INFO

# 专用日志函数
def log_spider_result(spider_name: str, status: str, message: str) -> None:
    """记录爬虫结果"""
    if status.lower() == 'success':
        log_success(f"[{spider_name}] {message}")
    elif status.lower() in ['error', 'failed', 'failure']:
        log_error(f"[{spider_name}] {message}")
    else:
        log_info(f"[{spider_name}] {status}: {message}")

def log_database_operation(operation: str, status: str, details: str = "") -> None:
    """记录数据库操作"""
    message = f"数据库操作 - {operation}"
    if details:
        message += f": {details}"

    if status.lower() == 'success':
        log_success(message)
    elif status.lower() in ['error', 'failed', 'failure']:
        log_error(message)
    else:
        log_info(message)

def log_file_operation(operation: str, file_path: str, status: str, details: str = "") -> None:
    """记录文件操作"""
    message = f"文件操作 - {operation}: {file_path}"
    if details:
        message += f" ({details})"

    if status.lower() == 'success':
        log_success(message)
    elif status.lower() in ['error', 'failed', 'failure']:
        log_error(message)
    else:
        log_info(message)

# 配置信息输出
def print_logger_config() -> None:
    """打印当前日志配置信息"""
    print(f"{Colors.BLUE}=== 日志系统配置 ==={Colors.RESET}")
    print(f"{Colors.BLUE}控制台级别: {_console_level_str} (>= {_console_level}){Colors.RESET}")
    if _log_file:
        print(f"{Colors.BLUE}日志文件: {_log_file}{Colors.RESET}")
        print(f"{Colors.BLUE}文件模式: {'追加' if _log_add else '覆盖'}{Colors.RESET}")
    else:
        print(f"{Colors.YELLOW}日志文件: 未配置，仅控制台输出{Colors.RESET}")
    print(f"{Colors.BLUE}=================={Colors.RESET}")

# 模块初始化时打印配置
if __name__ != "__main__":
    # 只在被导入时打印，避免直接运行时重复打印
    pass
