"""
彩色日志工具模块 - colored_logger.py

提供带颜色的日志输出功能，根据不同的错误类型显示不同的颜色：
1. IO错误（文件写入、数据库错误）- 红色
2. 正确结果 - 绿色
3. 页面解析错误 - 黄色
4. 其他信息 - 白色

支持控制台和文件双重输出，文件输出不包含颜色代码。
"""

import logging
import sys
import os
from enum import Enum
from typing import Optional

# 导入配置加载器
try:
    from .config_loader import get_logger_config
    LOGGER_CONFIG = get_logger_config()
except ImportError:
    # 如果无法导入配置加载器，使用默认配置
    LOGGER_CONFIG = {
        'CONSOLE_LEVEL': 'INFO',
        'DEFAULT_LOG_FILE': '/mnt/data1/leo/spider_colored.log',
        'LOG_FORMAT': '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        'DATE_FORMAT': '%Y-%m-%d %H:%M:%S',
        'DEFAULT_LOG_ADD': False,
        'CUSTOM_LEVELS': {
            'INFO': 10,
            'HINT': 20,
            'WARN': 30,
            'ERROR': 40,
            'SUCCESS': 25,
        }
    }


class LogColor(Enum):
    """日志颜色枚举"""
    RED    = '\033[91m' # 红色 - 错误
    GREEN  = '\033[92m' # 绿色 - 正确结果
    YELLOW = '\033[93m' # 黄色 - 页面解析错误
    BLUE   = '\033[94m' # 蓝色 - 正常输出
    WHITE  = '\033[97m' # 白色 - 其他信息
    RESET  = '\033[0m'  # 重置颜色


class LogType(Enum):
    """日志类型枚举"""
    ERROR   = "error"   # IO错误：文件写入、数据库错误
    WARN    = "warn"    # 页面解析错误
    INFO    = "info"    # 其他信息
    HINT    = "hint"    # 正常输出
    SUCCESS = "success" # 正确结果


class ColoredFormatter(logging.Formatter):
    """彩色日志格式化器"""
    def __init__(self, fmt=None, datefmt=None, use_colors=True):
        super().__init__(fmt, datefmt)
        self.use_colors = use_colors

        # 颜色映射
        self.color_map = {
            # 这个必须要输出
            LogType.SUCCESS: LogColor.GREEN,
            # 错误也要输出
            LogType.ERROR: LogColor.RED,
            # 下面这些根据日志类型进行设置
            LogType.WARN: LogColor.YELLOW,
            LogType.HINT: LogColor.BLUE,
            LogType.INFO: LogColor.WHITE
        }

    def format(self, record):
        """格式化日志记录，添加颜色"""
        formatted = super().format(record)

        if not self.use_colors:
            return formatted

        # 获取日志类型
        log_type = getattr(record, 'log_type', LogType.INFO)
        color = self.color_map.get(log_type, LogColor.WHITE)

        # 添加颜色
        return f"{color.value}{formatted}{LogColor.RESET.value}"


class CustomLevelHandler(logging.StreamHandler):
    """自定义级别控制的处理器"""
    def __init__(self, stream, level_filter_func):
        super().__init__(stream)
        self.level_filter_func = level_filter_func

    def emit(self, record):
        """发送日志记录，根据自定义级别过滤"""
        # 获取日志类型
        log_type = getattr(record, 'log_type', LogType.INFO)
        log_level_name = log_type.value.upper()


        # 检查是否应该输出
        if self.level_filter_func(log_level_name):
            super().emit(record)


class CustomLevelFileHandler(logging.FileHandler):
    """自定义级别控制的文件处理器"""
    def __init__(self, filename, mode='a', encoding=None, level_filter_func=None):
        super().__init__(filename, mode, encoding)
        self.level_filter_func = level_filter_func

    def emit(self, record):
        """发送日志记录到文件，根据自定义级别过滤"""
        if self.level_filter_func:
            # 获取日志类型
            log_type = getattr(record, 'log_type', LogType.INFO)
            log_level_name = log_type.value.upper()


            # 检查是否应该写入文件
            if self.level_filter_func(log_level_name):
                super().emit(record)
        else:
            # 如果没有过滤函数，直接写入
            super().emit(record)


class ColoredLogger:
    """彩色日志记录器"""

    def __init__(self, name: str = "colored_spider", console_level: str = None,
                 log_file: str = None, log_add: bool = None):
        # 使用独特的名称避免与根日志记录器冲突
        self.logger = logging.getLogger(name)

        # 从配置中获取级别设置
        self.console_level = console_level or LOGGER_CONFIG['CONSOLE_LEVEL']
        self.custom_levels = LOGGER_CONFIG['CUSTOM_LEVELS']

        # 存储文件相关参数
        self.log_file = log_file if log_file is not None else LOGGER_CONFIG['DEFAULT_LOG_FILE']
        self.log_add = log_add if log_add is not None else LOGGER_CONFIG['DEFAULT_LOG_ADD']

        # 设置 logger 的最低级别（DEBUG，让所有消息都能进入）
        self.logger.setLevel(logging.DEBUG)

        # 防止日志传播到根日志记录器，避免重复输出
        self.logger.propagate = False

        self.file_handler_created = False

        # 避免重复添加处理器
        if not self.logger.handlers:
            self._setup_handlers()

    def _should_log_to_console(self, log_level_name: str) -> bool:
        """判断是否应该输出到控制台"""
        # SUCCESS 总是显示
        if log_level_name == 'SUCCESS':
            return True

        # 获取当前日志级别和控制台级别的数值
        current_level = self.custom_levels.get(log_level_name, 0)
        console_level = self.custom_levels.get(self.console_level, 0)

        # 只有级别大于等于控制台级别的才输出
        return current_level >= console_level

    def _should_log_to_file(self, log_level_name: str) -> bool:
        """判断是否应该写入文件"""
        # 所有级别的日志都写入文件
        _ = log_level_name  # 忽略参数，保持接口一致性
        return True

    def _setup_handlers(self):
        """设置日志处理器"""
        try:
            # 控制台处理器（带颜色）- 使用自定义过滤器
            console_handler = CustomLevelHandler(sys.stdout, self._should_log_to_console)
            console_formatter = ColoredFormatter(
                fmt=LOGGER_CONFIG['LOG_FORMAT'],
                datefmt=LOGGER_CONFIG['DATE_FORMAT'],
                use_colors=True
            )
            console_handler.setFormatter(console_formatter)
            self.logger.addHandler(console_handler)

            # 文件处理器（无颜色）
            self._setup_file_handler()

        except Exception as e:
            # 如果设置处理器失败，记录错误并退出
            print(f"Failed to setup log handlers: {e}", file=sys.stderr)
            sys.exit(1)

    def _setup_file_handler(self):
        """设置文件处理器"""
        # 如果 log_file 为 None，则不创建文件处理器
        if self.log_file is None:
            print("💡 日志配置：仅控制台输出，不写入文件", file=sys.stderr)
            return

        try:
            # 检查文件是否可写
            log_file_path = self.log_file

            # 检查目录是否存在，如果不存在则创建
            log_dir = os.path.dirname(log_file_path)
            if log_dir and not os.path.exists(log_dir):
                os.makedirs(log_dir, exist_ok=True)

            # 测试文件是否可写
            try:
                # 确定文件模式
                test_mode = 'w' if not self.log_add else 'a'
                with open(log_file_path, test_mode, encoding='utf-8') as f:
                    # 实际写入一个测试字符并立即刷新
                    f.write('')
                    f.flush()
            except (OSError, PermissionError, IOError) as e:
                print(f"⚠️  无法写入日志文件 '{log_file_path}': {e}", file=sys.stderr)
                print("💡 切换到仅控制台输出模式...", file=sys.stderr)
                return

            # 确定文件模式
            file_mode = 'w' if not self.log_add else 'a'

            # 创建自定义文件处理器（支持级别过滤）
            file_handler = CustomLevelFileHandler(log_file_path, mode=file_mode,
                                                 encoding='utf-8', level_filter_func=self._should_log_to_file)
            file_formatter = ColoredFormatter(
                fmt=LOGGER_CONFIG['LOG_FORMAT'],
                datefmt=LOGGER_CONFIG['DATE_FORMAT'],
                use_colors=False
            )
            file_handler.setFormatter(file_formatter)
            self.logger.addHandler(file_handler)
            self.file_handler_created = True
            print(f"📝 日志文件输出: {log_file_path}", file=sys.stderr)

        except Exception as e:
            # 如果文件操作失败，只输出警告，不退出程序
            print(f"⚠️  文件日志设置失败 '{self.log_file}': {e}", file=sys.stderr)
            print("💡 切换到仅控制台输出模式...", file=sys.stderr)

    def _log_with_type(self, level: int, message: str, log_type: LogType):
        """带类型的日志记录"""
        extra = {'log_type': log_type}
        self.logger.log(level, message, extra=extra)

    def error(self, message: str):
        """记录IO错误（红色）"""
        self._log_with_type(logging.ERROR, f"[ERROR] {message}", LogType.ERROR)

    def success(self, message: str):
        """记录成功信息（绿色）"""
        self._log_with_type(logging.INFO, f"[SUCCESS] {message}", LogType.SUCCESS)

    def warn(self, message: str):
        """记录解析错误（黄色）"""
        self._log_with_type(logging.WARNING, f"[PARSE ERROR] {message}", LogType.WARN)

    def hint(self, message: str):
        """记录正常输出（蓝色）"""
        self._log_with_type(logging.INFO, message, LogType.HINT)

    def info(self, message: str):
        """记录一般信息（白色）"""
        self._log_with_type(logging.INFO, message, LogType.INFO)

    def debug(self, message: str):
        """记录调试信息（白色）"""
        self._log_with_type(logging.DEBUG, message, LogType.INFO)


# 全局彩色日志实例
colored_logger = ColoredLogger()

# 全局日志配置
_global_log_file = None
_global_log_add = False
_custom_logger = None


def set_global_log_file(log_file: str, log_add: bool = False, console_level: str = None):
    """
    设置全局日志文件和控制台级别

    Args:
        log_file (str): 日志文件路径
        log_add (bool): 是否追加模式，默认 False（重新创建文件）
        console_level (str): 控制台输出级别，默认使用配置文件中的设置

    Examples:
        >>> # 设置日志文件，重新创建
        >>> set_global_log_file("/path/to/spider.log", log_add=False)

        >>> # 设置日志文件，追加模式
        >>> set_global_log_file("/path/to/spider.log", log_add=True)

        >>> # 设置日志文件和控制台级别
        >>> set_global_log_file("/path/to/spider.log", console_level='WARN')
    """
    global _global_log_file, _global_log_add, _custom_logger
    _global_log_file = log_file
    _global_log_add = log_add

    # 创建新的自定义日志记录器
    _custom_logger = create_colored_logger(console_level=console_level, log_file=log_file, log_add=log_add)


def set_console_level(console_level: str):
    """
    设置全局控制台输出级别

    Args:
        console_level (str): 控制台输出级别 ('INFO', 'HINT', 'WARN', 'ERROR')

    Examples:
        >>> # 只显示 WARN 和 ERROR 级别的日志
        >>> set_console_level('WARN')

        >>> # 显示所有级别的日志
        >>> set_console_level('INFO')
    """
    global _custom_logger
    # 创建新的日志记录器，使用新的控制台级别
    _custom_logger = create_colored_logger(console_level=console_level)


def set_console_only_logging(console_level: str = 'INFO'):
    """
    设置仅控制台输出模式，不写入文件

    Args:
        console_level (str): 控制台输出级别，默认 'INFO'

    Examples:
        >>> # 设置仅控制台输出，显示所有级别
        >>> set_console_only_logging('INFO')

        >>> # 设置仅控制台输出，只显示警告和错误
        >>> set_console_only_logging('WARN')
    """
    global _custom_logger
    # 创建仅控制台输出的日志记录器
    _custom_logger = create_colored_logger(console_level=console_level, log_file=None)


def _get_active_logger():
    """获取当前活跃的日志记录器"""
    global _custom_logger, colored_logger
    return _custom_logger if _custom_logger else colored_logger


def create_colored_logger(console_level: str = None, log_file: str = None, log_add: bool = None) -> ColoredLogger:
    """
    创建自定义的彩色日志记录器

    Args:
        console_level (str, optional): 控制台输出级别 ('INFO', 'HINT', 'WARN', 'ERROR')
        log_file (str, optional): 指定日志文件路径。如果为 None，使用默认路径
        log_add (bool, optional): 是否追加到文件。默认 False（重新创建文件）

    Returns:
        ColoredLogger: 配置好的彩色日志记录器实例

    Examples:
        >>> # 使用默认设置
        >>> logger = create_colored_logger()

        >>> # 设置控制台级别为 WARN，只显示 WARN 和 ERROR
        >>> logger = create_colored_logger(console_level='WARN')

        >>> # 指定文件，重新创建
        >>> logger = create_colored_logger(log_file="/path/to/my.log", log_add=False)

        >>> # 指定文件，追加模式
        >>> logger = create_colored_logger(log_file="/path/to/my.log", log_add=True)
    """
    import time
    unique_name = f"colored_spider_{int(time.time() * 1000)}"
    return ColoredLogger(name=unique_name, console_level=console_level, log_file=log_file, log_add=log_add)

def log_success(message: str):
    """记录成功信息 - 绿色"""
    _get_active_logger().success(message)


def log_error(message: str):
    """记录IO错误 - 红色"""
    _get_active_logger().error(message)


def log_warn(message: str):
    """记录解析错误 - 黄色"""
    _get_active_logger().warn(message)


def log_hint(message: str):
    """记录正常输出 - 蓝色"""
    _get_active_logger().hint(message)


def log_info(message: str):
    """记录一般信息 - 白色"""
    _get_active_logger().info(message)


def log_spider_result(spider_name: str, status: str, message: str = ""):
    """根据爬虫结果状态记录相应颜色的日志"""
    if status == "success":
        pass
        # log_success(f"{spider_name} spider completed successfully")
    else:
        message_lower = message.lower()
        # SQL和JSON错误 - 黄色
        if any(keyword in message_lower for keyword in ['sql', 'mysql', 'database', 'json', 'decode', 'parse', 'html', 'xml']):
            log_warn(f"{spider_name} spider failed: {message}")
        # IO相关错误 - 红色
        elif any(keyword in message_lower for keyword in ['file', 'write', 'save', 'permission', 'access', 'io', 'connection']):
            log_error(f"{spider_name} spider failed: {message}")
        # 超时等其他状态 - 蓝色
        elif status in ["timeout", "warning"]:
            log_hint(f"{spider_name} spider {status}: {message}")
        # 其他错误 - 红色
        else:
            log_error(f"{spider_name} spider failed: {message}")


def log_database_operation(operation: str, success: bool, message: str = ""):
    """记录数据库操作结果"""
    if success:
        log_success(f"Database {operation} completed successfully")
    else:
        log_error(f"Database {operation} failed: {message}")


def log_file_operation(operation: str, filename: str, success: bool, message: str = ""):
    """记录文件操作结果"""
    if success:
        log_success(f"File {operation} completed: {filename}")
    else:
        log_error(f"File {operation} failed for {filename}: {message}")


def log_exception(context: str, exception: Exception):
    """智能记录异常信息，根据异常类型自动选择颜色"""
    error_msg = str(exception).lower()
    error_type = type(exception).__name__.lower()

    # 构建完整的错误信息
    if context:
        full_message = f"{context}: {exception}"
    else:
        full_message = str(exception)

    # SQL和JSON错误 - 黄色
    if any(keyword in error_type for keyword in ['sql', 'json', 'yaml']) or \
       any(keyword in error_msg for keyword in ['sql', 'mysql', 'database', 'json', 'decode', 'parse', 'html', 'xml']):
        log_warn(full_message)
    # IO相关错误 - 红色
    elif any(keyword in error_type for keyword in ['oserror', 'ioerror', 'permission', 'filenotfound', 'clienterror']) or \
         any(keyword in error_msg for keyword in ['file', 'write', 'save', 'permission', 'access', 'connection']):
        log_error(full_message)
    # 超时错误 - 蓝色
    elif 'timeout' in error_type or 'timeout' in error_msg:
        log_hint(full_message)
    # 其他错误 - 红色
    else:
        log_error(full_message)


# 使用示例
if __name__ == "__main__":
    # 测试不同类型的日志输出
    log_success("爬虫执行成功")
    log_error("数据库连接失败")
    log_warn("页面解析出错")
    log_info("系统启动中...")

    # 测试爬虫结果日志
    log_spider_result("Microsoft", "success")
    log_spider_result("Google", "error", "database connection failed")
    log_spider_result("Amazon", "error", "html parse error")
    log_spider_result("Tesla", "timeout", "request timeout")
