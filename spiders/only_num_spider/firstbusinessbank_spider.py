import json
import random
import time
from datetime import datetime
import logging
from urllib.parse import urlparse
from concurrent.futures import ThreadPoolExecutor, as_completed
from spiders.core_spider.base_spider import BaseSpider
from utils.country_utils import CountryCodeUtils
from utils.http_utils.http_client import HTTPClient
from bs4 import BeautifulSoup
import re
from lxml import etree
class FirstbusinessbankSpider(BaseSpider):
    def __init__(self, company_name, base_url, company_url, cwiq_code):
        super().__init__(company_name, base_url, company_url, cwiq_code)
        self.company_name = company_name
        self.company_url = company_url
        self.cwiq_code = cwiq_code
        self.country_utils = CountryCodeUtils()
        self.headers = {
    'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
    'accept-language': 'zh-CN,zh;q=0.9',
    'cache-control': 'max-age=0',
    'priority': 'u=0, i',
    'referer': 'https://jobs.jobvite.com/firstbusinessbank/jobs/positions',
    'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"',
    'sec-ch-ua-mobile': '?0',
    'sec-ch-ua-platform': '"Windows"',
    'sec-fetch-dest': 'document',
    'sec-fetch-mode': 'navigate',
    'sec-fetch-site': 'same-origin',
    'sec-fetch-user': '?1',
    'upgrade-insecure-requests': '1',
    'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36',
    # 'cookie': '_ga=GA1.1.********.**********; _ga_R9LH3G05ES=GS1.1.**********.2.0.**********.0.0.0; apt.uid=AP-G2NZN1QKA9C9-2-*************-********.0.0; _fbp=fb.1.*************.671525035805402623; _RCRTX03=aee8f92300a611f084fa0fb2f84c7c63acbcbafd9ada4d6e9d7e9913df83b777; _RCRTX03-samesite=aee8f92300a611f084fa0fb2f84c7c63acbcbafd9ada4d6e9d7e9913df83b777; AWSALBAPP-0=_remove_; AWSALBAPP-1=_remove_; AWSALBAPP-2=_remove_; AWSALBAPP-3=_remove_; OTGPPConsent=DBABLA~BVQqAAAACgA.QA; OptanonAlertBoxClosed=2025-05-08T07:45:46.844Z; OptanonConsent=isGpcEnabled=0&datestamp=Thu+May+08+2025+17%3A57%3A43+GMT%2B0800+(%E4%B8%AD%E5%9B%BD%E6%A0%87%E5%87%86%E6%97%B6%E9%97%B4)&version=202405.1.0&browserGpcFlag=0&isIABGlobal=false&hosts=&consentId=30d87b95-6f3f-4790-a2b3-19342136916c&interactionCount=1&isAnonUser=1&landingPath=NotLandingPage&GPPCookiesCount=1&groups=C0001%3A1%2CC0002%3A1%2CSSPD_BG%3A1%2CC0004%3A1&intType=3&geolocation=US%3B&AwaitingReconsent=false; wwwcsrf=a145f382-7567-48ed-bf64-0f687fc81f97; __cf_bm=TlZX5ABlXGTFqVBGhto6v1NQQZr1IUZJ1kwtOpePT.M-1747216558-*******-RndanTdku7kjcW8OI.Gzslh5zinr9KCXO1dsErdId5QFaTmcm3hjakAw079RO7sdyQIM6vkblKztdxvYyfB.1RFztR.sZM.4eqpwFzeY0xY',
}
        # 创建 HTTP 客户端
        self.client = HTTPClient()

    @classmethod
    def create_spider(cls, company_name, base_url, company_url, cwiq_code):
        """工厂方法创建 MedtronicSpider 实例"""
        return cls(company_name, base_url, company_url, cwiq_code)

    def make_request(self, method, url, company_name, requset_count, **kwargs):
        """发送请求的通用方法"""
        kwargs['headers'] = self.headers
        return self.client.request(method, url, company_name, requset_count, log_ip=False, **kwargs)

    def get_total_jobs(self):
        """获取总职位数"""
        try:
            params = {
                'c': '',
                'l': '',
                'd': '',
            }
            response = self.make_request('GET', self.base_url, self.company_name, requset_count=3, params=params)
            tree = etree.HTML(response.text)
            total = tree.xpath(
                "//div[@class='jv-wrapper']/div[@class='jv-pagination']/div[@class='jv-pagination-text']//text()")[0]
            match = re.search(r'of\s+(\d+)', total)
            if match:
                number = match.group(1)
            return number
        except Exception as e:
            logging.error(f"Error getting total jobs: {e} for {self.company_name}")
            return 0

    def parse_job(self, job_data):
        """解析职位数据"""
        pass

    def fetch_jobs(self,max_workers):
        """获取所有职位信息"""
        try:
            total_jobs = self.get_total_jobs()
            if total_jobs == 0:
                logging.info(f"No jobs found {self.company_name}")
                return [], 0

            logging.info(f"Found {total_jobs} total jobs")

            return [], total_jobs

        except Exception as e:
            logging.error(f"Error in fetch_jobs: {e}:{self.company_name}")
            return [], 0


    def __del__(self):
        """确保 HTTP 客户端被正确关闭"""
        if hasattr(self, 'client'):
            self.client.close()

