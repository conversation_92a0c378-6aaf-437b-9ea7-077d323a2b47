#!/usr/bin/env python3
"""
彩色日志测试脚本

演示不同类型的日志输出效果：
1. IO错误（文件写入、数据库错误）- 红色
2. 正确结果 - 绿色  
3. 页面解析错误 - 黄色
4. 其他信息 - 白色
"""

import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utils.colored_logger import (
    log_success, log_io_error, log_parse_error, log_info,
    log_spider_result, log_database_operation, log_file_operation
)


def test_basic_colored_logs():
    """测试基础彩色日志功能"""
    print("=" * 60)
    print("测试基础彩色日志功能")
    print("=" * 60)
    
    # 测试成功信息（绿色）
    log_success("爬虫系统启动成功")
    log_success("配置文件加载完成")
    log_success("数据库连接建立成功")
    
    # 测试IO错误（红色）
    log_io_error("数据库连接失败: Connection refused")
    log_io_error("文件写入失败: Permission denied")
    log_io_error("MySQL查询错误: Table doesn't exist")
    
    # 测试解析错误（黄色）
    log_parse_error("HTML页面解析失败: Invalid HTML structure")
    log_parse_error("JSON数据解析错误: Unexpected token")
    log_parse_error("XML格式错误: Missing closing tag")
    
    # 测试一般信息（白色）
    log_info("系统正在初始化...")
    log_info("开始处理爬虫任务")
    log_info("等待网络响应")


def test_spider_result_logs():
    """测试爬虫结果日志"""
    print("\n" + "=" * 60)
    print("测试爬虫结果日志")
    print("=" * 60)
    
    # 测试成功的爬虫
    log_spider_result("Microsoft", "success")
    log_spider_result("Google", "success")
    
    # 测试IO错误的爬虫
    log_spider_result("Amazon", "error", "database connection failed")
    log_spider_result("Tesla", "error", "file write error occurred")
    log_spider_result("Apple", "error", "MySQL server has gone away")
    
    # 测试解析错误的爬虫
    log_spider_result("Facebook", "error", "html parse error in response")
    log_spider_result("Netflix", "error", "json decode error")
    log_spider_result("Spotify", "error", "xml parsing failed")
    
    # 测试其他错误的爬虫
    log_spider_result("Uber", "timeout", "request timeout after 30s")
    log_spider_result("Airbnb", "error", "unknown network error")


def test_database_operation_logs():
    """测试数据库操作日志"""
    print("\n" + "=" * 60)
    print("测试数据库操作日志")
    print("=" * 60)
    
    # 测试成功的数据库操作
    log_database_operation("insert", True, "Successfully inserted 1000 records")
    log_database_operation("update", True, "Updated 500 job records")
    log_database_operation("query", True, "Retrieved 2000 company records")
    
    # 测试失败的数据库操作
    log_database_operation("connection", False, "Failed to connect to MySQL server")
    log_database_operation("insert", False, "Duplicate key error")
    log_database_operation("backup", False, "Insufficient disk space")


def test_file_operation_logs():
    """测试文件操作日志"""
    print("\n" + "=" * 60)
    print("测试文件操作日志")
    print("=" * 60)
    
    # 测试成功的文件操作
    log_file_operation("create", "spider_config.json", True)
    log_file_operation("write", "job_data.csv", True, "Written 5000 records")
    log_file_operation("backup", "database_backup.sql", True)
    
    # 测试失败的文件操作
    log_file_operation("create", "restricted_file.log", False, "Permission denied")
    log_file_operation("write", "large_file.dat", False, "Disk space insufficient")
    log_file_operation("read", "missing_file.txt", False, "File not found")


def test_mixed_scenario():
    """测试混合场景"""
    print("\n" + "=" * 60)
    print("测试混合场景 - 模拟真实爬虫执行")
    print("=" * 60)
    
    # 模拟爬虫系统启动
    log_info("Marzaha爬虫系统启动中...")
    log_success("日志系统初始化完成")
    log_success("加载了16,000个爬虫配置")
    
    # 模拟爬虫执行过程
    log_info("开始并发执行爬虫任务...")
    log_success("Microsoft爬虫执行成功，获取到245个职位")
    log_success("Google爬虫执行成功，获取到189个职位")
    
    # 模拟一些错误
    log_parse_error("Amazon爬虫页面结构变化，解析失败")
    log_io_error("Tesla爬虫数据库写入失败")
    log_info("Apple爬虫请求超时，将重试")
    
    # 模拟数据库操作
    log_database_operation("optimize_table", True, "jobs表优化完成")
    log_database_operation("generate_report", True, "生成Parquet报告")
    
    # 模拟系统完成
    log_success("所有处理完成，总耗时: 3600.5秒")
    log_success("成功率: 85.6% (13,680/16,000)")


def main():
    """主函数"""
    print("🎨 Marzaha 爬虫系统彩色日志测试")
    print("📋 颜色说明:")
    print("   🔴 红色 - IO错误（文件、数据库错误）")
    print("   🟢 绿色 - 正确结果")
    print("   🟡 黄色 - 页面解析错误")
    print("   ⚪ 白色 - 其他信息")
    print()
    
    # 运行所有测试
    test_basic_colored_logs()
    test_spider_result_logs()
    test_database_operation_logs()
    test_file_operation_logs()
    test_mixed_scenario()
    
    print("\n" + "=" * 60)
    print("✅ 彩色日志测试完成")
    print("💡 提示: 在支持ANSI颜色的终端中可以看到彩色效果")
    print("📁 日志文件: /mnt/data1/leo/spider_colored.log (无颜色)")
    print("=" * 60)


if __name__ == "__main__":
    main()
