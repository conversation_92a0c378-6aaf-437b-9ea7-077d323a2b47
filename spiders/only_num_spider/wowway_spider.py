import json
import random
import time
from datetime import datetime
import logging
from urllib.parse import urlparse
from concurrent.futures import ThreadPoolExecutor, as_completed
from spiders.core_spider.base_spider import BaseSpider
from utils.country_utils import CountryCodeUtils
from utils.http_utils.http_client import HTTPClient
from bs4 import BeautifulSoup
import re
from lxml import etree
class WowwaySpider(BaseSpider):
    def __init__(self, company_name, base_url, company_url, cwiq_code):
        super().__init__(company_name, base_url, company_url, cwiq_code)
        self.company_name = company_name
        self.company_url = company_url
        self.cwiq_code = cwiq_code
        self.country_utils = CountryCodeUtils()
        self.headers = {
    'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
    'accept-language': 'zh-CN,zh;q=0.9',
    'cache-control': 'max-age=0',
    'if-none-match': 'W/"ab4d13e8d0c7057c6f29e710c04463bd"',
    'priority': 'u=0, i',
    'referer': 'https://careers.wowway.com/jobs/J3S6CX79348ZGPM6Z4N',
    'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"',
    'sec-ch-ua-mobile': '?0',
    'sec-ch-ua-platform': '"Windows"',
    'sec-fetch-dest': 'document',
    'sec-fetch-mode': 'navigate',
    'sec-fetch-site': 'same-origin',
    'sec-fetch-user': '?1',
    'upgrade-insecure-requests': '1',
    'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36',
    # 'cookie': 'session_rails=9a066d5b5608b1eff6c7572772fb8343; BID=fb761e066c8a3d85406e7d902afdd31a; VID=VXCW711F7A52F24956426B0F77A5F68635E2715C; i18n_locale=en-RM; last_search_rails=%7B%22keywords%22%3A%22%22%2C%22location%22%3A%22%22%2C%22pay%22%3A%22%22%2C%22emp%22%3A%22%22%2C%22languages%22%3A%22%22%7D; work_from_home_remote=false; last_searched_location=; activity=%7B%22last_hit_at%22%3A1747981595%2C%22last_visit_at%22%3A0%7D; last_jdp_viewed_rails=%7B%22keywords%22%3A%22Information+Technology+%28IT%29+Support+Specialist%22%2C%22location%22%3A%22Augusta%2C+GA%22%2C%22languages%22%3Anull%7D',
}

        # 创建 HTTP 客户端
        self.client = HTTPClient()

    @classmethod
    def create_spider(cls, company_name, base_url, company_url, cwiq_code):
        """工厂方法创建 MedtronicSpider 实例"""
        return cls(company_name, base_url, company_url, cwiq_code)

    def make_request(self, method, url, company_name, requset_count, **kwargs):
        """发送请求的通用方法"""
        kwargs['headers'] = self.headers
        return self.client.request(method, url, company_name, requset_count, log_ip=False, **kwargs)

    def get_total_jobs(self):
        """获取总职位数"""
        try:
            params = {
                'keywords': '',
                'location': '',
            }
            response = self.make_request('GET', self.base_url, self.company_name, requset_count=3,params=params)
            tree = etree.HTML(response.text)
            total = tree.xpath(
                "//div[@class='col small']/div[@id='col-fixed']/div[@id='jrp-data']/div[@class='jrp-jobs-search-result-section']/div[@id='jobs-found']/div[@class='col-2']/div[@id='job-count']/h1[@class='fz1rem']//text()")[
                0]
            result = re.findall(r'\d+', total)
            total = int(result[0])
            return total
        except Exception as e:
            logging.error(f"Error getting total jobs: {e} for {self.company_name}")
            return 0

    def parse_job(self, job_data):
        """解析职位数据"""
        pass

    def fetch_jobs(self,max_workers):
        """获取所有职位信息"""
        try:
            total_jobs = self.get_total_jobs()
            if total_jobs == 0:
                logging.info(f"No jobs found {self.company_name}")
                return [], 0

            logging.info(f"Found {total_jobs} total jobs")

            return [], total_jobs

        except Exception as e:
            logging.error(f"Error in fetch_jobs: {e}:{self.company_name}")
            return [], 0


    def __del__(self):
        """确保 HTTP 客户端被正确关闭"""
        if hasattr(self, 'client'):
            self.client.close()

