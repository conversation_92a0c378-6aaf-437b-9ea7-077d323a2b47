import json
import time
from datetime import datetime
import logging
from spiders.core_spider.base_spider import Base<PERSON>pider
from utils.country_utils import CountryCodeUtils
from utils.http_utils.http_client import HTTPClient
from bs4 import BeautifulSoup
import re


class GreenHouseSpider(BaseSpider):
    def __init__(self, company_name, base_url, company_url, cwiq_code):
        super().__init__(company_name, base_url, company_url, cwiq_code)
        self.company_name = company_name
        self.company_url = company_url
        self.cwiq_code = cwiq_code
        self.country_utils = CountryCodeUtils()
        self.headers = {
            "Content-Type": "application/json",
            "Accept": "application/json",
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
        }
        # 创建 HTTP 客户端
        self.client = HTTPClient()

    @classmethod
    def create_spider(cls, company_name, base_url, company_url, cwiq_code):
        """工厂方法创建 MedtronicSpider 实例"""
        return cls(company_name, base_url, company_url, cwiq_code)

    def make_request(self, method, url, company_name,requset_count, **kwargs):
        """发送请求的通用方法"""
        kwargs['headers'] = self.headers
        return self.client.request(method, url, company_name, requset_count,log_ip=False, **kwargs)

    def parse_html(self, page_num=1):
        """解析html内容"""
        failed_requests_list = []
        params = {
            "page": {page_num},
        }
        try:
            response = self.make_request('GET', self.base_url, self.company_name,requset_count=3, params=params)
        except Exception as e:
            logging.error(f"Failed to make request: {e} for {self.company_name}")
            failed_requests_list.append({
                "method": 'GET',
                "url": self.base_url,
                "company_name": self.company_name,
                "payload": params,
            })
            return [], failed_requests_list

        try:
            # 解析 HTML 内容
            soup = BeautifulSoup(response.text, "html.parser")

            # 查找所有 <script> 标签
            script_tags = soup.find_all("script")

            # 遍历 <script> 标签，寻找包含 "window.__remixContext =" 的内容
            remix_context_content = None
            for script in script_tags:
                if script.string and "window.__remixContext =" in script.string:
                    remix_context_content = script.string
                    break

            if remix_context_content:
                try:
                    # 提取 "window.__remixContext =" 后的 JSON 数据
                    match = re.search(r"window\.\__remixContext = (\{.*\});", remix_context_content)
                    if match:
                        json_content = match.group(1)  # 提取 JSON 内容部分

                        # 将 JSON 内容解析为 Python 对象
                        remix_context_data = json.loads(json_content)['state']['loaderData']['routes/$url_token'][
                            'jobPosts']
                        return remix_context_data, failed_requests_list
                except json.JSONDecodeError as e:
                    logging.error(f"Error decoding JSON: {e} for {self.company_name}")
                    return [], failed_requests_list
            else:
                return [], failed_requests_list

        except Exception as e:
            logging.error(f"Error getting html: {e} for {self.company_name}")
            return [], failed_requests_list

    def parse_job(self, job_data):
        """解析职位数据"""
        try:
            if job_data:
                job_id = str(job_data.get("id"))
                # 解析地点信息
                location = job_data.get("location", "")
                # country_name = job_data.get("PrimaryLocationCountry", {})
                country_code = location.split(",")[-1] if location else ""
                city = location.split(",")[0] if location else ""
                state = location.split(",")[1] if location and len(location.split(",")) == 3 else ""
                return {
                    "jobId": job_id,
                    "company": self.company_name,
                    "cwiq_code": self.cwiq_code,
                    "companyUrl": self.company_url,
                    "country": country_code,
                    "city": city,
                    "jobTitle": job_data.get("title", ""),
                    # "department": job_info.get("hiringOrganization", {}).get("name", ""),
                    "location": state,
                    "jobDescription": job_data.get("content", ""),
                    "jobUrl": job_data.get("absolute_url", ""),
                    "created_time": None,
                    "updated_time": None,
                    "post_time": datetime.now(),
                }
        except Exception as e:
            logging.error(f"Error parsing job data: {e} for {self.company_name}")
            return None

    def get_total_jobs(self):
        """获取总职位数"""
        try:
            return self.parse_html()
        except Exception as e:
            logging.error(f"Error getting total jobs: {e}")
            return 0

    def fetch_jobs(self, max_workers):
        """获取greenhouse职位列表"""
        all_jobs = []
        fail_jobs = []
        try:
            # 获取总数据
            job_list, failed_requests_list = self.get_total_jobs()
            if job_list:
                total_jobs = job_list.get("total", 0)
                total_pages = job_list.get("total_pages", 1)
            else:
                total_jobs = 0
                total_pages = 1

            if total_jobs == 0:
                logging.info(f"No jobs found {self.company_name}")
                return [],0

            logging.info(f"Found {total_jobs} total jobs")

            # 遍历每一页
            for page_num in range(1, total_pages + 1):
                try:
                    response, failed_requests_list = self.parse_html(page_num)
                    data = response.get("data", [])

                    # 先获取当前页的职位数量再处理
                    page_jobs = [self.parse_job(job) for job in data]
                    all_jobs.extend(page_jobs)

                    # 使用当前页jobs数量显示进度
                    if total_jobs > 0:  # 避免除以零
                        progress = (len(all_jobs) / total_jobs) * 100
                        logging.info(f"Progress: {len(page_jobs)}/{total_jobs} jobs fetched ({progress:.2f}%)")

                except Exception as e:
                    logging.error(f"Error processing page {page_num + 1}: {str(e)}")
                    # if failed_requests_list:
                    #     for failed_request in failed_requests_list:
                    #         fail_jobs = self.retry_request(failed_request['method'], failed_request['url'],
                    #                                        failed_request['company_name'], retries=3, delay=5,
                    #                                        json=failed_request['payload'])

            logging.info(f"Successfully fetched {len(all_jobs)} jobs")
            all_jobs = all_jobs + fail_jobs
            return all_jobs,"no total_jobs"

        except Exception as e:
            logging.error(f"Error in fetch_jobs: {e}:{self.company_name}")
            return [],0

    # def retry_request(self, method, url, company_name, retries=3, delay=5, **kwargs):
    #     attempt = 0
    #     fail_jobs = []
    #
    #     while attempt < retries:
    #         try:
    #             response = self.make_request(method, url, company_name, **kwargs)
    #             # 解析 HTML 内容
    #             soup = BeautifulSoup(response.text, "html.parser")
    #
    #             # 查找所有 <script> 标签
    #             script_tags = soup.find_all("script")
    #
    #             # 遍历 <script> 标签，寻找包含 "window.__remixContext =" 的内容
    #             remix_context_content = None
    #             for script in script_tags:
    #                 if script.string and "window.__remixContext =" in script.string:
    #                     remix_context_content = script.string
    #                     break
    #
    #             if remix_context_content:
    #                 # 提取 "window.__remixContext =" 后的 JSON 数据
    #                 match = re.search(r"window\.\__remixContext = (\{.*\});", remix_context_content)
    #                 if match:
    #                     json_content = match.group(1)  # 提取 JSON 内容部分
    #
    #                     # 将 JSON 内容解析为 Python 对象
    #                     remix_context_data = json.loads(json_content)['state']['loaderData']['routes/$url_token'][
    #                         'jobPosts']
    #                     if remix_context_data:
    #                         data = response.get("data", [])
    #
    #                         # 先获取当前页的职位数量再处理
    #                         page_jobs = [self.parse_job(job) for job in data]
    #                         fail_jobs.extend(page_jobs)
    #                         return fail_jobs
    #
    #         except Exception as e:
    #             attempt += 1
    #             logging.error(f"Error on attempt {attempt} for {company_name}: {e}")
    #             if attempt < retries:
    #                 logging.info(f"Retrying in {delay} seconds...")
    #             else:
    #                 logging.error(f"All {retries} attempts failed for {company_name}")
    #                 return None

    def __del__(self):
        """确保 HTTP 客户端被正确关闭"""
        if hasattr(self, 'client'):
            self.client.close()
