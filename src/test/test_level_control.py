#!/usr/bin/env python3
"""
日志级别控制测试脚本 - test_level_control.py

测试新的日志级别控制功能：
1. 自定义级别：INFO -> HINT -> WARN -> ERROR（从低到高）
2. 控制台级别过滤
3. 文件级别过滤
4. settings.py 配置读取
"""

import os
import sys
import tempfile

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.colored_logger import (
    set_global_log_file, set_console_level, log_success, log_error,
    log_warn, log_hint, log_info, create_colored_logger
)
from utils.config_loader import get_logger_config


def test_default_settings():
    """测试默认设置"""
    print("\n" + "=" * 60)
    print("测试1: 默认设置")
    print("=" * 60)
    logger_config = get_logger_config()
    print(f"默认控制台级别: {logger_config['CONSOLE_LEVEL']}")
    print(f"自定义级别映射: {logger_config['CUSTOM_LEVELS']}")
    print()

    # 测试所有级别的日志
    print("测试所有级别的日志输出:")
    log_info("这是 INFO 级别的日志（级别: 10）")
    log_hint("这是 HINT 级别的日志（级别: 20）")
    log_success("这是 SUCCESS 级别的日志（级别: 25，总是显示）")
    log_warn("这是 WARN 级别的日志（级别: 30）")
    log_error("这是 ERROR 级别的日志（级别: 40）")

    print("✅ 默认设置测试完成")


def test_console_level_filtering():
    """测试控制台级别过滤"""
    print("\n" + "=" * 60)
    print("测试2: 控制台级别过滤")
    print("=" * 60)

    # 测试不同的控制台级别
    levels = ['INFO', 'HINT', 'WARN', 'ERROR']

    for level in levels:
        print(f"\n设置控制台级别为: {level}")
        print(f"预期显示: {level} 及以上级别的日志")
        set_console_level(level)

        print("测试输出:")
        log_info("INFO 级别日志")
        log_hint("HINT 级别日志")
        log_warn("WARN 级别日志")
        log_error("ERROR 级别日志")
        log_success("SUCCESS 级别日志（总是显示）")
        print()

    print("✅ 控制台级别过滤测试完成")


def test_file_logging_with_levels():
    """测试文件日志级别控制"""
    print("\n" + "=" * 60)
    print("测试3: 文件日志级别控制")
    print("=" * 60)

    # 创建临时文件
    temp_file = tempfile.mktemp(suffix='.log', prefix='level_test_')
    print(f"日志文件: {temp_file}")

    # 设置日志文件和控制台级别
    set_global_log_file(temp_file, log_add=False, console_level='WARN')
    print("设置控制台级别为 WARN，文件级别为 INFO（默认）")
    print("预期：控制台只显示 WARN、ERROR 和 SUCCESS，文件记录所有级别")

    print("\n测试输出:")
    log_info("INFO 级别日志 - 应该只写入文件")
    log_hint("HINT 级别日志 - 应该只写入文件")
    log_warn("WARN 级别日志 - 控制台和文件都有")
    log_error("ERROR 级别日志 - 控制台和文件都有")
    log_success("SUCCESS 级别日志 - 控制台和文件都有")

    print("\n文件内容:")
    with open(temp_file, 'r', encoding='utf-8') as f:
        lines = f.readlines()
        for i, line in enumerate(lines, 1):
            print(f"  {i:2d}: {line.strip()}")

    # 清理
    os.unlink(temp_file)
    print("✅ 文件日志级别控制测试完成")


def test_custom_logger_instances():
    """测试自定义日志记录器实例"""
    print("\n" + "=" * 60)
    print("测试4: 自定义日志记录器实例")
    print("=" * 60)

    # 创建不同级别的日志记录器
    temp_file1 = tempfile.mktemp(suffix='.log', prefix='custom1_')
    temp_file2 = tempfile.mktemp(suffix='.log', prefix='custom2_')

    print("创建两个不同级别的日志记录器:")
    logger1 = create_colored_logger(console_level='INFO', log_file=temp_file1, log_add=False)
    logger2 = create_colored_logger(console_level='ERROR', log_file=temp_file2, log_add=False)

    print("\nLogger1 (控制台级别: INFO):")
    logger1.info("Logger1 - INFO 级别")
    logger1.hint("Logger1 - HINT 级别")
    logger1.warn("Logger1 - WARN 级别")
    logger1.error("Logger1 - ERROR 级别")
    logger1.success("Logger1 - SUCCESS 级别")

    print("\nLogger2 (控制台级别: ERROR):")
    logger2.info("Logger2 - INFO 级别")
    logger2.hint("Logger2 - HINT 级别")
    logger2.warn("Logger2 - WARN 级别")
    logger2.error("Logger2 - ERROR 级别")
    logger2.success("Logger2 - SUCCESS 级别")

    print(f"\nLogger1 文件内容 ({temp_file1}):")
    with open(temp_file1, 'r', encoding='utf-8') as f:
        for i, line in enumerate(f.readlines(), 1):
            print(f"  {i:2d}: {line.strip()}")

    print(f"\nLogger2 文件内容 ({temp_file2}):")
    with open(temp_file2, 'r', encoding='utf-8') as f:
        for i, line in enumerate(f.readlines(), 1):
            print(f"  {i:2d}: {line.strip()}")

    # 清理
    os.unlink(temp_file1)
    os.unlink(temp_file2)
    print("✅ 自定义日志记录器实例测试完成")


def test_file_write_failure():
    """测试文件写入失败的处理"""
    print("\n" + "=" * 60)
    print("测试5: 文件写入失败处理")
    print("=" * 60)

    # 尝试写入到不存在的目录
    invalid_file = "/nonexistent/directory/test.log"
    print(f"尝试设置无效文件: {invalid_file}")
    print("预期行为: 显示警告信息，继续使用控制台日志")

    set_global_log_file(invalid_file, log_add=False, console_level='INFO')

    print("\n测试日志输出:")
    log_info("测试信息 - 应该只在控制台显示")
    log_warn("测试警告 - 应该只在控制台显示")
    log_error("测试错误 - 应该只在控制台显示")

    print("✅ 文件写入失败处理测试完成")


def main():
    """主函数"""
    print("🎨 Marzaha 爬虫系统 - 日志级别控制测试")
    print("📋 功能特性:")
    print("   ✅ 自定义级别：INFO(10) -> HINT(20) -> WARN(30) -> ERROR(40)")
    print("   ✅ SUCCESS(25) 总是显示")
    print("   ✅ 控制台级别过滤")
    print("   ✅ 文件级别过滤")
    print("   ✅ settings.py 配置读取")
    print("   ✅ 文件写入失败时优雅处理")
    print()

    # 运行所有测试
    test_default_settings()
    test_console_level_filtering()
    test_file_logging_with_levels()
    test_custom_logger_instances()
    test_file_write_failure()

    print("\n" + "=" * 60)
    print("✅ 所有日志级别控制测试完成")
    print("💡 使用方法:")
    print("   1. 设置控制台级别: set_console_level('WARN')")
    print("   2. 设置文件和级别: set_global_log_file('/path/to/log', console_level='ERROR')")
    print("   3. 创建自定义实例: create_colored_logger(console_level='HINT')")
    print("   4. 在 settings.py 中配置默认级别")
    print("=" * 60)


if __name__ == "__main__":
    main()
