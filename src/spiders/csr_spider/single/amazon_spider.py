from datetime import datetime
import logging
from spiders.core_spider.base_spider import Base<PERSON>pider
from utils.http_utils.http_client import HTT<PERSON>lient
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed


class AmazonSpider(BaseSpider):
    def __init__(self, company_name, base_url, company_url, cwiq_code):
        super().__init__(company_name, base_url, company_url, cwiq_code)
        self.company_name = company_name
        self.company_url = company_url
        self.cwiq_code = cwiq_code
        self.headers = {
            "Accept": "application/json, text/plain, */*",
            "Sec-CH-UA": '"Chromium";v="130", "Google Chrome";v="130", "Not?A_Brand";v="99"',
            "Sec-CH-UA-Mobile": "?0",
            "Sec-CH-UA-Platform": '"macOS"',
            "User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHT<PERSON>, like Gecko) Chrome/130.0.0.0 Safari/537.36"
        }
        # 创建 HTTP 客户端
        self.client = HTTPClient()

    @classmethod
    def create_spider(cls, company_name, base_url, company_url, cwiq_code):
        """工厂方法创建 AmazonSpider 实例"""
        return cls(company_name, base_url, company_url, cwiq_code)

    def make_request(self, method, url, company_name, requset_count, **kwargs):
        """发送请求的通用方法"""
        kwargs['headers'] = self.headers
        return self.client.request(method, url, company_name, requset_count, log_ip=False, **kwargs)

    def parse_job(self, job_data):
        """解析单个职位数据"""
        try:
            if job_data:
                state = job_data["location"].split(",")[1] if job_data["location"] and len(
                    job_data["location"].split(",")) == 3 else ""
            return {
                "jobId": job_data["id_icims"],
                "company": self.company_name,
                "cwiq_code": self.cwiq_code,
                "companyUrl": self.company_url,
                "country": job_data["country_code"],
                "city": job_data["city"],
                "jobTitle": job_data["title"],
                "department": job_data["job_family"],
                "location": state,
                "jobDescription": job_data["description"],
                "jobUrl": "https://www.amazon.jobs" + job_data.get("job_path", ""),
                "created_time": None,
                "updated_time": None,
                "post_time": datetime.now()
            }
        except Exception as e:
            logging.error(f"解析职位数据出错: {e} for {self.company_name}")

    def get_state_list(self):
        """获取亚马逊州列表"""
        response = self.make_request("GET",
                                     'https://www.amazon.jobs/en/search.json?radius=24km&facets%5B%5D=normalized_country_code&facets%5B%5D=normalized_state_name&facets%5B%5D=normalized_city_name&facets%5B%5D=location&facets%5B%5D=business_category&facets%5B%5D=category&facets%5B%5D=schedule_type_id&facets%5B%5D=employee_class&facets%5B%5D=normalized_location&facets%5B%5D=job_function_id&facets%5B%5D=is_manager&facets%5B%5D=is_intern&offset=0&result_limit=10&sort=relevant&latitude=&longitude=&loc_group_id=&loc_query=&base_query=&city=&country=&region=&county=&query_options=&',
                                     self.company_name, requset_count=3,
                                     )
        for item in response.json()['facets']['normalized_state_name_facet']:
            yield item

    def fetch_jobs(self, max_workers):
        """获取亚马逊职位列表"""
        all_jobs = []
        failed_requests_list = []
        fail_jobs = []

        def fetch_jobs_for_offset(offset, state):
            """为指定的 offset 获取职位"""
            url = (
                "https://www.amazon.jobs/en/search.json?"
                f"normalized_state_name%5B%5D={state}&"
                "radius=24km&"
                "facets%5B%5D=normalized_country_code&"
                "facets%5B%5D=normalized_state_name&"
                "facets%5B%5D=normalized_city_name&"
                "facets%5B%5D=location&"
                "facets%5B%5D=business_category&"
                "facets%5B%5D=category&"
                "facets%5B%5D=schedule_type_id&"
                "facets%5B%5D=employee_class&"
                "facets%5B%5D=normalized_location&"
                "facets%5B%5D=job_function_id&"
                "facets%5B%5D=is_manager&"
                "facets%5B%5D=is_intern&"
                f"offset={offset}&"
                "result_limit=100&"
                "sort=relevant"
            )

            try:
                response = self.make_request('GET', url, self.company_name, requset_count=3, )
                data = response.json()

                if data.get("error") is not None:
                    logging.info(f"No jobs found for offset {offset}")
                    return []

                jobs = [self.parse_job(job) for job in data["jobs"]]
                logging.info(f"Successfully scraped {len(jobs)} jobs from offset {offset}")
                return jobs

            except Exception as e:
                logging.error(f"Error fetching jobs from offset {offset}: {str(e)}")
                failed_requests_list.append({
                    "method": 'GET',
                    "url": url,
                    "company_name": self.company_name,
                })

                return []

        try:
            states = self.get_state_list()
            for state in states:
                state_name = list(state.keys())[0]  # 获取州名
                num = list(state.values())[0]  # 获取对应的职位数量

                # 使用多线程抓取职位
                with ThreadPoolExecutor(max_workers=10) as executor:
                    # 提交任务到线程池
                    future_to_offset = {executor.submit(fetch_jobs_for_offset, offset, state_name): offset for offset in
                                        range(0, num, 100)}

                    for future in as_completed(future_to_offset):
                        jobs = future.result()
                        all_jobs.extend(jobs)

                # if failed_requests_list:
                #     for failed_request in failed_requests_list:
                #         fail_jobs = self.retry_request(failed_request['method'], failed_request['url'],
                #                                        failed_request['company_name'], retries=3, delay=5,
                #                                        )
            logging.info(f"Successfully fetched a total of {len(all_jobs)} Amazon jobs")
            all_jobs = all_jobs + fail_jobs
            return all_jobs,"no total_jobs"

        except Exception as e:
            logging.error(f"Error in fetch_jobs: {e}:{self.company_name}")
            return [],0

    # def retry_request(self, method, url, company_name, retries=3, delay=5, **kwargs):
    #     attempt = 0
    #
    #     while attempt < retries:
    #         try:
    #             response = self.make_request(method, url, company_name, **kwargs)
    #             data = response.json()
    #
    #             if data.get("error") is not None:
    #                 return []
    #
    #             fail_jobs = [self.parse_job(job) for job in data["jobs"]]
    #             return fail_jobs
    #         except Exception as e:
    #             attempt += 1
    #             logging.error(f"Error on attempt {attempt} for {company_name}: {e}")
    #             if attempt < retries:
    #                 logging.info(f"Retrying in {delay} seconds...")
    #             else:
    #                 logging.error(f"All {retries} attempts failed for {company_name}")
    #                 return None
    def __del__(self):
        """确保 HTTP 客户端被正确关闭"""
        if hasattr(self, 'client'):
            self.client.close()
