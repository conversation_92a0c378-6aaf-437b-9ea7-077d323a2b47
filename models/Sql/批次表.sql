CREATE TABLE batch_tables (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '自增主键ID',
    batchId INT NOT NULL COMMENT '批次号',
    scrape_date DATE NOT NULL COMMENT '爬取日期',
    scrape_time TIME NOT NULL COMMENT '爬取时间',
    company VARCHAR(255) NOT NULL COMMENT '公司名称',
    companyUrl VARCHAR(255) NOT NULL COMMENT '公司url',
    cwiq_code VARCHAR(255) COMMENT '公司编码',
    batchSize INT NOT NULL COMMENT '该批次多少条数据',
    total_jobs VARCHAR(255) NOT NULL COMMENT '工作总量',
    success_rate VARCHAR(255) NOT NULL COMMENT '成功率'
);



CREATE INDEX idx_batch_company
ON batch_tables (company);

CREATE INDEX idx_batch_company_batchId
ON batch_tables (company,batchId);