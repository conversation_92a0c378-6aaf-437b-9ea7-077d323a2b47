import logging
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker, scoped_session
from sqlalchemy.exc import SQLAlchemyError
from contextlib import contextmanager

from config.settings import MYSQL_CONFIG
from models.data_models import Base, Job, Batch, NoJob
from utils.colored_logger import log_exception

class MySQLHandler:
    def __init__(self):
        connection_string = f"mysql+pymysql://{MYSQL_CONFIG['user']}:{MYSQL_CONFIG['password']}@{MYSQL_CONFIG['host']}:{MYSQL_CONFIG['port']}/{MYSQL_CONFIG['database']}"
        self.engine = create_engine(
            connection_string,
            pool_pre_ping=True,     # 检查连接是否健康，防止用到断开的连接
            pool_recycle=1800,      # 连接池中连接存活 1800 秒（30分钟）
            pool_size=3000,           # 连接池大小
            max_overflow=100         # 超出池子数量后最多临时再开10个连接
        )
        Base.metadata.create_all(self.engine)
        self.Session = scoped_session(sessionmaker(bind=self.engine))

    @contextmanager
    def get_session(self):
        """获取新的 session，每次使用自己的 session，防止污染"""
        session = self.Session()
        try:
            yield session
            session.commit()
        except Exception:
            session.rollback()
            raise
        finally:
            session.close()

    def save_job(self, job_data):
        try:
            with self.get_session() as session:
                job = Job(**job_data)
                session.add(job)
        except SQLAlchemyError as e:
            log_exception("Error saving job to MySQL", e)
            return False
        return True

    def save_no_job(self, job_data):
        try:
            with self.get_session() as session:
                no_job = NoJob(**job_data)
                session.add(no_job)
        except SQLAlchemyError as e:
            log_exception("Error saving no_job to MySQL", e)
            return False
        return True

    def save_batch(self, batch_data):
        try:
            with self.get_session() as session:
                batch = Batch(**batch_data)
                session.add(batch)
        except SQLAlchemyError as e:
            log_exception("Error saving batch to MySQL", e)
            return False
        return True

    def get_jobs_by_batch(self, batch_id):
        with self.get_session() as session:
            return session.query(Job).filter_by(batchId=batch_id).all()

    def get_batch_by_id(self, batch_id):
        with self.get_session() as session:
            return session.query(Batch).filter_by(batchId=batch_id).first()

    def get_no_job_by_id(self, batch_id):
        with self.get_session() as session:
            return session.query(NoJob).filter_by(batchId=batch_id).first()

    def close(self):
        """关闭连接池资源"""
        self.Session.remove()
        self.engine.dispose()
