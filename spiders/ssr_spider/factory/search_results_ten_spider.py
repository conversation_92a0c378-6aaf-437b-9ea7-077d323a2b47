import json
import random
import time
from datetime import datetime
import logging
from urllib.parse import urlparse
from concurrent.futures import ThreadPoolExecutor, as_completed
from spiders.core_spider.base_spider import BaseSpider
from utils.country_utils import CountryCodeUtils
from utils.http_utils.http_client import HTTPClient
from bs4 import BeautifulSoup
import re
import ast
def _simplify_job_data(job):
    """
    将原始职位数据简化为只包含必要信息的格式

    Args:
        job (dict): 原始职位数据

    Returns:
        dict: 简化后的职位数据，只包含ID和路径信息
    """
    try:
        id = job.get('jobId','')
        title = job.get('title','')
        # 替换非字母数字字符为 -
        modified_title = re.sub(r'[^a-zA-Z0-9]+', '-', title)
        # 去除连续的 -
        modified_title = re.sub(r'-+', '-', modified_title)
        # 去掉首尾的 -
        modified_title = modified_title.strip('-')
        externalPath = id + "/" + modified_title
    except Exception as e:
        logging.error(f"Error parsing job data{e}: {job}")
        return {
            'id': None,  # REQ号作为ID
            'externalPath': None
        }
    return {
        'id': id,
        'externalPath': externalPath
    }

class SearchResultsTenSpider(BaseSpider):
    def __init__(self, company_name, base_url, company_url, cwiq_code):
        super().__init__(company_name, base_url, company_url, cwiq_code)
        self.company_name = company_name
        self.company_url = company_url
        self.cwiq_code = cwiq_code
        self.country_utils = CountryCodeUtils()
        s = self.base_url.split(';')[-1]
        self.json_data = ast.literal_eval(s)
        self.json_data['s'] = "1"
        self.base_url = self.base_url.split(';')[0]
        parsed_url = urlparse(self.company_url)
        origin = f"{parsed_url.scheme}://{parsed_url.netloc}"
        self.headers = {
            'accept': '*/*',
            'accept-language': 'zh-CN,zh;q=0.9',
            'cache-control': 'no-cache',
            'content-type': 'application/json',
            # 'origin': 'https://careers.tranetechnologies.com',
            'pragma': 'no-cache',
            'priority': 'u=1, i',
            'referer': 'https://careers.dollartree.com/us/en/search-results?from=9980&s=1',
            'sec-ch-ua': '"Chromium";v="134", "Not:A-Brand";v="24", "Google Chrome";v="134"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"Windows"',
            'sec-fetch-dest': 'empty',
            'sec-fetch-mode': 'cors',
            'sec-fetch-site': 'same-origin',
            'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/134.0.0.0 Safari/537.36',
            # 'x-csrf-token': 'f1ec9e07ee494acdac7d34bd06bc70ed',
        }
        self.company_list = ['RAYTHEON TECHNOLOGIES CORP', 'ABBOTT LABORATORIES']
        # 创建 HTTP 客户端
        if self.company_url=="https://talent.lowes.com/us/en/c/corporate-jobs":
            self.job_url_base = self.company_url.replace('/c/corporate-jobs', '/job')
        else:
            self.job_url_base = self.company_url.replace('/search-results', '/job')
        self.client = HTTPClient()

    @classmethod
    def create_spider(cls, company_name, base_url, company_url, cwiq_code):
        """工厂方法创建 MedtronicSpider 实例"""
        return cls(company_name, base_url, company_url, cwiq_code)

    def make_request(self, method, url, company_name, requset_count, **kwargs):
        """发送请求的通用方法"""
        kwargs['headers'] = self.headers
        return self.client.request(method, url, company_name, requset_count, log_ip=False, **kwargs)

    def get_total_jobs(self):
        """获取总职位数"""
        json = self.json_data
        try:
            response = self.make_request('POST', self.base_url, self.company_name, requset_count=3, json=json)
            if self.company_name not in self.company_list:
                return response.json()['eagerLoadRefineSearch']['totalHits']
            else:
                return response.json()['refineSearch']['totalHits']
        except Exception as e:
            logging.error(f"Error getting total jobs: {e}:{self.company_name}")
            return 0

    def parse_job(self, job_data):
        """解析职位数据"""
        try:
            if job_data:
                job_id = job_data.get("jobId", "")
                job_title = job_data.get("title", "")
                # 替换非字母数字字符为 -
                modified_title = re.sub(r'[^a-zA-Z0-9]+', '-', job_title)
                # 去除连续的 -
                modified_title = re.sub(r'-+', '-', modified_title)
                # 去掉首尾的 -
                modified_title = modified_title.strip('-')
                externalPath = job_id + "/" + modified_title
                # 解析地点信息
                location = job_data.get("structureData", {}).get("jobLocation", {})
                address = location.get("address", {})
                country = address.get("addressCountry", "") if address else ""
                country_code = self.country_utils.get_alpha3(country)
                city = address.get("addressLocality", "") if address else ""
                state = address.get("addressRegion", "") if address else ""

                return {
                    "jobId": job_id,
                    "company": self.company_name,
                    "cwiq_code": self.cwiq_code,
                    "companyUrl": self.company_url,
                    "country": country_code,
                    "city": city,
                    "jobTitle": job_title,
                    # "department": job_info.get("hiringOrganization", {}).get("name", ""),
                    "location": state,
                    "jobDescription": job_data.get("description", ""),
                    "jobUrl": f"{self.job_url_base}/{externalPath}",
                    "created_time": None,
                    "updated_time": None,
                    "post_time": datetime.now(),
                }
        except Exception as e:
            logging.error(f"Error parsing job data: {e}:{self.company_name}")
            return None

    def fetch_job_details(self, external_path):
        """获取职位详细信息"""
        try:
            time.sleep(random.uniform(0.5, 1))
            job_url = f"{self.job_url_base}/{external_path}"
            response = self.make_request('GET', job_url,self.company_name, requset_count=3)
            # 解析 HTML
            soup = BeautifulSoup(response.text, 'html.parser')

            # 查找包含数据的 <script> 标签
            script_tags = soup.find_all('script', type='text/javascript')
            data_dict = {}

            # 遍历所有符合条件的 <script> 标签
            for script_tag in script_tags:
                # 提取 JavaScript 代码
                script_content = script_tag.string

                # 检查是否包含特定的变量
                if script_content and 'phApp.ddo = ' in script_content:
                    # 找到 'phApp.ddo = ' 后的位置
                    start_index = script_content.find('phApp.ddo = ') + len('phApp.ddo = ')

                    # 找到第一个 '};' 的位置
                    semicolon_index = script_content.find('};', start_index)

                    # 提取 JSON 数据并加载为 Python 字典
                    json_data = script_content[start_index:semicolon_index + 1].strip()
                    data_dict = json.loads(json_data)
                    break  # 找到后退出循环

            # 获取 job_data 数据
            jobDetail = data_dict.get("jobDetail", {})
            data = jobDetail.get("data", {})
            job_data = data.get("job", {})
            return job_data
        except Exception as e:
            logging.error(f"Error fetching job details for {self.company_name}: {e}")
            return None

    def fetch_jobs(self,max_workers):
        """获取所有职位信息"""
        try:
            total_jobs = self.get_total_jobs()
            if total_jobs > 9980:
                total_jobs = 9980
            if total_jobs == 0:
                logging.info(f"No jobs found {self.company_name}")
                return [],0

            logging.info(f"Found {total_jobs} total jobs")

            # 获取上一批次的职位数据
            latest_batch = self.db_ops.get_latest_batch(self.company_url)
            batch_id = latest_batch.batchId if latest_batch else 0
            previous_job_ids = set(self.db_ops.get_job_ids_by_batch(
                self.company_url,
                batch_id,
                status=[0, 1]
            ))

            all_jobs = self._fetch_job_listings(total_jobs)

            # 从现有职位中提取所有job_ids
            current_job_ids = {
                job.get('id', '')
                for job in all_jobs
            }

            # 找出重复的job_ids
            repeated_job_ids = current_job_ids.intersection(previous_job_ids)
            new_job_ids = current_job_ids - previous_job_ids

            # 一次性获取所有重复职位的数据
            repeated_jobs_dict = self.db_ops.get_jobs_by_ids(list(repeated_job_ids))

            # 分离新职位和重复职位
            new_jobs = [
                job for job in all_jobs
                if job.get('id', '') in new_job_ids
            ]
            repeated_jobs = list(repeated_jobs_dict.values())

            logging.info(f"Found {len(new_jobs)} new jobs and {len(repeated_jobs)} repeated jobs")

            # 只处理新职位的详细信息
            processed_jobs = []
            if new_jobs:
                with ThreadPoolExecutor(max_workers=max_workers) as executor:
                    future_to_job = {
                        executor.submit(self.fetch_job_details, job.get('externalPath', '')): job
                        for job in new_jobs
                    }

                    completed = 0
                    total_new = len(new_jobs)
                    for future in as_completed(future_to_job):
                        job_details = future.result()
                        if job_details:
                            parsed_job = self.parse_job(job_details)
                            if parsed_job:
                                processed_jobs.append(parsed_job)
                        completed += 1
                        progress = (completed / total_new) * 100
                        logging.info(f"Processing new job details: {completed}/{total_new} ({progress:.2f}%)")

            # 合并新处理的职位和重复的职位
            all_processed_jobs = processed_jobs + repeated_jobs
            logging.info(f"Successfully processed {len(all_processed_jobs)} total jobs")
            return all_processed_jobs,total_jobs

        except Exception as e:
            logging.error(f"Error in fetch_jobs: {e}:{self.company_name}")
            return [],0

    def _fetch_job_listings(self, total_jobs):
        """获取所有职位列表"""
        try:
            jobs_per_request = 10
            if self.company_name =="HONEYWELL INTERNATIONAL INC":
                jobs_per_request = 50
            total_requests = (total_jobs // jobs_per_request) + (1 if total_jobs % jobs_per_request else 0)
            all_jobs = []

            with ThreadPoolExecutor(max_workers=25) as executor:
                futures = []
                for i in range(total_requests):
                    time.sleep(3)
                    json = self.json_data
                    json['from'] = i * jobs_per_request
                    json['size'] = jobs_per_request
                    futures.append(
                        executor.submit(
                            self.make_request,
                            'POST',
                            self.base_url,
                            self.company_name,
                            requset_count=3,
                            json=json
                        )
                    )

                for future in as_completed(futures):
                    try:
                        response = future.result()
                        # 获取 jobs 数据
                        if self.company_name not in self.company_list:
                            jobs = response.json()['eagerLoadRefineSearch']['data']['jobs']
                        else:
                            jobs = response.json()['refineSearch']['data']['jobs']
                        # 使用新函数处理每个职位数据
                        simplified_jobs = [_simplify_job_data(job) for job in jobs]
                        all_jobs.extend(simplified_jobs)
                        logging.info(f"Progress: {len(all_jobs)}/{total_jobs} jobs fetched")
                    except Exception as e:
                        logging.error(f"Error processing job batch: {e}:{self.company_name}")

            return all_jobs
        except Exception as e:
            logging.error(f"Error in fetch_job_listings: {e}:{self.company_name}")


    def __del__(self):
        """确保 HTTP 客户端被正确关闭"""
        if hasattr(self, 'client'):
            self.client.close()

