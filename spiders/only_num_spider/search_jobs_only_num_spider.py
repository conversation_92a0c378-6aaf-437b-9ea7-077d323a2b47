import json
import random
import time
from datetime import datetime
import logging
from urllib.parse import urlparse
from concurrent.futures import ThreadPoolExecutor, as_completed
from spiders.core_spider.base_spider import BaseSpider
from utils.country_utils import CountryCodeUtils
from utils.http_utils.http_client import HTTPClient
from bs4 import BeautifulSoup
import re

class SearchJobsOnlyNumSpider(BaseSpider):
    def __init__(self, company_name, base_url, company_url, cwiq_code):
        super().__init__(company_name, base_url, company_url, cwiq_code)
        self.company_name = company_name
        self.company_url = company_url
        self.cwiq_code = cwiq_code
        self.country_utils = CountryCodeUtils()
        self.headers = {
            'Accept': '*/*',
            'Accept-Language': 'zh-CN,zh;q=0.9',
            'Connection': 'keep-alive',
            'Content-Type': 'application/json; charset=utf-8',
            'Sec-<PERSON><PERSON>-Dest': 'empty',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Site': 'same-origin',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/134.0.0.0 Safari/537.36 Edg/134.0.0.0',
            'X-Requested-With': 'XMLHttpRequest',
            'sec-ch-ua': '"Chromium";v="134", "Not:A-Brand";v="24", "Microsoft Edge";v="134"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"Windows"',
        }
        parsed_url = urlparse(base_url)
        self.job_url_base = f'{parsed_url.scheme}://{parsed_url.netloc}'
        # 创建 HTTP 客户端
        self.client = HTTPClient()

    @classmethod
    def create_spider(cls, company_name, base_url, company_url, cwiq_code):
        """工厂方法创建 MedtronicSpider 实例"""
        return cls(company_name, base_url, company_url, cwiq_code)

    def make_request(self, method, url, company_name, requset_count, **kwargs):
        """发送请求的通用方法"""
        kwargs['headers'] = self.headers
        return self.client.request(method, url, company_name, requset_count, log_ip=False, **kwargs)

    def get_total_jobs(self):
        """获取总职位数"""
        params = {
            'ActiveFacetID': '0',
            'CurrentPage': '1',
            'RecordsPerPage': '500',
            'Distance': '50',
            'RadiusUnitType': '0',
            'ShowRadius': 'False',
            'IsPagination': 'False',
            'FacetType': '0',
            'SearchResultsModuleName': 'Search Results',
            'SearchFiltersModuleName': 'Search Filters',
            'SortCriteria': '0',
            'SortDirection': '0',
            'SearchType': '5',
            'PostalCode': '',
            'ResultsType': '0',
        }
        if self.company_url == "https://www.jobs-ups.com/search-jobs":
            params['SearchResultsModuleName'] = "Default Search Results Module"
            params['SearchFiltersModuleName'] = "Default Search Filters"
        try:
            response = self.make_request('GET', self.base_url, self.company_name, requset_count=3, params=params)
            text = response.json()['results']
            soup = BeautifulSoup(text, 'html.parser')
            section_tag = soup.find('section', id='search-results')
            if self.company_url == "https://jobs.thetorocompany.com/search-jobs":
                section_tag = soup.find('div', id='search-results')
            total_jobs = int(section_tag['data-total-job-results'])
            return total_jobs
        except Exception as e:
            logging.error(f"Error getting total jobs: {e}")
            return 0

    def parse_job(self, job_data):
        """解析职位数据"""
        pass

    def fetch_jobs(self,max_workers):
        """获取所有职位信息"""
        try:
            total_jobs = self.get_total_jobs()
            if total_jobs == 0:
                logging.info(f"No jobs found {self.company_name}")
                return [], 0

            logging.info(f"Found {total_jobs} total jobs")
            return [], total_jobs
        except Exception as e:
            logging.error(f"Error in fetch_jobs: {e}:{self.company_name}")
            return [], 0

    def __del__(self):
        """确保 HTTP 客户端被正确关闭"""
        if hasattr(self, 'client'):
            self.client.close()
