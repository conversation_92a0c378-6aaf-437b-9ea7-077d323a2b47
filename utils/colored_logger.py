"""
彩色日志工具模块 - colored_logger.py

提供带颜色的日志输出功能，根据不同的错误类型显示不同的颜色：
1. IO错误（文件写入、数据库错误）- 红色
2. 正确结果 - 绿色
3. 页面解析错误 - 黄色
4. 其他信息 - 白色

支持控制台和文件双重输出，文件输出不包含颜色代码。
"""

import logging
import sys
from enum import Enum
from typing import Optional


class LogColor(Enum):
    """日志颜色枚举"""
    RED = '\033[91m'      # 红色 - IO错误
    GREEN = '\033[92m'    # 绿色 - 正确结果
    YELLOW = '\033[93m'   # 黄色 - 页面解析错误
    BLUE = '\033[94m'     # 蓝色 - 正常输出
    WHITE = '\033[97m'    # 白色 - 其他信息
    RESET = '\033[0m'     # 重置颜色


class LogType(Enum):
    """日志类型枚举"""
    IO_ERROR = "io_error"           # IO错误：文件写入、数据库错误
    SUCCESS = "success"             # 正确结果
    PARSE_ERROR = "parse_error"     # 页面解析错误
    NORMAL = "normal"               # 正常输出
    INFO = "info"                   # 其他信息


class ColoredFormatter(logging.Formatter):
    """彩色日志格式化器"""

    def __init__(self, fmt=None, datefmt=None, use_colors=True):
        super().__init__(fmt, datefmt)
        self.use_colors = use_colors

        # 颜色映射
        self.color_map = {
            LogType.IO_ERROR: LogColor.RED,
            LogType.SUCCESS: LogColor.GREEN,
            LogType.PARSE_ERROR: LogColor.YELLOW,
            LogType.NORMAL: LogColor.BLUE,
            LogType.INFO: LogColor.WHITE
        }

    def format(self, record):
        """格式化日志记录，添加颜色"""
        formatted = super().format(record)

        if not self.use_colors:
            return formatted

        # 获取日志类型
        log_type = getattr(record, 'log_type', LogType.INFO)
        color = self.color_map.get(log_type, LogColor.WHITE)

        # 添加颜色
        return f"{color.value}{formatted}{LogColor.RESET.value}"


class ColoredLogger:
    """彩色日志记录器"""

    def __init__(self, name: str = "colored_spider", level: int = logging.INFO):
        # 使用独特的名称避免与根日志记录器冲突
        self.logger = logging.getLogger(name)
        self.logger.setLevel(level)

        # 防止日志传播到根日志记录器，避免重复输出
        self.logger.propagate = False

        # 避免重复添加处理器
        if not self.logger.handlers:
            self._setup_handlers()

    def _setup_handlers(self):
        """设置日志处理器"""
        # 控制台处理器（带颜色）
        console_handler = logging.StreamHandler(sys.stdout)
        console_formatter = ColoredFormatter(
            fmt='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S',
            use_colors=True
        )
        console_handler.setFormatter(console_formatter)
        self.logger.addHandler(console_handler)

        # 文件处理器（无颜色）
        try:
            file_handler = logging.FileHandler('/mnt/data1/leo/spider_colored.log')
            file_formatter = ColoredFormatter(
                fmt='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
                datefmt='%Y-%m-%d %H:%M:%S',
                use_colors=False
            )
            file_handler.setFormatter(file_formatter)
            self.logger.addHandler(file_handler)
        except (OSError, PermissionError):
            # 如果无法创建文件，只使用控制台输出
            pass

    def _log_with_type(self, level: int, message: str, log_type: LogType):
        """带类型的日志记录"""
        extra = {'log_type': log_type}
        self.logger.log(level, message, extra=extra)

    def io_error(self, message: str):
        """记录IO错误（红色）"""
        self._log_with_type(logging.ERROR, f"[IO ERROR] {message}", LogType.IO_ERROR)

    def success(self, message: str):
        """记录成功信息（绿色）"""
        self._log_with_type(logging.INFO, f"[SUCCESS] {message}", LogType.SUCCESS)

    def parse_error(self, message: str):
        """记录解析错误（黄色）"""
        self._log_with_type(logging.WARNING, f"[PARSE ERROR] {message}", LogType.PARSE_ERROR)

    def normal(self, message: str):
        """记录正常输出（蓝色）"""
        self._log_with_type(logging.INFO, message, LogType.NORMAL)

    def info(self, message: str):
        """记录一般信息（白色）"""
        self._log_with_type(logging.INFO, message, LogType.INFO)

    def warning(self, message: str):
        """记录警告信息（白色）"""
        self._log_with_type(logging.WARNING, message, LogType.INFO)

    def error(self, message: str):
        """记录一般错误（白色）"""
        self._log_with_type(logging.ERROR, message, LogType.INFO)

    def debug(self, message: str):
        """记录调试信息（白色）"""
        self._log_with_type(logging.DEBUG, message, LogType.INFO)


# 全局彩色日志实例
colored_logger = ColoredLogger()


def log_io_error(message: str):
    """记录IO错误 - 红色"""
    colored_logger.io_error(message)


def log_success(message: str):
    """记录成功信息 - 绿色"""
    colored_logger.success(message)


def log_parse_error(message: str):
    """记录解析错误 - 黄色"""
    colored_logger.parse_error(message)


def log_normal(message: str):
    """记录正常输出 - 蓝色"""
    colored_logger.normal(message)


def log_info(message: str):
    """记录一般信息 - 白色"""
    colored_logger.info(message)


def log_spider_result(spider_name: str, status: str, message: str = ""):
    """根据爬虫结果状态记录相应颜色的日志"""
    if status == "success":
        log_success(f"{spider_name} spider completed successfully")
    else:
        message_lower = message.lower()

        # SQL和JSON错误 - 黄色
        if any(keyword in message_lower for keyword in ['sql', 'mysql', 'database', 'json', 'decode', 'parse', 'html', 'xml']):
            log_parse_error(f"{spider_name} spider failed: {message}")
        # IO相关错误 - 红色
        elif any(keyword in message_lower for keyword in ['file', 'write', 'save', 'permission', 'access', 'io', 'connection']):
            log_io_error(f"{spider_name} spider failed: {message}")
        # 超时等其他状态 - 蓝色
        elif status in ["timeout", "warning"]:
            log_normal(f"{spider_name} spider {status}: {message}")
        # 其他错误 - 红色
        else:
            log_io_error(f"{spider_name} spider failed: {message}")


def log_database_operation(operation: str, success: bool, message: str = ""):
    """记录数据库操作结果"""
    if success:
        log_success(f"Database {operation} completed successfully")
    else:
        log_io_error(f"Database {operation} failed: {message}")


def log_file_operation(operation: str, filename: str, success: bool, message: str = ""):
    """记录文件操作结果"""
    if success:
        log_success(f"File {operation} completed: {filename}")
    else:
        log_io_error(f"File {operation} failed for {filename}: {message}")


def log_exception(exception: Exception, context: str = ""):
    """智能记录异常信息，根据异常类型自动选择颜色"""
    error_msg = str(exception).lower()
    error_type = type(exception).__name__.lower()

    # 构建完整的错误信息
    if context:
        full_message = f"{context}: {exception}"
    else:
        full_message = str(exception)

    # SQL和JSON错误 - 黄色
    if any(keyword in error_type for keyword in ['sql', 'json', 'yaml']) or \
       any(keyword in error_msg for keyword in ['sql', 'mysql', 'database', 'json', 'decode', 'parse', 'html', 'xml']):
        log_parse_error(full_message)
    # IO相关错误 - 红色
    elif any(keyword in error_type for keyword in ['oserror', 'ioerror', 'permission', 'filenotfound', 'clienterror']) or \
         any(keyword in error_msg for keyword in ['file', 'write', 'save', 'permission', 'access', 'connection']):
        log_io_error(full_message)
    # 超时错误 - 蓝色
    elif 'timeout' in error_type or 'timeout' in error_msg:
        log_normal(full_message)
    # 其他错误 - 红色
    else:
        log_io_error(full_message)


# 使用示例
if __name__ == "__main__":
    # 测试不同类型的日志输出
    log_success("爬虫执行成功")
    log_io_error("数据库连接失败")
    log_parse_error("页面解析出错")
    log_info("系统启动中...")

    # 测试爬虫结果日志
    log_spider_result("Microsoft", "success")
    log_spider_result("Google", "error", "database connection failed")
    log_spider_result("Amazon", "error", "html parse error")
    log_spider_result("Tesla", "timeout", "request timeout")
