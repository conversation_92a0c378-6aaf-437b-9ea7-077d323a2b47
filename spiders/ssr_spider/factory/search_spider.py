import json
import random
import time
from datetime import datetime
import logging
from urllib.parse import urlparse
from concurrent.futures import ThreadPoolExecutor, as_completed
from spiders.core_spider.base_spider import BaseSpider
from utils.country_utils import CountryCodeUtils
from utils.http_utils.http_client import HTTPClient
from bs4 import BeautifulSoup
import re
def _simplify_job_data(job):
    """
    将原始职位数据简化为只包含必要信息的格式

    Args:
        job (str): 职位链接

    Returns:
        dict: 简化后的职位数据，只包含ID和路径信息
    """
    try:
        externalPath = job
        id = externalPath.split('/')[-2]
    except Exception as e:
        logging.error(f"Error parsing job data{e}: {job}")
        return {
            'id': None,  # REQ号作为ID
            'externalPath': None
        }
    return {
        'id': id,
        'externalPath': externalPath
    }

class SearchSpider(BaseSpider):
    def __init__(self, company_name, base_url, company_url, cwiq_code):
        super().__init__(company_name, base_url, company_url, cwiq_code)
        self.company_name = company_name
        self.company_url = company_url
        self.cwiq_code = cwiq_code
        self.country_utils = CountryCodeUtils()
        self.headers = {
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
            'Accept-Language': 'zh-CN,zh;q=0.9',
            'Connection': 'keep-alive',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'same-origin',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/133.0.0.0 Safari/537.36 Edg/133.0.0.0',
            'sec-ch-ua': '"Not(A:Brand";v="99", "Microsoft Edge";v="133", "Chromium";v="133"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"Windows"',
        }
        self.parsed_url = urlparse(self.base_url)
        self.job_url_base = f"{self.parsed_url.scheme}://{self.parsed_url.netloc}"
        # 创建 HTTP 客户端
        self.client = HTTPClient()

    @classmethod
    def create_spider(cls, company_name, base_url, company_url, cwiq_code):
        """工厂方法创建 MedtronicSpider 实例"""
        return cls(company_name, base_url, company_url, cwiq_code)

    def make_request(self, method, url, company_name, requset_count, **kwargs):
        """发送请求的通用方法"""
        kwargs['headers'] = self.headers
        return self.client.request(method, url, company_name, requset_count, log_ip=False, **kwargs)

    def get_total_jobs(self):
        """获取总职位数"""
        params = {
            'q': '',
            'sortColumn': 'referencedate',
            'sortDirection': 'desc',
            'startrow': 0,
        }
        try:
            response = self.make_request('GET', self.base_url, self.company_name, requset_count=3, params=params)
            # 解析 HTML
            soup = BeautifulSoup(response.text, 'html.parser')

            if self.base_url == "https://jobs.nscorp.com/search":
                span_tag = soup.find('span', id='tile-search-results-label')
                text = span_tag.get_text(strip=True)
                m = re.search(r'of\s+(\d+)\s+Jobs', text)
                total_jobs = int(m.group(1)) if m else 0
            else:
                # 查找包含数据的 <span> 标签
                span_tag = soup.find('span', class_='paginationLabel')

                b_tag = span_tag.find_all('b')[1]
                total_jobs = int(b_tag.text)
            return total_jobs
        except Exception as e:
            logging.error(f"Error getting total jobs: {e} for {self.company_name}")
            return 0

    def parse_job(self, job_data):
        """解析职位数据"""
        try:
            if job_data:
                country = job_data.get("country", "")
                country_code = self.country_utils.get_alpha3(country)
                return {
                    "jobId": job_data.get("job_id", ""),
                    "company": self.company_name,
                    "cwiq_code": self.cwiq_code,
                    "companyUrl": self.company_url,
                    "country": country_code,
                    "city": job_data.get("city", ""),
                    "jobTitle": job_data.get("title", ""),
                    # "department": job_info.get("hiringOrganization", {}).get("name", ""),
                    "location": job_data.get("state", ""),
                    "jobDescription": job_data.get("description", ""),
                    "jobUrl": job_data.get("job_url", ""),
                    "created_time": None,
                    "updated_time": None,
                    "post_time": datetime.now(),
                }
        except Exception as e:
            logging.error(f"Error parsing job data: {e} for {self.company_name}")
            return None

    def fetch_job_details(self, external_path):
        """获取职位详细信息"""
        try:
            time.sleep(random.uniform(0.5, 1))
            job_url = f"{self.job_url_base}{external_path}"
            response = self.make_request('GET', job_url,self.company_name, requset_count=3)
            # 解析 HTML
            soup = BeautifulSoup(response.text, 'html.parser')

            if (self.base_url == "https://careers.flightsafety.com/search"
                    or self.base_url == "https://careers.bnsflogistics.com/search"
                    or self.base_url == "https://jobs.nexteraenergy.com/search"
                    or self.base_url == "https://jobs.nassco.com/search"
                    or self.base_url == "https://careers.gulfstream.com/search"
                    or self.base_url == "https://jobs.nscorp.com/search"
                    or self.base_url == "https://ienovatalento.com.mx/search"):
                title_tag = soup.find('h1', {'itemprop': 'title'})
            else:
                title_tag = soup.find('span', {'data-careersite-propertyid': 'title'})
            if self.base_url == "https://careers.dominionenergy.com/search" and title_tag is None:
                title_tag = soup.find('h1', {'itemprop': 'title'})
            if title_tag:
                title = title_tag.text.strip()
            else:
                title = ""

            location_tag = soup.find('span', class_='jobGeoLocation')
            if location_tag:
                location = location_tag.text.strip()
                parts = location.split(',')
                if (self.base_url == "https://jobs.exxonmobil.com/search"
                        or self.base_url == "https://careers.bnsflogistics.com/search"
                        or self.base_url == "https://careers.gulfstream.com/search"
                        or self.base_url == "https://ienovatalento.com.mx/search"):
                    country = parts[-1].strip() if len(parts) > 0 else ""
                    state = parts[-2].strip() if len(parts) > 1 else ""
                    city = parts[-3].strip() if len(parts) > 2 else ""
                else:
                    if len(parts) == 4:
                        country = parts[-2].strip()
                        state = parts[-3].strip()
                        city = parts[-4].strip()
                    elif len(parts) == 3:
                        country = parts[-2].strip()
                        city = parts[-3].strip()
                        state = ""
                    else:
                        country = parts[-1].strip()
                        state = parts[-2].strip() if len(parts) > 1 else ""
                        city = ""
            else:
                country = ""
                city = ""
                state = ""

            if (self.base_url == "https://careers.flightsafety.com/search"
                    or self.base_url == "https://careers.bnsflogistics.com/search"
                    or self.base_url == "https://jobs.nexteraenergy.com/search"
                    or self.base_url == "https://jobs.nassco.com/search"
                    or self.base_url == "https://careers.gulfstream.com/search"
                    or self.base_url == "https://jobs.nscorp.com/search"
                    or self.base_url == "https://careers.dominionenergy.com/search"
                    or self.base_url == "https://ienovatalento.com.mx/search"):
                description_tag = soup.find('span', {'itemprop': 'description'})
            else:
                description_tag = soup.find('span', {'data-careersite-propertyid': 'description'})
            if description_tag:
                description = description_tag.text.strip()
            else:
                description = ""

            job_id = job_url.split('/')[-2]

            job_data = {
                "title": title,
                "country": country,
                "state": state,
                "city": city,
                "description": description,
                "job_id": job_id,
                "job_url": job_url,
            }
            return job_data
        except Exception as e:
            logging.error(f"Error fetching job details for {self.company_name}{job_url}: {e}")
            return None

    def fetch_jobs(self,max_workers):
        """获取所有职位信息"""
        try:
            total_jobs = self.get_total_jobs()
            if total_jobs == 0:
                logging.info(f"No jobs found {self.company_name}")
                return [],0

            logging.info(f"Found {total_jobs} total jobs")

            # 获取上一批次的职位数据
            latest_batch = self.db_ops.get_latest_batch(self.company_url)
            batch_id = latest_batch.batchId if latest_batch else 0
            previous_job_ids = set(self.db_ops.get_job_ids_by_batch(
                self.company_url,
                batch_id,
                status=[0, 1]
            ))

            all_jobs = self._fetch_job_listings(total_jobs)


            # 从现有职位中提取所有job_ids
            current_job_ids = {
                job.get('id', '')
                for job in all_jobs
            }

            # 找出重复的job_ids
            repeated_job_ids = current_job_ids.intersection(previous_job_ids)
            new_job_ids = current_job_ids - previous_job_ids

            # 一次性获取所有重复职位的数据
            repeated_jobs_dict = self.db_ops.get_jobs_by_ids(list(repeated_job_ids))

            # 分离新职位和重复职位
            new_jobs = [
                job for job in all_jobs
                if job.get('id', '') in new_job_ids
            ]
            repeated_jobs = list(repeated_jobs_dict.values())

            logging.info(f"Found {len(new_jobs)} new jobs and {len(repeated_jobs)} repeated jobs")

            # 只处理新职位的详细信息
            processed_jobs = []
            if new_jobs:
                with ThreadPoolExecutor(max_workers=max_workers) as executor:
                    future_to_job = {
                        executor.submit(self.fetch_job_details, job.get('externalPath', '')): job
                        for job in new_jobs
                    }

                    completed = 0
                    total_new = len(new_jobs)
                    for future in as_completed(future_to_job):
                        job_details = future.result()
                        if job_details:
                            parsed_job = self.parse_job(job_details)
                            if parsed_job:
                                processed_jobs.append(parsed_job)
                        completed += 1
                        progress = (completed / total_new) * 100
                        logging.info(f"Processing new job details: {completed}/{total_new} ({progress:.2f}%)")

            # 合并新处理的职位和重复的职位
            all_processed_jobs = processed_jobs + repeated_jobs
            logging.info(f"Successfully processed {len(all_processed_jobs)} total jobs")
            return all_processed_jobs,total_jobs

        except Exception as e:
            logging.error(f"Error in fetch_jobs: {e}:{self.company_name}")
            return [],0

    def _fetch_job_listings(self, total_jobs):
        """获取所有职位列表"""
        try:
            if self.company_url == "https://up.jobs/search" or self.company_url == "https://jobs.nassco.com/search":
                jobs_per_request = 20
            elif self.company_url == "https://jobs.jetaviation.com/search":
                jobs_per_request = 50
            elif self.company_url == "https://careers.phillips66.com/search":
                jobs_per_request = 15
            else:
                jobs_per_request = 25
            total_requests = (total_jobs // jobs_per_request) + (1 if total_jobs % jobs_per_request else 0)
            all_jobs = []

            with ThreadPoolExecutor(max_workers=25) as executor:
                futures = []
                for i in range(total_requests):
                    params = {
                        'q': '',
                        'sortColumn': 'referencedate',
                        'sortDirection': 'desc',
                        'startrow': i*jobs_per_request,
                    }

                    futures.append(
                        executor.submit(
                            self.make_request,
                            'GET',
                            self.base_url,
                            self.company_name,
                            requset_count=3,
                            params=params
                        )
                    )

                for future in as_completed(futures):
                    try:
                        response = future.result()
                        jobs = []
                        # 解析 HTML
                        soup = BeautifulSoup(response.text, 'html.parser')

                        if self.base_url == "https://jobs.nscorp.com/search":
                            li_tags = soup.find_all('li', class_='job-tile')

                            for li_tag in li_tags:
                                externalPath = li_tag['data-url']
                                jobs.append(externalPath)
                        else:
                            # 查找包含数据的 <span> 标签
                            span_tags = soup.find_all('span', class_='jobTitle hidden-phone')

                            for span_tag in span_tags:
                                externalPath = span_tag.find('a')['href']
                                jobs.append(externalPath)

                        # 使用新函数处理每个职位数据
                        simplified_jobs = [_simplify_job_data(job) for job in jobs]
                        all_jobs.extend(simplified_jobs)
                        logging.info(f"Progress: {len(all_jobs)}/{total_jobs} jobs fetched")
                    except Exception as e:
                        logging.error(f"Error processing job batch: {e}:{self.company_name}")

            return all_jobs
        except Exception as e:
            logging.error(f"Error fetching job list: {e}:{self.company_name}")


    def __del__(self):
        """确保 HTTP 客户端被正确关闭"""
        if hasattr(self, 'client'):
            self.client.close()

