#!/usr/bin/env python3
"""
简单日志测试脚本 - test_simple_logger.py

测试基本的日志功能
"""

import os
import sys

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_basic_import():
    """测试基本导入"""
    print("测试1: 基本导入")
    try:
        from utils.colored_logger import log_info, log_success, log_error, log_warn, log_hint
        print("✅ 导入成功")
        return True
    except Exception as e:
        print(f"❌ 导入失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_basic_logging():
    """测试基本日志功能"""
    print("\n测试2: 基本日志功能")
    try:
        from utils.colored_logger import log_info, log_success, log_error, log_warn, log_hint
        
        print("测试各种级别的日志:")
        log_info("这是 INFO 级别的日志")
        log_hint("这是 HINT 级别的日志")
        log_success("这是 SUCCESS 级别的日志")
        log_warn("这是 WARN 级别的日志")
        log_error("这是 ERROR 级别的日志")
        
        print("✅ 基本日志功能测试成功")
        return True
    except Exception as e:
        print(f"❌ 基本日志功能测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_level_control():
    """测试级别控制"""
    print("\n测试3: 级别控制")
    try:
        from utils.colored_logger import set_console_level, log_info, log_hint, log_warn, log_error
        
        print("设置控制台级别为 WARN")
        set_console_level('WARN')
        
        print("测试输出（应该只显示 WARN 和 ERROR）:")
        log_info("INFO 级别 - 不应该显示")
        log_hint("HINT 级别 - 不应该显示")
        log_warn("WARN 级别 - 应该显示")
        log_error("ERROR 级别 - 应该显示")
        
        print("✅ 级别控制测试成功")
        return True
    except Exception as e:
        print(f"❌ 级别控制测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🎨 简单日志功能测试")
    print("=" * 50)
    
    success_count = 0
    total_tests = 3
    
    if test_basic_import():
        success_count += 1
    
    if test_basic_logging():
        success_count += 1
    
    if test_level_control():
        success_count += 1
    
    print("\n" + "=" * 50)
    print(f"测试结果: {success_count}/{total_tests} 通过")
    
    if success_count == total_tests:
        print("✅ 所有测试通过")
    else:
        print("❌ 部分测试失败")

if __name__ == "__main__":
    main()
