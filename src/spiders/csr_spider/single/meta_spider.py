from urllib.parse import urlparse, urljoin

from spiders.core_spider.base_spider import BaseSpider
from utils.country_utils import CountryCodeUtils
import logging
from concurrent.futures import ThreadPoolExecutor, as_completed
import random
import time
from datetime import datetime

from utils.http_utils.http_client import HTTPClient
# from utils.get_fail_length import append_to_csv,append_to_detail_csv


from bs4 import BeautifulSoup


# meta模板方法
class MetaSpider(BaseSpider):
    def __init__(self, company_name, base_url, company_url, cwiq_code):
        super().__init__(company_name, base_url, company_url, cwiq_code)
        self.company_name = company_name
        self.company_url = company_url
        self.cwiq_code = cwiq_code
        self.country_utils = CountryCodeUtils()

        self.headers = {
            'accept': '*/*',
            'accept-language': 'zh-CN,zh;q=0.9',
            'cache-control': 'no-cache',
            'content-type': 'application/x-www-form-urlencoded',
            'cookie': 'datr=DtSIZ8dzOeOEuHtd6Z0Df50t; wd=1640x375',
            'origin': 'https://www.metacareers.com',
            'pragma': 'no-cache',
            'priority': 'u=1, i',
            'referer': 'https://www.metacareers.com/jobs',
            'sec-ch-ua': '"Google Chrome";v="131", "Chromium";v="131", "Not_A Brand";v="24"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"Windows"',
            'sec-fetch-dest': 'empty',
            'sec-fetch-mode': 'cors',
            'sec-fetch-site': 'same-origin',
            'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/131.0.0.0 Safari/537.36',
            'x-asbd-id': '129477',
            'x-fb-friendly-name': 'CareersJobSearchResultsQuery',
            'x-fb-lsd': 'AVrQFuMQiA0',
        }

        # 创建 HTTP 客户端，使用全局代理配置
        self.client = HTTPClient()

    @classmethod
    def create_spider(cls, company_name, base_url, company_url, cwiq_code):
        """工厂方法创建 MedtronicSpider 实例"""
        return cls(company_name, base_url, company_url, cwiq_code)

    def make_request(self, method, url, company_name,requset_count,**kwargs):
        """发送请求的通用方法"""
        kwargs['headers'] = self.headers
        return self.client.request(method, url, company_name,requset_count,log_ip=False, **kwargs)

    def get_total_jobs(self):
        """获取总职位数"""
        data = {
            'av': '0',
            '__user': '0',
            '__a': '1',
            '__req': '3',
            '__hs': '20104.BP:DEFAULT.*******.0',
            'dpr': '1',
            '__ccg': 'UNKNOWN',
            '__rev': '1019372895',
            '__s': 'd25o22:48kxuo:9vbfe0',
            '__hsi': '7460448212951120677',
            '__dyn': '7xeUmwkHg7ebwKBAg5S1Dxu13wqovzEdEc8uxa1twKzobo1nEhwem0nCq1ewcG0RU2Cwooa81VohwnU14E9k2C0sy0H82NxCawcK1iwmE2ewnE2Lw5XwSyES4E3PwbS1Lwqo3cwbq0x8qw53wtU5K0zU5a',
            '__csr': '',
            'lsd': 'AVrQFuMQiA0',
            'jazoest': '2909',
            '__spin_r': '1019372895',
            '__spin_b': 'trunk',
            '__spin_t': '1737020959',
            '__jssesw': '1',
            'fb_api_caller_class': 'RelayModern',
            'fb_api_req_friendly_name': 'CareersJobSearchResultsQuery',
            'variables': '{"search_input":{"q":null,"divisions":[],"offices":[],"roles":[],"leadership_levels":[],"saved_jobs":[],"saved_searches":[],"sub_teams":[],"teams":[],"is_leadership":false,"is_remote_only":false,"sort_by_new":false,"results_per_page":null}}',
            'server_timestamps': 'true',
            'doc_id': '9114524511922157',
        }
        try:
            response = self.make_request('POST', self.base_url, self.company_name, requset_count=3,data=data)
            return len(response.json()['data']['job_search'])
        except Exception as e:
            logging.error(f"Error getting total jobs: {e} for {self.company_name}")
            return 0

    def parse_job(self, job_data):
        """解析职位数据"""
        try:
            if job_data:
                # 解析地点信息
                location = job_data.get("locations", [])
                state = location[0] if location else ""
                city = state.split(",")[0] if state else ""
                # country = location.get("country", "") if location else ""
                # country_code = self.country_utils.get_alpha3(country)
                # city = location.get("city", "") if location else ""
                # city = city.split(",")[0].strip() if city else ""
                # state = location.get("region", "") if location else ""
                return {
                    "jobId": job_data.get("id", ""),
                    "company": self.company_name,
                    "cwiq_code": self.cwiq_code,
                    "companyUrl": self.company_url,
                    # "country": country_code,
                    "city": city,
                    "jobTitle": job_data.get("title", ""),
                    # "department": job_info.get("hiringOrganization", {}).get("name", ""),
                    "location": state,
                    "jobDescription": job_data.get("description", ""),
                    "jobUrl": self.company_url + "/" + job_data.get("id", ""),
                    "created_time": None,
                    "updated_time": None,
                    "post_time": datetime.now(),
                }
        except Exception as e:
            logging.error(f"Error parsing job data: {e} for {self.company_name}")
            return None

    def fetch_job_details(self, job_data):
        """获取职位详细信息"""
        try:
            job_id = job_data.get('id', '')
            fail_list = []
            time.sleep(random.uniform(0.5, 1))
            job_url = f"{self.company_url}/{job_id}"
            response = self.make_request('GET', job_url, self.company_name,requset_count=3,)
            # 获取职位描述
            soup = BeautifulSoup(response.text, 'html.parser')
            description_1 = soup.find(class_='_1n-_ _6hy- _94t2')
            description_2 = soup.find_all(class_='_8mlh')[:3]
            description = description_1.text if description_1 else ""
            for desc in description_2:
                description += desc.text
            job_data['description'] = description
            return job_data
        except Exception as e:
            logging.error(f"Error fetching job details for {job_id}: {e}")
            fail_list.append({
                "method": 'GET',
                "url": f"{self.company_url}/{job_id}",
                "company_name": self.company_name,
                "job_data":job_data
            })
            return fail_list

    def fetch_jobs(self,max_workers):
        """获取所有职位信息"""
        try:
            total_jobs = self.get_total_jobs()
            if total_jobs == 0:
                logging.info(f"No jobs found {self.company_name}")
                return [],0

            logging.info(f"Found {total_jobs} total jobs")

            # 获取上一批次的职位数据
            latest_batch = self.db_ops.get_latest_batch(self.company_url)
            batch_id = latest_batch.batchId if latest_batch else 0
            previous_job_ids = set(self.db_ops.get_job_ids_by_batch(
                self.company_url,
                batch_id,
                status=[0, 1]
            ))

            all_jobs = self._fetch_job_listings(total_jobs)

            # 从现有职位中提取所有job_ids
            current_job_ids = {
                job.get('id', '')
                for job in all_jobs
            }

            # 找出重复的job_ids
            repeated_job_ids = current_job_ids.intersection(previous_job_ids)
            new_job_ids = current_job_ids - previous_job_ids

            # 一次性获取所有重复职位的数据
            repeated_jobs_dict = self.db_ops.get_jobs_by_ids(list(repeated_job_ids))

            # 分离新职位和重复职位
            new_jobs = [
                job for job in all_jobs
                if job.get('id', '') in new_job_ids
            ]
            repeated_jobs = list(repeated_jobs_dict.values())

            logging.info(f"Found {len(new_jobs)} new jobs and {len(repeated_jobs)} repeated jobs")
            num_job_1 = []
            # 只处理新职位的详细信息
            processed_jobs = []
            if new_jobs:
                with ThreadPoolExecutor(max_workers=max_workers) as executor:
                    future_to_job = {
                        executor.submit(self.fetch_job_details, job): job
                        for job in new_jobs
                    }

                    completed = 0
                    total_new = len(new_jobs)
                    need_retry = []
                    for future in as_completed(future_to_job):
                        job_details = future.result()
                        if isinstance(job_details, list):
                            need_retry += [(job_details[0]['method'], job_details[0]['url'],job_details[0]['job_data'])]
                        else:
                            if job_details:
                                parsed_job = self.parse_job(job_details)
                                if parsed_job:
                                    processed_jobs.append(parsed_job)
                            completed += 1
                            progress = (completed / total_new) * 100
                            logging.info(f"Processing new job details: {completed}/{total_new} ({progress:.2f}%)")

                num_job_1 += [len(need_retry)]
                attempt = 0
                retries = 2
                while attempt < retries:
                    attempt += 1
                    if need_retry:
                        time.sleep(60)
                        with ThreadPoolExecutor(max_workers=max_workers) as executor:
                            future_to_job = {
                                executor.submit(self.retry_failed_details_requests, job[0], job[1],job[2], retries=1,
                                                delay=0): job
                                for job in need_retry
                            }
                            need_retry = []
                            for future in as_completed(future_to_job):
                                job_details = future.result()
                                if isinstance(job_details, list):
                                    need_retry += [(job_details[0]['method'], job_details[0]['url'],job_details[0]['job_data'])]
                                else:
                                    if job_details:
                                        parsed_job = self.parse_job(job_details)
                                        if parsed_job:
                                            processed_jobs.append(parsed_job)
                                    completed += 1
                                    progress = (completed / total_new) * 100
                                    logging.info(
                                        f"Processing new job details: {completed}/{total_new} ({progress:.2f}%)")
                            num_job_1 += [len(need_retry)]
                logging.info(f"attempt: {attempt}. num_job_1 = {num_job_1}")

            # if num_job_1:
            #     #计算详情页尝试后的结果
            #     self.get_detail_fail_timeout_length(num_job_1)

            # 合并新处理的职位和重复的职位
            all_processed_jobs = processed_jobs + repeated_jobs
            logging.info(f"Successfully processed {len(all_processed_jobs)} total jobs")
            return all_processed_jobs,total_jobs

        except Exception as e:
            logging.error(f"Error in fetch_jobs:{self.company_name}: {str(e)}")
            return [],0

    # def get_detail_fail_timeout_length(self,num_job_1):
    #     model = 'detail'
    #     if len(num_job_1) == 1:
    #         append_to_detail_csv(self.company_name, num_job_1[0], "NaN", "NaN",  model)
    #     if len(num_job_1) == 2:
    #         append_to_detail_csv(self.company_name, num_job_1[0], num_job_1[1], "NaN",  model)
    #     if len(num_job_1) == 3:
    #         append_to_detail_csv(self.company_name, num_job_1[0], num_job_1[1], num_job_1[2],  model)

    def _fetch_job_listings(self, total_jobs):
        """获取所有职位列表"""
        all_jobs = []
        failed_requests_list = []
        fail_jobs =[]
        try:
            payload = {
                'av': '0',
                '__user': '0',
                '__a': '1',
                '__req': '3',
                '__hs': '20104.BP:DEFAULT.*******.0',
                'dpr': '1',
                '__ccg': 'UNKNOWN',
                '__rev': '1019372895',
                '__s': 'd25o22:48kxuo:9vbfe0',
                '__hsi': '7460448212951120677',
                '__dyn': '7xeUmwkHg7ebwKBAg5S1Dxu13wqovzEdEc8uxa1twKzobo1nEhwem0nCq1ewcG0RU2Cwooa81VohwnU14E9k2C0sy0H82NxCawcK1iwmE2ewnE2Lw5XwSyES4E3PwbS1Lwqo3cwbq0x8qw53wtU5K0zU5a',
                '__csr': '',
                'lsd': 'AVrQFuMQiA0',
                'jazoest': '2909',
                '__spin_r': '1019372895',
                '__spin_b': 'trunk',
                '__spin_t': '1737020959',
                '__jssesw': '1',
                'fb_api_caller_class': 'RelayModern',
                'fb_api_req_friendly_name': 'CareersJobSearchResultsQuery',
                'variables': '{"search_input":{"q":null,"divisions":[],"offices":[],"roles":[],"leadership_levels":[],"saved_jobs":[],"saved_searches":[],"sub_teams":[],"teams":[],"is_leadership":false,"is_remote_only":false,"sort_by_new":false,"results_per_page":null}}',
                'server_timestamps': 'true',
                'doc_id': '9114524511922157',
            }
            response = self.make_request('POST', self.base_url, self.company_name,requset_count=3, data=payload)
            jobs = response.json()['data']['job_search']
            all_jobs.extend(jobs)
            logging.info(f"Progress: {len(all_jobs)}/{total_jobs} jobs fetched")
        except Exception as e:
            logging.error(f"Error processing job batch: {e}：{self.company_name}")
            failed_requests_list.append({
                "method": 'POST',
                "url": self.base_url,
                "company_name": self.company_name,
                "payload": payload,
            })
            # if failed_requests_list:
            #     for failed_request in failed_requests_list:
            #         fail_jobs = self.retry_request(failed_request['method'], failed_request['url'],
            #                            failed_request['company_name'], retries=3, delay=5,
            #                            json=failed_request['payload'])

        all_jobs = all_jobs + fail_jobs
        return all_jobs

    # def retry_request(self, method, url, company_name, retries=3, delay=5, **kwargs):
    #     attempt = 0
    #     fail_jobs  =[]
    #
    #     while attempt < retries:
    #         try:
    #             response = self.make_request(method, url, company_name, **kwargs)
    #             jobs = response.json()['data']['job_search']
    #             fail_jobs.extend(jobs)
    #
    #             return fail_jobs
    #         except Exception as e:
    #             attempt += 1
    #             logging.error(f"Error on attempt {attempt} for {company_name}: {e}")
    #             if attempt < retries:
    #                 logging.info(f"Retrying in {delay} seconds...")
    #             else:
    #                 logging.error(f"All {retries} attempts failed for {company_name}")
    #                 return None

    def retry_failed_details_requests(self, method, url, job_data, retries=3, delay=5, **kwargs):
        fail_two_list = []
        attempt = 0
        while attempt < retries:
            try:
                response = self.make_request(method, url, self.company_name,requset_count=1,)
                # 获取职位描述
                soup = BeautifulSoup(response.text, 'html.parser')
                description_1 = soup.find(class_='_1n-_ _6hy- _94t2')
                description_2 = soup.find_all(class_='_8mlh')[:3]
                description = description_1.text if description_1 else ""
                for desc in description_2:
                    description += desc.text
                job_data['description'] = description
                return job_data

            except Exception as e:
                attempt += 1
                logging.error(f"Error on attempt {attempt} for {self.company_name}: {e}")
                if attempt < retries:
                    logging.info(f"Retrying in {delay} seconds...")
                else:
                    logging.error(f"All {retries} attempts failed for {self.company_name}")
                    fail_two_list.append({
                        "method": method,
                        "url": url,
                        "company_name": self.company_name,
                        "job_data": job_data,
                    })
                    return fail_two_list

    def __del__(self):
        """确保 HTTP 客户端被正确关闭"""
        if hasattr(self, 'client'):
            self.client.close()