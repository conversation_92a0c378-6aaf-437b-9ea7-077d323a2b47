from spiders.core_spider.base_spider import BaseSpider
from utils.country_utils import CountryCodeUtils
import logging
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed
import random
import time
from datetime import datetime

from utils.http_utils.http_client import HTTPClient
# from utils.get_fail_length import append_to_csv, append_to_detail_csv


# work day的模板方法
def _simplify_job_data(job, url):
    """
    将原始职位数据简化为只包含必要信息的格式

    Args:
        job (dict): 原始职位数据

    Returns:
        dict: 简化后的职位数据，只包含ID和路径信息
    """
    try:
        externalPath = job.get('externalPath', None)
        jid = job.get('bulletFields',None)[0]

    except Exception as e:
        logging.error(f"Error simplifying job data: {e}:{url}!!!!!!!!!!!!!{job}")
        return {
            'id': None,  # REQ号作为ID
            'externalPath': externalPath
        }

    return {
        'id': jid,  # REQ号作为ID
        'externalPath': externalPath
    }


class WorkDayId0Spider(BaseSpider):
    def __init__(self, company_name, base_url, company_url, cwiq_code):
        super().__init__(company_name, base_url, company_url, cwiq_code)
        self.company_name = company_name
        self.company_url = company_url
        self.cwiq_code = cwiq_code
        self.country_utils = CountryCodeUtils()
        self.headers = {
            "Content-Type": "application/json",
            "Accept": "application/json",
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
        }
        self.job_url_base = self.base_url.rsplit('/', 1)[0]

        # 创建 HTTP 客户端，使用全局代理配置
        self.client = HTTPClient()

    @classmethod
    def create_spider(cls, company_name, base_url, company_url, cwiq_code):
        """工厂方法创建 MedtronicSpider 实例"""
        return cls(company_name, base_url, company_url, cwiq_code)

    def make_request(self, method, url, company_name, requset_count, **kwargs):
        """发送请求的通用方法"""
        kwargs['headers'] = self.headers
        return self.client.request(method, url, company_name, requset_count, log_ip=False, **kwargs)

    def get_total_jobs(self):
        """获取总职位数"""
        try:
            payload = {
                "appliedFacets": {},
                "limit": 20,
                "offset": 0,
                "searchText": ""
            }
            response = self.make_request('POST', self.base_url, self.company_name, requset_count=3, json=payload)
            if response.json().get('errorCode'):
                return 0
            else:
                return response.json()['total']
        except Exception as e:
            logging.error(f"Error getting total jobs: {e} for {self.company_name}")
            return 0

    def parse_job(self, job_data):
        """解析职位数据"""
        try:
            if job_data:
                job_info = job_data.get("jobPostingInfo", {})
                # 解析地点信息
                location = job_info.get("location", "")
                country_name = job_info.get("country", {}).get("descriptor", "")
                country_code = self.country_utils.get_alpha3(country_name)
                city = location.split(",")[0] if location else ""
                state = location.split(",")[1] if location and len(location.split(",")) == 3 else ""
                return {
                    "jobId": job_info.get("jobReqId", ""),
                    "company": self.company_name,
                    "cwiq_code": self.cwiq_code,
                    "companyUrl": self.company_url,
                    "country": country_code,
                    "city": city,
                    "jobTitle": job_info.get("title", ""),
                    "department": job_info.get("hiringOrganization", {}).get("name", ""),
                    "location": state,
                    "jobDescription": job_info.get("jobDescription", ""),
                    "jobUrl": job_info.get("externalUrl", ""),
                    "created_time": None,
                    "updated_time": None,
                    "post_time": datetime.now(),
                }
        except Exception as e:
            logging.error(f"Error parsing job data: {e} for {self.company_name}")
            return None

    def fetch_job_details(self, external_path):
        """获取职位详细信息"""
        if external_path:
            fail_list = []
            try:
                time.sleep(random.uniform(0.5, 1))
                job_url = f"{self.job_url_base}{external_path}"
                response = self.make_request('GET', job_url, self.company_name, requset_count=3, )
                return response.json()
            except Exception as e:
                logging.error(f"Error fetching job details for : {e} for {self.company_name}")
                fail_list.append({
                    "method": 'GET',
                    "url": f"{self.job_url_base}{external_path}",
                    "company_name": self.company_name,
                })
                return fail_list

    def fetch_jobs(self, max_workers):
        """获取所有职位信息"""
        try:
            all_failed_jobs = []
            all_failed_page = []
            total_jobs = self.get_total_jobs()
            if total_jobs == 0:
                logging.info(f"No jobs found {self.company_name}")
                return [],0

            logging.info(f"Found {total_jobs} total jobs for {self.company_name}")

            # 获取并组织职位数据
            all_jobs, previous_job_ids, failed_requests_list, (
                cnt_failed_page, cnt_failed_jobs) = self.fetch_and_organize_jobs(
                total_jobs)
            # previous_job_ids, previous_job_ids_active
            # assert set(previous_job_ids) == set(previous_job_ids_active)

            if failed_requests_list:
                fail_jobs, failed_requests, (all_failed_jobs, all_failed_page) = self.retry_failed_requests(
                    failed_requests_list)

                # 去重
                unique_jobs = fail_jobs + all_jobs
                all_unique_jobs = self.list_handle_list(unique_jobs)

                all_processed_jobs, num_job_1 = self.process_job_details(all_unique_jobs, previous_job_ids,
                                                                         max_workers)
            else:
                all_jobs = self.list_handle_list(all_jobs)
                # 处理职位详细信息
                all_processed_jobs, num_job_1 = self.process_job_details(all_jobs, previous_job_ids,
                                                                         max_workers)

            # if num_job_1:
            #     # 计算详情页尝试后的结果
            #     self.get_detail_fail_timeout_length(num_job_1)
            #
            # if failed_requests_list:
            #     # 计算重试次数以及重试后结果
            #     self.get_page_fail_timeout_length((cnt_failed_page, cnt_failed_jobs),
            #                                       (all_failed_jobs, all_failed_page))
            return all_processed_jobs,total_jobs

        except Exception as e:
            logging.error(f"Error in fetch_jobs: {e}:{self.company_name}")
            return [],0

    def fetch_and_organize_jobs(self, total_jobs):
        """获取职位列表并按当前批次组织。"""
        try:
            # 获取上一批次的职位数据
            latest_batch = self.db_ops.get_latest_batch(self.company_url)
            batch_id = latest_batch.batchId if latest_batch else 0
            previous_job_ids = set(self.db_ops.get_job_ids_by_batch(
                self.company_url,
                batch_id,
                status=[0, 1]
            ))

            all_jobs, failed_requests_list, (cnt_failed_page, cnt_failed_jobs) = self._fetch_job_listings(total_jobs,previous_job_ids)
            # if failed_requests_list:
            #     fail_jobs, failed_requests = self.retry_failed_requests(failed_requests_list)
            #     # if failed_requests:
            #     #     fail_jobs_two,failed_requests_two = self.retry_failed_requests(failed_requests)
            # else:
            #     fail_jobs = []
            #     failed_requests = []

            # if failed_requests_list:
            #     self.get_page_fail_timeout_length(failed_requests_list, failed_requests, failed_requests_two)

            return all_jobs, previous_job_ids, failed_requests_list, (cnt_failed_page, cnt_failed_jobs)

        except Exception as e:
            logging.error(f"Error in fetch_and_organize_jobs: {e}:{self.company_name}")
            return [], set(), [], []

    def process_job_details(self, new_jobs, previous_job_ids, max_workers):
        """处理新职位和重复职位的详细信息。"""
        processed_jobs = []
        try:
            # 从现有职位中提取所有job_ids
            current_job_ids = {job.get('id', '') for job in new_jobs}

            # 找出重复的job_ids
            repeated_job_ids = current_job_ids.intersection(previous_job_ids)
            new_job_ids = current_job_ids - previous_job_ids

            # 一次性获取所有重复职位的数据
            repeated_jobs_dict = self.db_ops.get_jobs_by_ids(list(repeated_job_ids))

            # 分离新职位和重复职位
            new_jobs = [job for job in new_jobs if job.get('id', '') in new_job_ids]
            repeated_jobs = list(repeated_jobs_dict.values())

            logging.info(f"Found {len(new_jobs)} new jobs and {len(repeated_jobs)} repeated jobs for {self.company_name}")
            num_job_1 = []
            # 只处理新职位的详细信息
            if new_jobs:
                new_jobs = self.list_handle_list(new_jobs)
                # 去重
                with ThreadPoolExecutor(max_workers=max_workers) as executor:
                    future_to_job = {
                        executor.submit(self.fetch_job_details, job.get('externalPath', '')): job
                        for job in new_jobs
                    }

                    completed = 0
                    total_new = len(new_jobs)
                    need_retry = []
                    for future in as_completed(future_to_job):
                        job_details = future.result()
                        if isinstance(job_details, list):
                            need_retry += [(job_details[0]['method'], job_details[0]['url'])]
                        else:
                            if job_details:
                                parsed_job = self.parse_job(job_details)
                                if parsed_job:
                                    processed_jobs.append(parsed_job)
                                    completed += 1
                            progress = (completed / total_new) * 100
                            logging.info(f"Processing new job details: {completed}/{total_new} ({progress:.2f}%)")

                num_job_1 += [len(need_retry)]
                attempt = 0
                retries = 2
                while attempt < retries:
                    attempt += 1
                    if need_retry:
                        time.sleep(60)
                        with ThreadPoolExecutor(max_workers=max_workers) as executor:
                            future_to_job = {
                                executor.submit(self.retry_failed_details_requests, job[0], job[1], retries=1,
                                                delay=0): job
                                for job in need_retry
                            }
                            need_retry = []
                            for future in as_completed(future_to_job):
                                job_details = future.result()
                                if isinstance(job_details, list):
                                    need_retry += [(job_details[0]['method'], job_details[0]['url'])]
                                else:
                                    if job_details:
                                        parsed_job = self.parse_job(job_details)
                                        if parsed_job:
                                            processed_jobs.append(parsed_job)
                                            completed += 1
                                    progress = (completed / total_new) * 100
                                    logging.info(
                                        f"Processing new job details: {completed}/{total_new} ({progress:.2f}%)")
                            num_job_1 += [len(need_retry)]
                logging.info(f"attempt: {attempt}. num_job_1 = {num_job_1}")

            # 合并新处理的职位和重复的职位
            all_processed_jobs = processed_jobs + repeated_jobs
            logging.info(f"Successfully processed {len(all_processed_jobs)} total jobs")
            return all_processed_jobs, num_job_1

        except Exception as e:
            logging.error(f"Error in process_job_details: {e}:{self.company_name}")
            return []

    def list_handle_list(self, unique_jobs):
        # 已经出现过的 id
        seen_ids = set()
        # 重后的 job
        all_unique_jobs = []

        for job in unique_jobs:
            job_id = job.get('id')
            if job_id not in seen_ids and job.get('externalPath') is not None:
                seen_ids.add(job_id)
                all_unique_jobs.append(job)
        return all_unique_jobs

    # def get_detail_fail_timeout_length(self, num_job_1):
    #     model = 'detail'
    #     if len(num_job_1) == 1:
    #         append_to_detail_csv(self.company_name, num_job_1[0], "NaN", "NaN", model)
    #     if len(num_job_1) == 2:
    #         append_to_detail_csv(self.company_name, num_job_1[0], num_job_1[1], "NaN", model)
    #     if len(num_job_1) == 3:
    #         append_to_detail_csv(self.company_name, num_job_1[0], num_job_1[1], num_job_1[2], model)
    #
    # def get_page_fail_timeout_length(self, failed_requests_list, failed_requests):
    #     model = 'page'
    #     second_jobs = 0
    #     second_page = 0
    #     cnt_failed_page, cnt_failed_jobs = failed_requests_list[0], failed_requests_list[1]
    #     all_failed_jobs, all_failed_page = failed_requests[0], failed_requests[1]
    #     first_jobs = all_failed_jobs[0]
    #     first_page = all_failed_page[0]
    #     if len(all_failed_jobs) == 2:
    #         second_jobs = all_failed_jobs[1]
    #     if len(all_failed_page) == 2:
    #         second_page = all_failed_page[1]
    #     append_to_csv(self.company_name, cnt_failed_jobs, cnt_failed_page, first_jobs, first_page, second_jobs,
    #                   second_page, model)

    def _fetch_job_listings(self, total_jobs, previous_job_ids):
        """获取所有职位列表，增加超时重试机制"""
        try:
            jobs_per_request = 20
            total_requests = (total_jobs // jobs_per_request) + (1 if total_jobs % jobs_per_request else 0)

            all_jobs = []  # 存两次拉取结果的并集
            failed_requests_list = []

            max_attempts = 2  # 最多执行2次

            for attempt in range(max_attempts):
                current_jobs = []  # 当前这一轮拉取的职位列表
                failed_requests_list.clear()  # 每次重新拉取时清空失败列表

                with ThreadPoolExecutor(max_workers=total_requests) as executor:
                    future_to_payload = {}

                    for i in range(total_requests):
                        payload = {
                            "appliedFacets": {},
                            "limit": jobs_per_request,
                            "offset": i * jobs_per_request,
                            "searchText": ""
                        }
                        future = executor.submit(
                            self.make_request,
                            'POST',
                            self.base_url,
                            self.company_name,
                            requset_count=3,
                            json=payload
                        )
                        future_to_payload[future] = payload

                    cnt_failed_jobs = 0
                    cnt_failed_page = 0

                    for future in as_completed(future_to_payload):
                        simplified_jobs = []
                        failed_page_flag = False  # 标记当前页是否失败

                        try:
                            response = future.result()
                            jobs = response.json()['jobPostings']
                            simplified_jobs = [_simplify_job_data(job, self.job_url_base) for job in jobs]
                            current_jobs.extend(simplified_jobs)
                            logging.info(f"Progress: {len(current_jobs)}/{total_jobs} jobs fetched")

                        except Exception as e:
                            logging.error(f"Error processing job batch: {e}:{self.company_name}")
                            failed_page_flag = True

                        for _job in simplified_jobs:
                            if _job['id'] is None:
                                logging.error(f"{self.job_url_base}!!!!!!!!!!!!!{_job}")
                                cnt_failed_jobs += 1
                                failed_page_flag = True
                            elif _job['externalPath'] is None:
                                if _job['id'] in previous_job_ids:
                                    continue
                                logging.error(f"{self.job_url_base}!!!!!!!!!!!!!{_job}")
                                cnt_failed_jobs += 1
                                failed_page_flag = True

                        if failed_page_flag:
                            failed_requests_list.append(future_to_payload[future])
                            cnt_failed_page += 1

                current_jobs = self.list_handle_list(current_jobs)
                all_jobs.extend(current_jobs)

                # 如果是第一次执行，判断是否需要进行第二次
                if attempt == 0:
                    if len(current_jobs) == len(previous_job_ids):
                        logging.info("no retry")
                        break
                    else:
                        logging.info("need retry")
                        continue
            all_jobs = self.list_handle_list(all_jobs)
            return all_jobs, failed_requests_list, (cnt_failed_page, cnt_failed_jobs)

        except Exception as e:
            logging.error(f"Error processing job batch: {e}:{self.company_name}")

    def retry_failed_requests(self, payload_list, max_retries=2):
        try:
            fail_jobs = []
            retries = 0
            all_failed_jobs = []
            all_failed_page = []
            # 逐步重试
            while retries < max_retries:
                if payload_list:

                    logging.info(
                        f"Retrying {len(payload_list)} failed requests... (Attempt {retries + 1}/{max_retries})")

                    # 增加一定的延迟以避免过多请求同时重试
                    time.sleep(300)

                    # 创建一个新的列表，用来存储重试后的成功请求
                    new_failed_requests = []

                    # 重试失败的请求
                    with ThreadPoolExecutor(max_workers=len(payload_list)) as executor:
                        fail_future_to_payload = {}
                        for payload in payload_list:
                            time.sleep(10)
                            future = executor.submit(
                                self.make_request,
                                'POST',
                                self.base_url,
                                self.company_name,
                                requset_count=3,
                                json=payload)  # 使用自定义的重试方法

                            fail_future_to_payload[future] = payload

                        cnt_failed_jobs = 0
                        cnt_failed_page = 0
                        # 处理每个请求的结果
                        for future in as_completed(fail_future_to_payload):
                            failed_page_flag = False
                            try:
                                response = future.result()
                                jobs = response.json()['jobPostings']
                                simplified_jobs = [_simplify_job_data(job, self.job_url_base) for job in jobs]
                                fail_jobs.extend(simplified_jobs)
                                logging.info(f"Progress: {len(fail_jobs)} jobs fetched (after retry)")
                            except Exception as e:
                                logging.error(f"Error processing retry job batch: {e}")
                                failed_page_flag = True

                            for _job in simplified_jobs:
                                if _job['id'] is None or _job['externalPath'] is None:
                                    cnt_failed_jobs += 1
                                    failed_page_flag = True
                            if failed_page_flag:
                                # 如果请求仍然失败，则加入待重试列表
                                new_failed_requests.append(fail_future_to_payload[future])
                                cnt_failed_page += 1

                    # 更新待重试请求列表
                    payload_list = new_failed_requests
                    all_failed_jobs.append(cnt_failed_jobs)
                    all_failed_page.append(cnt_failed_page)

                retries += 1
                # 如果在最大重试次数内都失败了，跳出循环
                if retries >= max_retries:
                    if payload_list:
                        logging.error(f"Max retries reached for failed requests: {len(payload_list)}")
                    else:
                        logging.info(f"All failed requests have been retried successfully.")

            return fail_jobs, payload_list, (all_failed_jobs, all_failed_page)
        except Exception as e:
            logging.error(f"Error retry_failed_requests for {self.company_name}: {e}")

    def retry_failed_details_requests(self, method, url, retries=3, delay=5, **kwargs):
        if url:
            fail_two_list = []
            attempt = 0
            while attempt < retries:
                try:
                    response = self.make_request(method, url, self.company_name, requset_count=3, )
                    return response.json()

                except Exception as e:
                    attempt += 1
                    logging.error(f"Error on attempt {attempt} for {self.company_name}: {e}")
                    if attempt < retries:
                        logging.info(f"Retrying in {delay} seconds...")
                        time.sleep(delay)
                    else:
                        logging.error(f"All {retries} attempts failed for {self.company_name}")
                        fail_two_list.append({
                            "method": method,
                            "url": url,
                            "company_name": self.company_name,
                        })
                        return fail_two_list

    def __del__(self):
        """确保 HTTP 客户端被正确关闭"""
        if hasattr(self, 'client'):
            self.client.close()
