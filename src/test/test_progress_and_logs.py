#!/usr/bin/env python3
"""
进度条和彩色日志测试脚本 - test_progress_and_logs.py

测试修复后的进度条显示和彩色日志功能
"""

import os
import sys
import time

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.progress_bar import create_progress_bar, format_progress_message
from utils.colored_logger import (
    log_success, log_error, loglog_warnog_info, log_hint,
    log_spider_result, log_exception
)


def test_progress_bar():
    """测试进度条功能"""
    print("\n" + "=" * 60)
    print("测试进度条功能")
    print("=" * 60)

    # 模拟爬虫处理进度
    total_spiders = 2131

    for i in range(0, total_spiders + 1, 200):
        if i > total_spiders:
            i = total_spiders

        message = format_progress_message(i, total_spiders, "Processing spiders")
        print(message)
        time.sleep(0.1)  # 模拟处理时间


def test_colored_logs():
    """测试彩色日志功能"""
    print("\n" + "=" * 60)
    print("测试彩色日志功能")
    print("=" * 60)

    # 测试不同类型的日志
    log_success("爬虫系统启动成功")
    log_hint("开始加载配置文件...")
    log_success("已加载 2131 个爬虫配置")

    # 模拟一些错误
    log_warn("JSON解析错误: Invalid JSON format")
    log_error("数据库连接失败: Connection timeout")
    log_hint("正在重试连接...")
    log_success("数据库连接恢复")

    # 测试爬虫结果日志
    log_spider_result("Microsoft", "success")
    log_spider_result("Google", "error", "database connection failed")
    log_spider_result("Amazon", "error", "html parse error")
    log_spider_result("Tesla", "timeout", "request timeout after 30s")


def test_exception_logging():
    """测试异常日志功能"""
    print("\n" + "=" * 60)
    print("测试异常日志功能")
    print("=" * 60)

    # 模拟不同类型的异常
    try:
        import json
        json.loads("invalid json")
    except json.JSONDecodeError as e:
        log_exception("JSON parsing failed", e)

    try:
        open("/nonexistent/file.txt", "r")
    except FileNotFoundError as e:
        log_exception("File operation failed", e)


def test_mixed_scenario():
    """测试混合场景 - 模拟真实的爬虫执行过程"""
    print("\n" + "=" * 60)
    print("测试混合场景 - 模拟爬虫执行")
    print("=" * 60)

    total = 2131

    log_hint("Starting spider processing...")
    log_success(f"Loaded {total} spider configs")

    # 模拟处理进度
    for current in [0, 500, 1000, 1500, 2000, 2131]:
        if current > 0:
            # 模拟一些随机的成功和错误
            if current == 500:
                log_warn("JSON decode error in response from api.example.com")
            elif current == 1000:
                log_error("Database connection lost, retrying...")
                log_success("Database connection restored")
            elif current == 1500:
                log_hint("Processing continues...")

        # 显示进度
        message = format_progress_message(current, total, "Progress")
        log_hint(message)
        time.sleep(0.2)

    success_count = 2050
    log_success(f"All processing completed: {success_count}/{total} spiders succeeded")


def main():
    """主函数"""
    print("🎨 Marzaha 爬虫系统 - 进度条和彩色日志测试")
    print("📋 测试内容:")
    print("   1. 进度条显示功能")
    print("   2. 彩色日志输出")
    print("   3. 异常日志处理")
    print("   4. 混合场景模拟")
    print()

    # 运行所有测试
    test_progress_bar()
    test_colored_logs()
    test_exception_logging()
    test_mixed_scenario()

    print("\n" + "=" * 60)
    print("✅ 进度条和彩色日志测试完成")
    print("💡 提示: 在支持ANSI颜色的终端中可以看到彩色效果")
    print("📁 日志文件: /mnt/data1/leo/spider_colored.log")
    print("=" * 60)


if __name__ == "__main__":
    main()
