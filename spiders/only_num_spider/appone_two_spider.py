
import re

from spiders.core_spider.base_spider import BaseSpider
from utils.country_utils import CountryCodeUtils
import logging
from datetime import datetime
from lxml import etree
from utils.http_utils.http_client import HTTPClient


# ultipro的模板方法
class ApponeTwoSpider(BaseSpider):
    def __init__(self, company_name, base_url, company_url, cwiq_code):
        super().__init__(company_name, base_url, company_url, cwiq_code)
        self.company_name = company_name
        self.company_url = company_url
        self.cwiq_code = cwiq_code
        self.country_utils = CountryCodeUtils()
        self.headers = {
    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
    'Accept-Language': 'zh-CN,zh;q=0.9',
    'Cache-Control': 'max-age=0',
    'Connection': 'keep-alive',
    'Content-Type': 'application/x-www-form-urlencoded',
    'Origin': 'https://www2.appone.com',
    'Referer': 'https://www2.appone.com/Search/Search.aspx?ServerVar=muthelectricinc.appone.com',
    'Sec-Fetch-Dest': 'document',
    'Sec-Fetch-Mode': 'navigate',
    'Sec-Fetch-Site': 'same-origin',
    'Sec-Fetch-User': '?1',
    'Upgrade-Insecure-Requests': '1',
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36',
    'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"',
    'sec-ch-ua-mobile': '?0',
    'sec-ch-ua-platform': '"Windows"',
    # 'Cookie': 'NotificationCookie=1; ASP.NET_SessionId=bpgjovf3v2vbv50qve35jc14; tsPartner=1334-Muth Electric, Inc.; WebServerNumber=w12; ADRUM=s=1747902518940&r=https%3A%2F%2Fwww2.appone.com%2FSearch%2FSearch.aspx%3F-1414036551',
}
        # 创建 HTTP 客户端，使用全局代理配置
        self.client = HTTPClient()

    @classmethod
    def create_spider(cls, company_name, base_url, company_url, cwiq_code):
        """工厂方法创建 MedtronicSpider 实例"""
        return cls(company_name, base_url, company_url, cwiq_code)

    def make_request(self, method, url, company_name, requset_count,**kwargs):
        """发送请求的通用方法"""
        kwargs['headers'] = self.headers
        return self.client.request(method, url, company_name, requset_count,log_ip=False, **kwargs)

    def get_total_jobs(self):
        """获取总职位数"""
        try:
            params = {
                'ServerVar': 'muthelectricinc.appone.com',
            }

            data = {
                '__EVENTTARGET': '',
                '__EVENTARGUMENT': '',
                '__VIEWSTATE': '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',
                '__VIEWSTATEGENERATOR': '56CCD8F3',
                'ctl00$cphBody$txtKeyword': '',
                'ctl00$cphBody$rbKeyword': 'all',
                'ctl00$cphBody$ddlCategory': '16',
                'ctl00$cphBody$hfCategory': '',
                'ctl00$cphBody$ddlLocation': '505119',
                'ctl00$cphBody$hfLocation': '',
                'ctl00$cphBody$ddlDivision': '148451',
                'ctl00$cphBody$hfDivision': '',
                'ctl00$cphBody$btnSearch': 'Search for Jobs',
                'ctl00$ucFooter$txtBrandingId': '83',
                'ctl00$sst': 'newfull2-navy',
                'ctl00$CountryID': '3',
                'ctl00$fb': '',
                'ctl00$ServerVar': 'muthelectricinc.appone.com',
                'ctl00$LanguageID': '2',
            }

            response = self.make_request('POST', self.base_url, self.company_name,requset_count=3,params=params,data=data)
            tree = etree.HTML(response.text)
            total = tree.xpath(
                "//table[@id='cphBody_tableOuterResultsContent']/tr/td[2]/div[@id='cphBody_UpdatePanelReqs']/table[@class='tableContent'][1]/tr[2]/td[@id='cphBody_tdFoundText']/b//text()")[
                0]
            result = re.findall(r'\d+', total)
            total = int(result[0])
            return total
        except Exception as e:
            logging.error(f"Error getting total jobs: {e} for {self.company_name}")
            return 0

    def parse_job(self, job_data):
        """解析职位数据"""
        pass

    def fetch_jobs(self, max_workers):
        """获取所有职位信息"""
        try:
            total_jobs = self.get_total_jobs()
            if total_jobs == 0:
                logging.info(f"No jobs found {self.company_name}")
                return [], 0

            logging.info(f"Found {total_jobs} total jobs")
            return [], total_jobs
        except Exception as e:
            logging.error(f"Error in fetch_jobs: {e}:{self.company_name}")
            return [], 0

    def __del__(self):
        """确保 HTTP 客户端被正确关闭"""
        if hasattr(self, 'client'):
            self.client.close()
