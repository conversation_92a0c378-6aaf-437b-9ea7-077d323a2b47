# Marzaha 爬虫项目 Makefile
# 提供常用的项目管理命令

# 虚拟环境路径配置
VENV_DIR = ../venv
PYTHON = $(VENV_DIR)/bin/python
PIP = $(VENV_DIR)/bin/pip

# 检查虚拟环境是否存在的函数
define check_venv
	@if [ ! -f "$(PYTHON)" ]; then \
		echo "❌ 错误: 虚拟环境不存在或Python不可用"; \
		echo "   路径: $(PYTHON)"; \
		echo "   请先运行: make venv && make install"; \
		exit 1; \
	fi
endef

.PHONY: help install install-tested install-simple install-core install-prod install-dev install-full \
        check status config-check test lint format clean run run-once scheduler db-init docs venv \
        security deps-check complexity benchmark ci release quickstart \
        health monitor backup restore cleanup emergency-stop

# 默认目标
help:
	@echo "Marzaha 爬虫项目管理命令:"
	@echo ""
	@echo "🐍 虚拟环境:"
	@echo "  venv            - 创建虚拟环境 (首次使用必须)"
	@echo ""
	@echo "📦 安装相关:"
	@echo "  install         - 默认安装 (等同于install-tested)"
	@echo "  install-tested  - 安装测试验证版本 (推荐)"
	@echo "  install-simple  - 简化安装 (避免编译问题)"
	@echo "  install-core    - 安装核心依赖"
	@echo "  install-prod    - 安装生产环境依赖"
	@echo "  install-dev     - 安装开发环境依赖"
	@echo "  install-full    - 安装完整依赖"
	@echo ""
	@echo "🔍 检查和修复:"
	@echo "  check           - 检查安装状态"
	@echo "  status          - 系统状态检查"
	@echo "  config-check    - 配置文件检查"
	@echo ""
	@echo "🛠️  开发相关:"
	@echo "  test            - 运行测试"
	@echo "  lint            - 代码检查"
	@echo "  format          - 代码格式化"
	@echo "  clean           - 清理临时文件"
	@echo ""
	@echo "🚀 运行相关:"
	@echo "  run             - 运行爬虫系统 (带监控和自动重启)"
	@echo "  run-once        - 运行爬虫系统 (单次执行)"
	@echo "  scheduler       - 运行调度器"
	@echo ""
	@echo "🗄️  数据库相关:"
	@echo "  db-init         - 初始化数据库表"
	@echo ""
	@echo "🔧 其他工具:"
	@echo "  docs            - 生成文档"
	@echo "  security        - 安全检查"
	@echo "  deps-check      - 依赖安全检查"
	@echo "  complexity      - 代码复杂度分析"
	@echo "  benchmark       - 性能测试"
	@echo ""
	@echo "🏥 稳定性工具:"
	@echo "  health          - 系统健康检查"
	@echo "  monitor         - 启动系统监控"
	@echo "  backup          - 创建系统备份"
	@echo "  restore         - 恢复系统备份"
	@echo "  cleanup         - 清理系统资源"
	@echo "  emergency-stop  - 紧急停止所有进程"
	@echo ""
	@echo "🎯 组合命令:"
	@echo "  quickstart      - 快速开始 (venv+install+check)"
	@echo "  ci              - CI/CD流程 (clean+lint+test)"
	@echo "  release         - 发布准备"
	@echo ""
	@echo "💡 使用提示:"
	@echo "  1. 首次使用: make venv && make install"
	@echo "  2. 快速开始: make quickstart"
	@echo "  3. 虚拟环境路径: $(VENV_DIR)"

# ================================
# 安装相关命令
# ================================
# 安装venv环境
venv:
	@echo "📦 安装venv到$(VENV_DIR)"



# 测试版本安装 (推荐，版本已验证)
install-tested:
	@echo "📦 安装测试验证版本..."
	$(call check_venv)
	$(PIP) install -r requirements-tested.txt
	@echo "✅ 安装完成！运行 'make check' 验证安装"

# 简化安装 (避免编译问题)
install-simple:
	@echo "📦 安装简化版本 (避免编译问题)..."
	$(call check_venv)
	$(PIP) install -r requirements-simple.txt
	@echo "✅ 简化安装完成！"

# 安装核心依赖
install-core:
	@echo "📦 安装核心依赖..."
	$(call check_venv)
	$(PIP) install -r requirements-core.txt

# 安装生产环境依赖
install-prod:
	@echo "📦 安装生产环境依赖..."
	$(call check_venv)
	$(PIP) install -r requirements-prod.txt

# 安装开发环境依赖
install-dev:
	@echo "📦 安装开发环境依赖..."
	$(call check_venv)
	$(PIP) install -r requirements-dev.txt

# 安装完整依赖
install-full:
	@echo "📦 安装完整依赖..."
	$(call check_venv)
	$(PIP) install -r requirements.txt

# 默认安装 (指向推荐版本)
install: install-tested

# ================================
# 检查和验证命令
# ================================

# 安装状态检查
check:
	@echo "🔍 检查安装状态..."
	$(call check_venv)
	@$(PYTHON) -c "import pymysql, sqlalchemy, requests, bs4, yaml, apscheduler, pandas; print('✅ 核心模块检查通过')" 2>/dev/null || echo "❌ 部分核心模块缺失，请运行: make install-tested"

# 系统状态检查
status:
	@echo "=== 系统状态检查 ==="
	@echo "虚拟环境状态:"
	@if [ -f "$(PYTHON)" ]; then \
		echo "✅ 虚拟环境存在: $(VENV_DIR)"; \
		echo "✅ Python可用: $(PYTHON)"; \
	else \
		echo "❌ 虚拟环境不存在或Python不可用"; \
		echo "   路径: $(PYTHON)"; \
	fi
	@echo ""
	@echo "Python版本:"
	@if [ -f "$(PYTHON)" ]; then $(PYTHON) --version; else echo "Python不可用"; fi
	@echo ""
	@echo "依赖包状态:"
	@if [ -f "$(PYTHON)" ]; then $(PIP) list | grep -E "(requests|pandas|sqlalchemy|apscheduler|beautifulsoup4)" || echo "未找到核心包"; else echo "虚拟环境不存在"; fi
	@echo ""
	@echo "数据库连接测试:"
	@if [ -f "$(PYTHON)" ]; then $(PYTHON) -c "from utils.mysql_handler import MySQLHandler; handler = MySQLHandler(); print('✅ 数据库连接正常')" 2>/dev/null || echo "❌ 数据库连接失败"; else echo "虚拟环境不存在"; fi

# 配置检查
config-check:
	@echo "🔧 检查配置文件..."
	$(call check_venv)
	@$(PYTHON) -c "from config.spider_config import SpiderConfigLoader; loader = SpiderConfigLoader(); configs = loader.load_config(); print(f'✅ 配置加载成功，共{len(configs)}个爬虫配置')" 2>/dev/null || echo "❌ 配置加载失败"

# ================================
# 开发相关命令
# ================================

# 运行测试
test:
	@echo "🧪 运行测试..."
	$(call check_venv)
	@if [ -d "tests" ]; then \
		$(PYTHON) -m pytest tests/ -v --cov=. --cov-report=html; \
	else \
		echo "⚠️  tests目录不存在，跳过测试"; \
	fi

# 代码检查
lint:
	@echo "🔍 代码检查..."
	$(call check_venv)
	@$(PYTHON) -c "import flake8" 2>/dev/null && \
		$(PYTHON) -m flake8 . --count --select=E9,F63,F7,F82 --show-source --statistics && \
		$(PYTHON) -m flake8 . --count --exit-zero --max-complexity=10 --max-line-length=127 --statistics || \
		echo "⚠️  flake8未安装，跳过代码检查"

# 代码格式化
format:
	@echo "🎨 代码格式化..."
	$(call check_venv)
	@$(PYTHON) -c "import black" 2>/dev/null && $(PYTHON) -m black . || echo "⚠️  black未安装，跳过格式化"
	@$(PYTHON) -c "import isort" 2>/dev/null && $(PYTHON) -m isort . || echo "⚠️  isort未安装，跳过导入排序"

# 清理临时文件
clean:
	@echo "🧹 清理临时文件..."
	find . -type f -name "*.pyc" -delete
	find . -type d -name "__pycache__" -delete
	find . -type d -name "*.egg-info" -exec rm -rf {} + 2>/dev/null || true
	find . -type f -name ".coverage" -delete
	find . -type d -name "htmlcov" -exec rm -rf {} + 2>/dev/null || true
	find . -type d -name ".pytest_cache" -exec rm -rf {} + 2>/dev/null || true
	@echo "✅ 清理完成"

# ================================
# 运行相关命令
# ================================

# 运行爬虫系统 (带监控和自动重启)
run:
	@echo "🚀 启动爬虫监控系统..."
	$(call check_venv)
	@if [ ! -f "spider.py" ]; then echo "❌ 错误: spider.py文件不存在"; exit 1; fi
	@echo "   项目目录: $(shell pwd)"
	@echo "   Python路径: $(PYTHON)"
	@echo "   脚本路径: $(shell pwd)/spider.py"
	@echo "   按 Ctrl+C 停止监控"
	@echo ""
	@while true; do \
		echo "⏰ $$(date '+%Y-%m-%d %H:%M:%S') - 启动爬虫进程..."; \
		$(PYTHON) spider.py; \
		EXIT_CODE=$$?; \
		echo "⚠️  $$(date '+%Y-%m-%d %H:%M:%S') - Python进程退出 (退出码: $$EXIT_CODE)"; \
		echo "   等待60秒后重启..."; \
		echo ""; \
		sleep 60; \
	done

# 运行爬虫系统 (单次执行，不重启)
run-once:
	@echo "🚀 启动爬虫系统 (单次执行)..."
	$(call check_venv)
	@if [ ! -f "spider.py" ]; then echo "❌ 错误: spider.py文件不存在"; exit 1; fi
	@echo "   项目目录: $(shell pwd)"
	@echo "   Python路径: $(PYTHON)"
	@echo "   脚本路径: $(shell pwd)/spider.py"
	$(PYTHON) spider.py

# 运行调度器
scheduler:
	@echo "⏰ 启动调度器..."
	$(call check_venv)
	@echo "   项目目录: $(shell pwd)"
	@echo "   Python路径: $(PYTHON)"
	$(PYTHON) -c "from spider import run_scheduler; run_scheduler()"

# ================================
# 数据库相关命令
# ================================

# 数据库初始化
db-init:
	@echo "🗄️  初始化数据库表..."
	$(call check_venv)
	@$(PYTHON) -c "from models.data_models import Base; from utils.mysql_handler import MySQLHandler; handler = MySQLHandler(); Base.metadata.create_all(handler.engine); print('✅ 数据库表已创建')" 2>/dev/null || echo "❌ 数据库初始化失败"

# ================================
# 其他工具命令
# ================================

# 生成文档
docs:
	@echo "📚 生成文档..."
	@if [ -d "docs" ] && [ -f "docs/Makefile" ]; then \
		cd docs && make html; \
	else \
		echo "⚠️  docs目录或Makefile不存在，跳过文档生成"; \
	fi

# 创建虚拟环境
venv:
	@echo "🐍 创建虚拟环境..."
	python -m venv venv
	@echo "✅ 虚拟环境已创建"
	@echo "💡 激活命令:"
	@echo "   Linux/Mac: source venv/bin/activate"
	@echo "   Windows:   venv\\Scripts\\activate"

# ================================
# 高级工具命令 (可选)
# ================================

# 安全检查
security:
	@echo "🔒 安全检查..."
	$(call check_venv)
	@$(PYTHON) -c "import bandit" 2>/dev/null && \
		$(PYTHON) -m bandit -r . -f json -o security-report.json && \
		echo "✅ 安全检查完成，报告: security-report.json" || \
		echo "⚠️  bandit未安装，跳过安全检查"

# 依赖检查
deps-check:
	@echo "📋 依赖安全检查..."
	$(call check_venv)
	@$(PYTHON) -c "import safety" 2>/dev/null && $(PYTHON) -m safety check || echo "⚠️  safety未安装，跳过依赖检查"

# 代码复杂度分析
complexity:
	@echo "📊 代码复杂度分析..."
	$(call check_venv)
	@$(PYTHON) -c "import radon" 2>/dev/null && $(PYTHON) -m radon cc . --min B || echo "⚠️  radon未安装，跳过复杂度分析"

# 性能测试
benchmark:
	@echo "⚡ 性能测试..."
	$(call check_venv)
	@$(PYTHON) -c "import memory_profiler" 2>/dev/null && $(PYTHON) -m memory_profiler spider.py || echo "⚠️  memory_profiler未安装，跳过性能测试"

# ================================
# 组合命令
# ================================

# 完整的CI/CD流程
ci: clean lint test
	@echo "🎯 CI/CD检查完成"

# 快速开始 (创建环境+安装+检查)
quickstart: venv install-tested check
	@echo "🚀 快速开始完成！"
	@echo "💡 下一步:"
	@echo "   1. 配置数据库: config/settings.py"
	@echo "   2. 运行爬虫: make run"
	@echo "   3. 运行调度器: make scheduler"

# ================================
# 稳定性工具命令
# ================================

# 系统健康检查
health:
	@echo "🏥 系统健康检查..."
	$(call check_venv)
	@$(PYTHON) utils/health_monitor.py --status

# 启动系统监控
monitor:
	@echo "📊 启动系统监控..."
	$(call check_venv)
	@echo "   监控将在后台运行，日志保存在 monitoring/logs/"
	@nohup $(PYTHON) utils/health_monitor.py --daemon > monitoring/monitor.log 2>&1 &
	@echo "✅ 系统监控已启动"

# 创建系统备份
backup:
	@echo "💾 创建系统备份..."
	$(call check_venv)
	@$(PYTHON) utils/backup_manager.py backup --type full

# 恢复系统备份
restore:
	@echo "🔄 恢复系统备份..."
	$(call check_venv)
	@echo "可用备份文件:"
	@$(PYTHON) utils/backup_manager.py list
	@echo ""
	@echo "使用方法: $(PYTHON) utils/backup_manager.py restore --file <backup_file>"

# 清理系统资源
cleanup:
	@echo "🧹 清理系统资源..."
	@echo "清理Python缓存文件..."
	@find . -name "*.pyc" -delete
	@find . -name "__pycache__" -type d -exec rm -rf {} + 2>/dev/null || true
	@echo "清理日志文件..."
	@find . -name "*.log" -mtime +7 -delete 2>/dev/null || true
	@echo "清理临时文件..."
	@rm -rf /tmp/spider_* 2>/dev/null || true
	$(call check_venv)
	@$(PYTHON) -c "import gc; gc.collect(); print('✅ 内存清理完成')"
	@echo "清理旧备份..."
	@$(PYTHON) utils/backup_manager.py cleanup
	@echo "✅ 系统清理完成"

# 紧急停止所有进程
emergency-stop:
	@echo "🚨 紧急停止所有爬虫进程..."
	@pkill -f "python.*spider.py" || echo "没有找到爬虫进程"
	@pkill -f "health_monitor.py" || echo "没有找到监控进程"
	@echo "✅ 所有进程已停止"

# 完整的系统检查
full-check: health check config-check
	@echo "🎯 完整系统检查完成"
