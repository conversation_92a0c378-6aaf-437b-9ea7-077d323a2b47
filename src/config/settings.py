"""
Marzaha 爬虫系统配置模块

从 settings.ini 文件读取配置信息
"""

import configparser
from pathlib import Path


def load_config():
    """加载配置文件"""
    config = configparser.ConfigParser()

    # 配置文件路径
    config_file = Path(__file__).parent / 'settings.ini'

    if not config_file.exists():
        raise FileNotFoundError(f"配置文件不存在: {config_file}")

    config.read(config_file, encoding='utf-8')
    return config


def get_bool(config, section, key, fallback=False):
    """获取布尔值配置"""
    try:
        return config.getboolean(section, key)
    except (configparser.NoSectionError, configparser.NoOptionError, ValueError):
        return fallback


def get_int(config, section, key, fallback=0):
    """获取整数配置"""
    try:
        return config.getint(section, key)
    except (configparser.NoSectionError, configparser.NoOptionError, ValueError):
        return fallback


def get_str(config, section, key, fallback=''):
    """获取字符串配置"""
    try:
        return config.get(section, key)
    except (configparser.NoSectionError, configparser.NoOptionError):
        return fallback


# 加载配置
try:
    config = load_config()

    # Redis 配置
    BROKER_URL = get_str(config, 'redis', 'broker_url', 'redis://localhost:6379/0')
    CELERY_RESULT_BACKEND = get_str(config, 'redis', 'result_backend', 'redis://localhost:6379/1')

    # MySQL 配置
    MYSQL_CONFIG = {
        'host': get_str(config, 'mysql', 'host', 'localhost'),
        'user': get_str(config, 'mysql', 'user', 'root'),
        'password': get_str(config, 'mysql', 'password', '123456'),
        'database': get_str(config, 'mysql', 'database', 'crawler_leo'),
        'port': get_int(config, 'mysql', 'port', 3306)
    }

    # Logger 配置
    LOGGER_CONFIG = {
        # 控制台输出级别
        'CONSOLE_LEVEL': get_str(config, 'logger', 'console_level', 'INFO'),

        # 默认日志文件路径
        'DEFAULT_LOG_FILE': get_str(config, 'logger', 'default_log_file', '/mnt/data1/leo/spider_colored.log'),

        # 日志格式配置
        'LOG_FORMAT': get_str(config, 'logger', 'log_format', '%(asctime)s - %(name)s - %(levelname)s - %(message)s'),
        'DATE_FORMAT': get_str(config, 'logger', 'date_format', '%Y-%m-%d %H:%M:%S'),

        # 文件模式配置
        'DEFAULT_LOG_ADD': get_bool(config, 'logger', 'default_log_add', False),

        # 自定义日志级别映射
        'CUSTOM_LEVELS': {
            'INFO': get_int(config, 'logger', 'level_info', 10),
            'HINT': get_int(config, 'logger', 'level_hint', 20),
            'WARN': get_int(config, 'logger', 'level_warn', 30),
            'ERROR': get_int(config, 'logger', 'level_error', 40),
            'SUCCESS': get_int(config, 'logger', 'level_success', 25),
        },

        # 级别说明
        'LEVEL_DESCRIPTION': {
            'INFO': 'INFO - 一般信息（白色）',
            'HINT': 'HINT - 正常输出（蓝色）',
            'WARN': 'WARN - 解析错误（黄色）',
            'ERROR': 'ERROR - IO错误（红色）',
            'SUCCESS': 'SUCCESS - 成功信息（绿色，总是显示）'
        },

        # 文件日志说明
        'FILE_LOGGING_NOTE': '所有级别的日志都会写入文件，控制台根据 CONSOLE_LEVEL 过滤显示'
    }

    # 系统配置
    SYSTEM_CONFIG = {
        'DEBUG': get_bool(config, 'system', 'debug', False),
        'MAX_WORKERS': get_int(config, 'system', 'max_workers', 10),
        'TIMEOUT': get_int(config, 'system', 'timeout', 30),
        'RETRY_TIMES': get_int(config, 'system', 'retry_times', 3)
    }

    # 爬虫配置
    SPIDER_CONFIG = {
        'USER_AGENT': get_str(config, 'spider', 'user_agent', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'),
        'REQUEST_DELAY': get_int(config, 'spider', 'request_delay', 1),
        'MAX_RETRIES': get_int(config, 'spider', 'max_retries', 3),
        'TIMEOUT': get_int(config, 'spider', 'timeout', 30)
    }

    # 路径配置
    PATHS_CONFIG = {
        'DATA_DIR': get_str(config, 'paths', 'data_dir', '/mnt/data1/leo'),
        'LOG_DIR': get_str(config, 'paths', 'log_dir', '/mnt/data1/leo/logs'),
        'BACKUP_DIR': get_str(config, 'paths', 'backup_dir', '/mnt/data1/leo/backups'),
        'TEMP_DIR': get_str(config, 'paths', 'temp_dir', '/tmp/spider_temp')
    }

except FileNotFoundError as e:
    print(f"警告: {e}")
    print("使用默认配置...")

    # 默认配置
    BROKER_URL = 'redis://localhost:6379/0'
    CELERY_RESULT_BACKEND = 'redis://localhost:6379/1'

    MYSQL_CONFIG = {
        'host': 'localhost',
        'user': 'root',
        'password': '123456',
        'database': 'crawler_leo',
        'port': 3306
    }

    LOGGER_CONFIG = {
        'CONSOLE_LEVEL': 'INFO',
        'DEFAULT_LOG_FILE': '/mnt/data1/leo/spider_colored.log',
        'LOG_FORMAT': '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        'DATE_FORMAT': '%Y-%m-%d %H:%M:%S',
        'DEFAULT_LOG_ADD': False,
        'CUSTOM_LEVELS': {
            'INFO': 10,
            'HINT': 20,
            'WARN': 30,
            'ERROR': 40,
            'SUCCESS': 25,
        },
        'LEVEL_DESCRIPTION': {
            'INFO': 'INFO - 一般信息（白色）',
            'HINT': 'HINT - 正常输出（蓝色）',
            'WARN': 'WARN - 解析错误（黄色）',
            'ERROR': 'ERROR - IO错误（红色）',
            'SUCCESS': 'SUCCESS - 成功信息（绿色，总是显示）'
        },
        'FILE_LOGGING_NOTE': '所有级别的日志都会写入文件，控制台根据 CONSOLE_LEVEL 过滤显示'
    }

    SYSTEM_CONFIG = {
        'DEBUG': False,
        'MAX_WORKERS': 10,
        'TIMEOUT': 30,
        'RETRY_TIMES': 3
    }

    SPIDER_CONFIG = {
        'USER_AGENT': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        'REQUEST_DELAY': 1,
        'MAX_RETRIES': 3,
        'TIMEOUT': 30
    }

    PATHS_CONFIG = {
        'DATA_DIR': '/mnt/data1/leo',
        'LOG_DIR': '/mnt/data1/leo/logs',
        'BACKUP_DIR': '/mnt/data1/leo/backups',
        'TEMP_DIR': '/tmp/spider_temp'
    }
