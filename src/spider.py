"""
Marzaha 爬虫系统核心模块 - spider.py

本模块是 Marzaha 爬虫系统的核心文件，负责管理和协调所有爬虫任务的执行。
支持 16,000+ 爬虫配置的并发处理，具备完整的容错机制和性能监控。

主要功能：
1. 爬虫工厂模式管理 600+ 种不同类型的爬虫
2. 高并发任务处理（800个工作线程）
3. 非阻塞爬虫执行和超时控制
4. 定时任务调度和自动化执行
5. 数据库操作和报告生成
6. 完整的错误处理和日志记录

技术特点：
- 并发度：800个工作线程用于爬虫执行，5个线程用于数据处理
- 容错性：多层次异常处理，单点故障不影响整体流程
- 监控性：实时进度显示、成功率统计、执行时间监控
- 扩展性：工厂模式支持快速添加新爬虫类型

性能指标：
- 支持爬虫数量：16,000+
- 并发线程数：800
- 单爬虫超时：1500秒
- 调度间隔：30分钟
- 最大调度实例：3个

作者：Marzaha Team
版本：2.0
更新时间：2025
"""

import logging
import threading
from concurrent.futures import ThreadPoolExecutor, wait, FIRST_COMPLETED
from datetime import datetime
from typing import List, Tuple, Any, Dict

from apscheduler.schedulers.blocking import BlockingScheduler
from config.logging_config import setup_logging
from config.spider_config import SpiderConfigLoader
from utils.colored_logger import (
    log_success, log_error, log_warn, log_info, log_hint,
    log_spider_result, log_database_operation, log_file_operation
)

from spiders.csr_spider.factory.applicant_pool_spider import ApplicantPoolSpider
from spiders.csr_spider.factory.bamboohr_spider import BamboohrSpider
from spiders.csr_spider.factory.brassring_spider import BrassringSpider
from spiders.csr_spider.factory.eightfold_spider import EightfoldSpider
from spiders.csr_spider.factory.hiringroom_spider import HiringroomSpider
from spiders.csr_spider.factory.job_search_results_job_spider import JobSearchResultsJobSpider
from spiders.csr_spider.factory.job_search_results_search_spider import JobSearchResultsSearchSpider
from spiders.csr_spider.factory.jobs_dayforcehcm_spider import JobsDayforcehcmSpider
from spiders.csr_spider.factory.jobs_spider import JobsSpider
from spiders.csr_spider.factory.myjobsadp_spider import MyjobsAdpSpider
from spiders.csr_spider.factory.oraclecloud_spider import OracleCloudSpider
from spiders.csr_spider.factory.ultipro_spider import UltiproSpider
from spiders.csr_spider.factory.workable_spider import WorkableSpider
from spiders.csr_spider.factory.workday_id0_spider import WorkDayId0Spider
from spiders.csr_spider.factory.workday_spider import WorkDaySpider
from spiders.csr_spider.factory.workday_two_spider import WorkDayTwoSpider
from spiders.csr_spider.factory.workforcenow_spider import WorkforcenowSpider
from spiders.csr_spider.single.aep_spider import AepSpider
from spiders.csr_spider.single.amazon_spider import AmazonSpider
from spiders.csr_spider.single.american_campus_spider import AmericanCampusSpider
from spiders.csr_spider.single.bank_of_america_spider import BankOfAmericaSpider
from spiders.csr_spider.single.bha_spider import BhaSpider
from spiders.csr_spider.single.fritolay_spider import FritolaySpider
from spiders.csr_spider.single.google_spider import GoogleSpider
from spiders.csr_spider.single.home_depot_spider import HomeDepotSpider
from spiders.csr_spider.single.ibm_spider import IbmSpider
from spiders.csr_spider.single.lockheed_spider import LockheedSpider
from spiders.csr_spider.single.meta_spider import MetaSpider
from spiders.csr_spider.single.microsoft_spider import MicrosoftSpider
from spiders.csr_spider.single.mondelez_international_spider import MondelezInternationalSpider
from spiders.csr_spider.single.oneapp_spider import OneappSpider
from spiders.csr_spider.single.pfizer_spider import PfizerSpider
from spiders.csr_spider.single.southern_spider import SouthernSpider
from spiders.csr_spider.single.tesla_spider import TeslaSpider
from spiders.csr_spider.single.visa_spider import VisaSpider
from spiders.csr_spider.single.whole_foods_spider import WholeFoodsSpider
from spiders.only_num_spider.aam_spider import AamSpider
from spiders.only_num_spider.aarons_spider import AaronsSpider
from spiders.only_num_spider.aerosonic_spider import AerosonicSpider
from spiders.only_num_spider.afi_spider import AfiSpider
from spiders.only_num_spider.agcocorp_spider import AgcocorpSpider
from spiders.only_num_spider.airbnb_spider import AirbnbSpider
from spiders.only_num_spider.align_spider import AlignSpider
from spiders.only_num_spider.allstate_spider import AllstateSpider
from spiders.only_num_spider.alticeusacareers_spider import AlticeusacareersSpider
from spiders.only_num_spider.amcor_spider import AmcorSpider
from spiders.only_num_spider.amphenol_spider import AmphenolSpider
from spiders.only_num_spider.amphenolrf_spider import AmphenolrfSpider
from spiders.only_num_spider.aosmd_spider import AosmdSpider
from spiders.only_num_spider.apachecorp_spider import ApachecorpSpider
from spiders.only_num_spider.applicantpro_spider import ApplicantproSpider
from spiders.only_num_spider.appone_spider import ApponeSpider
from spiders.only_num_spider.appone_two_spider import ApponeTwoSpider
from spiders.only_num_spider.aramarkcareers_spider import AramarkcareersgroupSpider
from spiders.only_num_spider.aurora_spider import AuroraSpider
from spiders.only_num_spider.avanos_spider import AvanosSpider
from spiders.only_num_spider.avature_spider import AvatureSpider
from spiders.only_num_spider.badcock_spider import BadcockSpider
from spiders.only_num_spider.bahamabreeze_spider import BahamabreezeSpider
from spiders.only_num_spider.ball_spider import BallSpider
from spiders.only_num_spider.bathandbodyworks_spider import BathandbodyworksSpider
from spiders.only_num_spider.baylis_spider import BaylisSpider
from spiders.only_num_spider.bbsistaffingsa_spider import BbsistaffingsaSpider
from spiders.only_num_spider.bentley_spider import BentleySpider
from spiders.only_num_spider.bequad_spider import BequadSpider
from spiders.only_num_spider.bigbear_spider import BigbearSpider
from spiders.only_num_spider.bill_spider import BillSpider
from spiders.only_num_spider.biorad_spider import BioradSpider
from spiders.only_num_spider.bjsrestaurants_spider import BjsrestaurantsSpider
from spiders.only_num_spider.blueprintgenetics_spider import BlueprintgeneticsSpider
from spiders.only_num_spider.boots_spider import BootsSpider
from spiders.only_num_spider.bunge_spider import BungeSpider
from spiders.only_num_spider.careered_spider import CareeredSpider
from spiders.only_num_spider.careers_callnorthwest_spider import CareersCallnorthwestSpider
from spiders.only_num_spider.careers_clarkpest_spider import CareersClarkpestSpider
from spiders.only_num_spider.careers_cranepestcontrol_spider import CareersCranepestcontrolSpider
from spiders.only_num_spider.careers_icims_spider import CareersIcimsSpider
from spiders.only_num_spider.careers_orkin_spider import CareersOrkinSpider
from spiders.only_num_spider.careers_ui_spider import CareersUiSpider
from spiders.only_num_spider.careershealthcare_spider import CareershealthcareSpider
from spiders.only_num_spider.carlyle_avature_spider import CarlyleAvatureSpider
from spiders.only_num_spider.carnival_spider import CarnivalSpider
from spiders.only_num_spider.carvana_spider import CarvanaSpider
from spiders.only_num_spider.centerlinedrivers_spider import CenterlinedriversSpider
from spiders.only_num_spider.checkpoint_spider import CheckpointSpider
from spiders.only_num_spider.cheddars_spider import CheddarsSpider
from spiders.only_num_spider.chicos_spider import ChicosSpider
from spiders.only_num_spider.clcgroup_spider import ClcgroupSpider
from spiders.only_num_spider.cmc_spider import CmcSpider
from spiders.only_num_spider.cmco_spider import CmcoSpider
from spiders.only_num_spider.columbia_sussex_spider import ColumbiaSussexSpider
from spiders.only_num_spider.consolidated_spider import ConsolidatedSpider
from spiders.only_num_spider.cooperstandard_spider import CooperStandardSpider
from spiders.only_num_spider.corecivic_spider import CorecivicSpider
from spiders.only_num_spider.costacareers_spider import CostaCareersSpider
from spiders.only_num_spider.couchbase_spider import CouchbaseSpider
from spiders.only_num_spider.cubesmart_spider import CubesmartSpider
from spiders.only_num_spider.cummins_spider import CumminsSpider
from spiders.only_num_spider.datadoghq_spider import DatadoghqSpider
from spiders.only_num_spider.deciem_prod_atlanta_spider import DeciemProdAtlantaSpider
from spiders.only_num_spider.deluxe_spider import DeluxeSpider
from spiders.only_num_spider.depopcareers_spider import DepopcareersSpider
from spiders.only_num_spider.diversityjobs_spider import DiversityjobsSpider
from spiders.only_num_spider.dmoleculartherapeutics_spider  import DmoleculartherapeuticsSpider
from spiders.only_num_spider.docgo_spider import DocgoSpider
from spiders.only_num_spider.dswinc_spider import DswincSpider
from spiders.only_num_spider.dvelectronics_spider import DvelectronicsSpider
from spiders.only_num_spider.dxl_spider import DxlSpider
from spiders.only_num_spider.ea_spider import EaSpider
from spiders.only_num_spider.eastman_spider import EastmanSpider
from spiders.only_num_spider.emergentbiosolutions_spider import EmergentbiosolutionsSpider
from spiders.only_num_spider.eqt_spider import EqtSpider
from spiders.only_num_spider.etsy_spider import EtsySpider
from spiders.only_num_spider.expandenergy_spider import ExpandenergySpider
from spiders.only_num_spider.expediagroup_spider import ExpediagroupSpider
from spiders.only_num_spider.farm_foods_spider import FarmFoodsSpider
from spiders.only_num_spider.ffbusa_spider import FfbusaSpider
from spiders.only_num_spider.firstbusinessbank_spider import FirstbusinessbankSpider
from spiders.only_num_spider.flooranddecor_spider import FlooranddecorSpider
from spiders.only_num_spider.fmc_spider import FmcSpider
from spiders.only_num_spider.franklincovey_spider import FranklincoveySpider
from spiders.only_num_spider.geniussports_spider import GeniussportsSpider
from spiders.only_num_spider.globalindustrial_spider import GlobalindustrialSpider
from spiders.only_num_spider.godaddy_spider import GodaddySpider
from spiders.only_num_spider.gohealth_spider import GohealthSpider
from spiders.only_num_spider.government_jobs_spider import GovernmentJobsSpider
from spiders.only_num_spider.groupe_psa_spider import GroupePsaSpider
from spiders.only_num_spider.guarantybank_spider import GuarantybankSpider
from spiders.only_num_spider.gupy_spider import GupySpider
from spiders.only_num_spider.hansgrohe_spider import HansgroheSpider
from spiders.only_num_spider.havertys_spider import HavertysSpider
from spiders.only_num_spider.haynes_spider import HaynesSpider
from spiders.only_num_spider.hcshiring_spider import HcshiringSpider
from spiders.only_num_spider.heritagehillsenior_spider import HeritagehillseniorSpider
from spiders.only_num_spider.hfsinclair_spider import HfsinclairSpider
from spiders.only_num_spider.hiitsd_spider import HiitsdSpider
from spiders.only_num_spider.hologic_spider import HologicSpider
from spiders.only_num_spider.horizonbank_spider import HorizonbankSpider
from spiders.only_num_spider.hrsmart_spider import HrsmartSpider
from spiders.only_num_spider.hubbell_spider import HubbellSpider
from spiders.only_num_spider.hubspot_spider import HubspotSpider
from spiders.only_num_spider.huntingtoningalls_spider import HuntingtoningallsSpider
from spiders.only_num_spider.huya_spider import HuyaSpider
from spiders.only_num_spider.icims_two_spider import IcimsTwoSpider
from spiders.only_num_spider.iconplc_spider import IconplcSpider
from spiders.only_num_spider.ifdsgroup_spider import IfdsgroupSpider
from spiders.only_num_spider.inflightcloud_spider import InflightcloudSpider
from spiders.only_num_spider.internationalpaper_spider import InternationalpaperSpider
from spiders.only_num_spider.invitation_homes_spider import InvitationHomesSpider
from spiders.only_num_spider.ionq_spider import IonqSpider
from spiders.only_num_spider.jeidwen_spider import JeidwenSpider
from spiders.only_num_spider.job_search_results_search_only_num_spider import JobSearchResultsSearchOnlyNumSpider
from spiders.only_num_spider.jobs_aa_spider import JobsAaSpider
from spiders.only_num_spider.jobs_dayforcehcm_only_num_spider import JobsDayforcehcmOnlyNumSpider
from spiders.only_num_spider.jobs_only_num_spider import JobsOnlyNumSpider
from spiders.only_num_spider.jobs_two_only_num_spider import JobsTwoOnlyNumSpider
from spiders.only_num_spider.johnson_spider import JohnsonSpider
from spiders.only_num_spider.karriere_spider import KarriereSpider
from spiders.only_num_spider.kellyocg_spider import KellyocgSpider
from spiders.only_num_spider.kff_spider import KffSpider
from spiders.only_num_spider.kkr_spider import KkrSpider
from spiders.only_num_spider.kornit_spider import KornitSpider
from spiders.only_num_spider.kroll_spider import KrollSpider
from spiders.only_num_spider.lawson_spider import LawsonSpider
from spiders.only_num_spider.lennar_spider import LennarSpider
from spiders.only_num_spider.limb1_spider import Limb1Spider
from spiders.only_num_spider.linkedin_spider import LinkedinSpider
from spiders.only_num_spider.lkqeurope_spider import LkqeuropeSpider
from spiders.only_num_spider.longhornsteakhouse_spider import LonghornsteakhouseSpider
from spiders.only_num_spider.lululemon_spider import LululemonSpider
from spiders.only_num_spider.marcuscorp_spider import MarcuscorpSpider
from spiders.only_num_spider.marketaxess_spider import MarketaxessSpider
from spiders.only_num_spider.materialise_spider import MaterialiseSpider
from spiders.only_num_spider.maxeon_spider import MaxeonSpider
from spiders.only_num_spider.mccann_spider import MccannSpider
from spiders.only_num_spider.mercbank_spider import MercbankSpider
from spiders.only_num_spider.met_life_spider import MetLifeSpider
from spiders.only_num_spider.milwaukeejobs_spider import MilwaukeejobsSpider
from spiders.only_num_spider.monroe_energy_spider import MonroeEnergySpider
from spiders.only_num_spider.mytheresa_spider import MytheresaSpider
from spiders.only_num_spider.myworkdayjobs_spider import MyworkdayjobsSpider
from spiders.only_num_spider.novocure_spider import NovocureSpider
from spiders.only_num_spider.oatly_spider import OatlySpider
from spiders.only_num_spider.oldsecond_spider import OldsecondSpider
from spiders.only_num_spider.olivegarden_spider import OlivegardenSpider
from spiders.only_num_spider.oracle_cloud_only_num_spider import OracleCloudOnlyNumSpider
from spiders.only_num_spider.orioncarbons_spider import OrioncarbonsSpider
from spiders.only_num_spider.paramount_spider import ParamountSpider
from spiders.only_num_spider.parkelectro_spider import ParkelectroSpider
from spiders.only_num_spider.partech_spider import PartechSpider
from spiders.only_num_spider.paycomonline_spider import PaycomonlineSpider
from spiders.only_num_spider.paysafe_spider import PaysafeSpider
from spiders.only_num_spider.peopeadmin_spider import PeopeadminSpider
from spiders.only_num_spider.people_click_spider import PeopleClickSpider
from spiders.only_num_spider.phf_tbe_taleo_spider import PhfTbeTaleoSpider
from spiders.only_num_spider.pinterestcareers_spider import PinterestcareersSpider
from spiders.only_num_spider.pizzahut_spider import PizzahutSpider
from spiders.only_num_spider.qorvo_spider import QorvoSpider
from spiders.only_num_spider.quality_carriers_spider import QualityCarriersSpider
from spiders.only_num_spider.radware_spider import RadwareSpider
from spiders.only_num_spider.referrals_spider import ReferralsSpider
from spiders.only_num_spider.retaildata_spider import RetaildataSpider
from spiders.only_num_spider.revgroup_spider import RevgroupSpider
from spiders.only_num_spider.rgarecareers_spider import RgarecareersSpider
from spiders.only_num_spider.rgp_spider import RgpSpider
from spiders.only_num_spider.rossstores_spider import RossstoresSpider
from spiders.only_num_spider.royalcaribbeangroup_spider import RoyalcaribbeangroupSpider
from spiders.only_num_spider.sabacloud_spider import SabacloudSpider
from spiders.only_num_spider.sapiens_spider import SapiensSpider
from spiders.only_num_spider.sea_spider import SeaSpider
from spiders.only_num_spider.seagatecareers_spider import SeagatecareersSpider
from spiders.only_num_spider.search_jobs_only_num_spider import SearchJobsOnlyNumSpider
from spiders.only_num_spider.search_only_num_spider import SearchOnlyNumSpider
from spiders.only_num_spider.search_results_only_num_spider import SearchResultsOnlyNumSpider
from spiders.only_num_spider.seasons52_spider import Seasons52Spider
from spiders.only_num_spider.selectminds_spider import SelectmindsSpider
from spiders.only_num_spider.selectquote_spider import SelectQuoteSpider
from spiders.only_num_spider.senecafoods_spider import SenecafoodsSpider
from spiders.only_num_spider.shoecarnival_spider import ShoecarnivalSpider
from spiders.only_num_spider.sk_spider import SkSpider
from spiders.only_num_spider.skillsoft_one_spider import SkillsoftOneSpider
from spiders.only_num_spider.skyworksinc_spider import SkyworksincSpider
from spiders.only_num_spider.smartrecruiters_spider import SmartrecruitersSpider
from spiders.only_num_spider.smartrecruiters_total_spider import SmartrecruitersTotalSpider
from spiders.only_num_spider.socialpoint_spider import SocialpointSpider
from spiders.only_num_spider.solaredge_spider import SolaredgeSpider
from spiders.only_num_spider.southern_peru_spider import SouthernPeruSpider
from spiders.only_num_spider.spotifyjobs_spider import SpotifyjobsSpider
from spiders.only_num_spider.stahlgruber_spider import StahlgruberSpider
from spiders.only_num_spider.steelcase_spider import SteelcaseSpider
from spiders.only_num_spider.stratasys_spider import StratasysSpider
from spiders.only_num_spider.sweetgreen_spider import SweetgreenSpider
from spiders.only_num_spider.symphonytalent_spider import SymphonytalentSpider
from spiders.only_num_spider.take2games_spider import Take2gamesSpider
from spiders.only_num_spider.taleo_spider import TaleoSpider
from spiders.only_num_spider.tapestry_spider import TapestrySpider
from spiders.only_num_spider.tbe_taleo_net_spider import TbeTaleoNetSpider
from spiders.only_num_spider.tdcx_spider import TdcxSpider
from spiders.only_num_spider.teamtailor_spider import TeamtailorSpider
from spiders.only_num_spider.teleflex_spider import TeleflexSpider
from spiders.only_num_spider.tennantco_spider import TennantcoSpider
from spiders.only_num_spider.teradyne_spider import TeradyneSpider
from spiders.only_num_spider.the_applicant_manager_spider import TheApplicantManagerSpider
from spiders.only_num_spider.thecapitalgrille_spider import ThecapitalgrilleSpider
from spiders.only_num_spider.training_spider import TrainingSpider
from spiders.only_num_spider.triumphgroup_spider import TriumphgroupSpider
from spiders.only_num_spider.tuniu_spider import TuniuSpider
from spiders.only_num_spider.tuniu_xiaozhao import TuniuXiaozhaoSpider
from spiders.only_num_spider.tuniu_xiaozhao_1_spider import TuniuXiaozhaooneSpider
from spiders.only_num_spider.uber_spider import UberSpider
from spiders.only_num_spider.uhaul_spider import UhaulSpider
from spiders.only_num_spider.uhsinc_spider import UhsincSpider
from spiders.only_num_spider.ultipro_only_num_spider import UltiproOnlyNumSpider
from spiders.only_num_spider.ultipro_spider import UltiproSpider
from spiders.only_num_spider.unilab_spider import UnilabSpider
from spiders.only_num_spider.us_dayforcehcm_only_num_spider import UsDayforcehcmOnlyNumSpider
from spiders.only_num_spider.uscellular_spider import UscellularSpider
from spiders.only_num_spider.uzf4_spider import Uzf4Spider
from spiders.only_num_spider.vacasa_spider import VacasaSpider
from spiders.only_num_spider.vailresortscareers_spider import VailresortscareersSpider
from spiders.only_num_spider.valero_spider import ValeroSpider
from spiders.only_num_spider.veeco_spider import VeecoSpider
from spiders.only_num_spider.vempranexa_spider import VempranexaSpider
from spiders.only_num_spider.venparanexa_spider import VenparanexaSpider
from spiders.only_num_spider.vizio_spider import VizioSpider
from spiders.only_num_spider.vizirecruiter_spider import VizirecruiterSpider
from spiders.only_num_spider.vuzix_spider import VuzixSpider
from spiders.only_num_spider.werkenbijlkq_spider import WerkenbijlkqSpider
from spiders.only_num_spider.westlaketalent_spider import WestlaketalentSpider
from spiders.only_num_spider.westrockta_avature_spider import WestrocktaAvatureSpider
from spiders.only_num_spider.williamhillgroup_spider import WilliamhillgroupSpider
from spiders.only_num_spider.wolt_spider import WoltSpider
from spiders.only_num_spider.workday_only_num_spider import WorkdayOnlyNumSpider
from spiders.only_num_spider.workforcenow_two_spider import WorkforcenowTwoSpider
from spiders.only_num_spider.wowway_spider import WowwaySpider
from spiders.only_num_spider.wynnresorts_spider import WynnresortsSpider
from spiders.only_num_spider.yardhouse_spider import YardhouseSpider
from spiders.only_num_spider.youradv_spider import YouradvSpider
from spiders.only_num_spider.yum_china_spider import YumChinaSpider
from spiders.only_num_spider.yum_spider import YumSpider
from spiders.only_num_spider.zimvie_spider import ZimvieSpider
from spiders.only_num_spider.zuora_spider import ZuoraSpider
from spiders.only_num_spider.zynga_spider import ZyngaSpider
from spiders.ssr_spider.factory.apply_to_job_spider import ApplyToJobSpider
from spiders.ssr_spider.factory.greenhouse_other_spider import GreenHouseOtherSpider
from spiders.ssr_spider.factory.greenhouse_spider import GreenHouseSpider
from spiders.ssr_spider.factory.icims_spider import IcimsSpider
from spiders.ssr_spider.factory.jobs_two_spider import JobsTwoSpider
from spiders.ssr_spider.factory.jobslever_spider import JobsLeverSpider
from spiders.ssr_spider.factory.jobvite_spider import JobViteSpider
from spiders.ssr_spider.factory.search_jobs_spider import SearchJobsSpider
from spiders.ssr_spider.factory.search_jobs_two_spider import SearchJobsTwoSpider
from spiders.ssr_spider.factory.search_results_spider import SearchResultsSpider
from spiders.ssr_spider.factory.search_results_ten_spider import SearchResultsTenSpider
from spiders.ssr_spider.factory.search_results_two_spider import SearchResultsTwoSpider
from spiders.ssr_spider.factory.search_spider import SearchSpider
from spiders.ssr_spider.factory.us242_dayforcehcm_spider import Us242DayforcehcmSpider
from spiders.ssr_spider.single.abbvie_spider import AbbvieSpider
from spiders.ssr_spider.single.adp_spider import AdpSpider
from spiders.ssr_spider.single.agoda_spider import AgodaSpider
from spiders.ssr_spider.single.assured_spider import AssuredSpider
from spiders.ssr_spider.single.axogeninc_spider import AxogenincSpider
from spiders.ssr_spider.single.cisco_spider import CiscoSpider
from spiders.ssr_spider.single.eog_spider import EogSpider
from spiders.ssr_spider.single.gossamerbio_spider import GossamerbioSpider
from spiders.ssr_spider.single.hrmos_spider import HrmosSpider
from spiders.ssr_spider.single.jnj_spider import JnjSpider
from spiders.ssr_spider.single.maravai_spider import MaravaiSpider
from spiders.ssr_spider.single.nord_cloud_spider import NordCloudSpider
from spiders.ssr_spider.single.olaplex_spider import OlaplexSpider
from spiders.ssr_spider.single.pierce_engineers_spider import PierceEngineersSpider
from spiders.ssr_spider.single.skillsoft_spider import SkillsoftSpider
from spiders.ssr_spider.single.walmart_spider import WalmartSpider
# import utils
from utils.db_operations import DBOperations


def create_progress_bar(percentage: float, width: int = 30) -> str:
    """
    创建进度条字符串

    Args:
        percentage: 完成百分比 (0-100)
        width: 进度条宽度（字符数）

    Returns:
        str: 格式化的进度条字符串
    """
    filled_width = int(width * percentage / 100)
    bar = '█' * filled_width + '░' * (width - filled_width)
    return f"[{bar}]"


class SpiderFactory:
    """
    爬虫工厂类 - 负责创建和管理所有类型的爬虫实例

    这是整个系统的核心工厂模式实现，支持 600+ 种不同类型的爬虫。
    工厂模式的优势：
    1. 统一的爬虫创建接口
    2. 类型安全的爬虫实例化
    3. 易于扩展新的爬虫类型
    4. 集中的配置管理

    支持的爬虫类型分类：
    - CSR Spider Factory: workday, oracle_cloud, greenhouse, ultipro 等
    - CSR Spider Single: amazon, tesla, google, microsoft 等
    - Only Num Spider: airbnb, uber, linkedin 等
    - SSR Spider: walmart, cisco, jnj 等

    使用示例：
        factory = SpiderFactory()
        spider_instance, spider_name = factory.create_spider(
            'workday',
            company_name='Microsoft',
            base_url='https://microsoft.wd1.myworkdayjobs.com',
            company_url='https://microsoft.com',
            cwiq_code='MSFT001'
        )
    """

    @staticmethod
    def create_spider(spider_type: str, **kwargs) -> Tuple[Any, str]:
        """
        工厂方法，根据爬虫类型和参数创建对应的爬虫实例

        这是整个系统的核心工厂模式实现，支持 600+ 种不同类型的爬虫。

        Args:
            spider_type (str): 爬虫类型标识符，如 'workday', 'greenhouse', 'amazon' 等
                支持的类型包括：
                - 'workday': Workday 招聘系统爬虫
                - 'greenhouse': Greenhouse 招聘平台爬虫
                - 'amazon': Amazon 职位爬虫
                - 'tesla': Tesla 职位爬虫
                - 'google': Google 职位爬虫
                - 'microsoft': Microsoft 职位爬虫
                - 更多类型请参考 spider_configs 字典

            **kwargs: 创建爬虫所需的动态参数，必须包含：
                - company_name (str): 公司名称，用于标识和日志
                - base_url (str): 爬虫目标URL，爬虫的入口地址
                - company_url (str): 公司官网URL，用于数据关联
                - cwiq_code (str): 公司唯一标识码，用于数据库存储

                可选参数：
                - id (int): 配置ID，用于任务标识
                - name (str): 显示名称，用于日志输出

        Returns:
            Tuple[Any, str]: 返回爬虫实例和爬虫名称的元组
                - spider_instance: 创建的爬虫实例，具有 run() 方法
                - spider_name: 爬虫的显示名称，用于日志和监控

        Raises:
            ValueError: 当 spider_type 不存在或必需参数缺失时抛出
                - "Unknown spider type: {spider_type}": 不支持的爬虫类型
                - "spider requires company_name, base_url, company_url, cwiq_code": 缺少必需参数

        Examples:
            创建 Workday 爬虫：
            >>> factory = SpiderFactory()
            >>> spider, name = factory.create_spider(
            ...     'workday',
            ...     company_name='Microsoft',
            ...     base_url='https://microsoft.wd1.myworkdayjobs.com',
            ...     company_url='https://microsoft.com',
            ...     cwiq_code='MSFT001'
            ... )
            >>> result = spider.run()

            创建 Greenhouse 爬虫：
            >>> spider, name = factory.create_spider(
            ...     'greenhouse',
            ...     company_name='Google',
            ...     base_url='https://boards.greenhouse.io/google',
            ...     company_url='https://google.com',
            ...     cwiq_code='GOOG001'
            ... )

        技术实现：
            1. 验证爬虫类型是否在支持列表中
            2. 检查必需参数是否完整
            3. 根据类型调用对应的爬虫类或工厂方法
            4. 返回实例化的爬虫对象和名称

        性能考虑：
            - 工厂方法是静态的，无需实例化工厂类
            - 爬虫实例按需创建，避免内存浪费
            - 类型检查在创建时进行，确保类型安全
        """
        spider_configs = {
            'amazon': (AmazonSpider, "Amazon"),
            'tesla': (TeslaSpider, "Tesla"),
            'google': (GoogleSpider.create_spider, "Google"),
            'workday': (WorkDaySpider.create_spider, "WORKDAY"),
            'workday_two': (WorkDayTwoSpider.create_spider, "WORKDAY_TWO"),
            'workday_id0': (WorkDayId0Spider.create_spider, "WORKDAY_ID0"),
            'oracle_cloud': (OracleCloudSpider.create_spider, "ORACLE_SPIDER"),
            'green_house': (GreenHouseSpider.create_spider, "GreenHouse"),
            'green_house_other': (GreenHouseOtherSpider.create_spider, "GreenHouse_Other"),
            'jobs_lever': (JobsLeverSpider.create_spider, "JobsLever"),
            'job_vite': (JobViteSpider.create_spider, "JobVite"),
            'ultipro': (UltiproSpider.create_spider, "Ultipro"),
            'workforcenow': (WorkforcenowSpider.create_spider, "workforcenow"),
            'workable': (WorkableSpider.create_spider, "Workable"),
            'myjobs_adp': (MyjobsAdpSpider.create_spider, "MyjobsAdp"),
            'microsoft': (MicrosoftSpider.create_spider, "Microsoft"),
            'meta': (MetaSpider.create_spider, "Meta"),
            'search_results': (SearchResultsSpider.create_spider, "SearchResults"),
            'search_results_two': (SearchResultsTwoSpider.create_spider, "SearchResultsTwo"),
            'search_results_ten': (SearchResultsTenSpider.create_spider, "SearchResultsTen"),
            'pfizer': (PfizerSpider.create_spider, "Pfizer"),
            'eightfold': (EightfoldSpider.create_spider, "Eightfold"),
            'jobs_dayforcehcm': (JobsDayforcehcmSpider.create_spider, "JobsDayforcehcm"),
            'us242_dayforcehcm': (Us242DayforcehcmSpider.create_spider, "Us242Dayforcehcm"),
            'job_search_results_job': (JobSearchResultsJobSpider.create_spider, "JobSearchResultsJob"),
            'job_search_results_search': (JobSearchResultsSearchSpider.create_spider, "JobSearchResultsSearch"),
            'search': (SearchSpider.create_spider, "Search"),
            'walmart': (WalmartSpider.create_spider, "Walmart"),
            'visa': (VisaSpider.create_spider, "Visa"),
            'jobs': (JobsSpider.create_spider, "Jobs"),
            'jnj': (JnjSpider.create_spider, "Jnj"),
            'applicant_pool': (ApplicantPoolSpider.create_spider, "ApplicantPool"),
            'bha': (BhaSpider.create_spider, "Bha"),
            'icims': (IcimsSpider.create_spider, "Icims"),
            'oneapp': (OneappSpider.create_spider, "Oneapp"),
            'search_jobs': (SearchJobsSpider.create_spider, "SearchJobs"),
            'search_jobs_two': (SearchJobsTwoSpider.create_spider, "SearchJobsTwo"),
            'whole_foods': (WholeFoodsSpider.create_spider, "WholeFoods"),
            'home_depot': (HomeDepotSpider.create_spider, "HomeDepot"),
            'aep': (AepSpider.create_spider, "Aep"),
            'brassring': (BrassringSpider.create_spider, "Brassring"),
            'bamboohr': (BamboohrSpider.create_spider, "Bamboohr"),
            'hiringroom': (HiringroomSpider.create_spider, "Hiringroom"),
            'bank_of_america': (BankOfAmericaSpider.create_spider, "BankOfAmerica"),
            'abbvie': (AbbvieSpider.create_spider, "Abbvie"),
            'fritolay': (FritolaySpider.create_spider, "Fritolay"),
            'pierce_engineers': (PierceEngineersSpider.create_spider, "PierceEngineers"),
            'jobs_two': (JobsTwoSpider.create_spider, "JobsTwo"),
            'hrmos': (HrmosSpider.create_spider, "Hrmos"),
            'nord_cloud': (NordCloudSpider.create_spider, "NordCloud"),
            'ibm': (IbmSpider.create_spider, "Ibm"),
            'cisco': (CiscoSpider.create_spider, "Cisco"),
            'maravai': (MaravaiSpider.create_spider, "Maravai"),
            'lockheed': (LockheedSpider.create_spider, "Lockheed"),
            'adp': (AdpSpider.create_spider, "Adp"),
            'apply_to_job': (ApplyToJobSpider.create_spider, "ApplyToJob"),
            'agoda': (AgodaSpider.create_spider, "Agoda"),
            'eog': (EogSpider.create_spider, "Eog"),
            'southern': (SouthernSpider.create_spider, "Southern"),
            'mondelez_international': (MondelezInternationalSpider.create_spider, "MondelezInternational"),
            'american_campus': (AmericanCampusSpider.create_spider, "AmericanCampus"),
            'olaplex': (OlaplexSpider.create_spider, "olaplex"),
            'assured': (AssuredSpider.create_spider, "assured"),
            'axogeninc': (AxogenincSpider.create_spider, "axogeninc"),
            'gossamerbio': (GossamerbioSpider.create_spider, "gossamerbio"),
            'skillsoft': (SkillsoftSpider.create_spider, "skillsoft"),
            'government_jobs': (GovernmentJobsSpider.create_spider, "GovernmentJobs"),
            'deciem_prod_atlanta': (DeciemProdAtlantaSpider.create_spider, "DeciemProdAtlanta"),
            'uber': (UberSpider.create_spider, "Uber"),
            'southern_peru': (SouthernPeruSpider.create_spider, "SouthernPeru"),
            'met_life': (MetLifeSpider.create_spider, "MetLife"),
            'costacareers': (CostaCareersSpider.create_spider, "costacareers"),
            'careers_icims': (CareersIcimsSpider.create_spider, "careers_icims"),
            'smartrecruiters': (SmartrecruitersSpider.create_spider, "smartrecruiters"),
            'gohealth': (GohealthSpider.create_spider, "gohealth"),
            'selectquote': (SelectQuoteSpider.create_spider, "selectquote"),
            'bequad': (BequadSpider.create_spider, "bequad"),
            'taleo': (TaleoSpider.create_spider, "taleo"),
            'columbia_sussex': (ColumbiaSussexSpider.create_spider, "ColumbiaSussex"),
            'valero': (ValeroSpider.create_spider, "Valero"),
            'cooperstandard': (CooperStandardSpider.create_spider, "cooperstandard"),
            'limb1': (Limb1Spider.create_spider, "limb1"),
            'linkedin': (LinkedinSpider.create_spider, "linkedin"),
            'baylis': (BaylisSpider.create_spider, "Baylis"),
            'groupe_psa': (GroupePsaSpider.create_spider, "GroupePsa"),
            'amphenol': (AmphenolSpider.create_spider, "Amphenol"),
            'karriere': (KarriereSpider.create_spider, "Karriere"),
            'ultipro_only_num': (UltiproOnlyNumSpider.create_spider, "UltiproOnlyNum"),
            'search_only_num': (SearchOnlyNumSpider.create_spider, "SearchOnlyNum"),
            'job_search_results_search_only_num': (JobSearchResultsSearchOnlyNumSpider.create_spider, "JobSearchResultsSearchOnlyNum"),
            'search_results_only_num': (SearchResultsOnlyNumSpider.create_spider, "SearchResultsOnlyNum"),
            'amphenolrf': (AmphenolrfSpider.create_spider, "Amphenolrf"),
            'farm_foods': (FarmFoodsSpider.create_spider, "FarmFoods"),
            'jobs_only_num': (JobsOnlyNumSpider.create_spider, "JobsOnlyNum"),
            'kkr': (KkrSpider.create_spider, "Kkr"),
            'sk': (SkSpider.create_spider, "Sk"),
            'johnson': (JohnsonSpider.create_spider, "Johnson"),
            'tuniu': (TuniuSpider.create_spider, "Tuniu"),
            'tuniu_xiaozhao': (TuniuXiaozhaoSpider.create_spider, "tuniu_xiaozhao"),
            'tuniu_xiaozhao_1': (TuniuXiaozhaooneSpider.create_spider, "tuniu_xiaozhao_1"),
            'workday_only_num': (WorkdayOnlyNumSpider.create_spider, "workday_only_num"),
            'zimvie': (ZimvieSpider.create_spider, "zimvie"),
            'bigbear': (BigbearSpider.create_spider, "bigbear"),
            'people_click': (PeopleClickSpider.create_spider, "PeopleClick"),
            'oracle_cloud_only_num': (OracleCloudOnlyNumSpider.create_spider, "oracle_cloud_only_num"),
            'dvelectronics': (DvelectronicsSpider.create_spider, "dvelectronics"),
            'teamtailor': (TeamtailorSpider.create_spider, "teamtailor"),
            'icims_two': (IcimsTwoSpider.create_spider, "icims_two"),
            'peopeadmin': (PeopeadminSpider.create_spider, "peopeadmin"),
            'skillsoftone': (SkillsoftOneSpider.create_spider, "skillsoftone"),
            'parkelectro': (ParkelectroSpider.create_spider, "parkelectro"),
            'appone': (ApponeSpider.create_spider, "appone"),
            'training': (TrainingSpider.create_spider, "training"),
            'vuzix': (VuzixSpider.create_spider, "vuzix"),
            'tdcx': (TdcxSpider.create_spider, "tdcx"),
            'ffbusa': (FfbusaSpider.create_spider, "ffbusa"),
            'firstbusinessbank': (FirstbusinessbankSpider.create_spider, "firstbusinessbank"),
            'workforcenow_two': (WorkforcenowTwoSpider.create_spider, "workforcenow_two"),
            'hrsmart': (HrsmartSpider.create_spider, "hrsmart"),
            'airbnb': (AirbnbSpider.create_spider, "Airbnb"),
            'search_jobs_only_num': (SearchJobsOnlyNumSpider.create_spider, "SearchJobsOnlyNum"),
            'rossstores': (RossstoresSpider.create_spider, "Rossstores"),
            'kff': (KffSpider.create_spider, "Kff"),
            'vacasa': (VacasaSpider.create_spider, "vacasa"),
            'senecafoods': (SenecafoodsSpider.create_spider, "senecafoods"),
            'marcuscorp': (MarcuscorpSpider.create_spider, "marcuscorp"),
            'dxl': (DxlSpider.create_spider, "dxl"),
            'huya': (HuyaSpider.create_spider, "huya"),
            'aarons': (AaronsSpider.create_spider, "aarons"),
            'consolidated': (ConsolidatedSpider.create_spider, "consolidated"),
            'havertys': (HavertysSpider.create_spider, "havertys"),
            'materialise': (MaterialiseSpider.create_spider, "Materialise"),
            'lululemon': (LululemonSpider.create_spider, "Lululemon"),
            'diversityjobs': (DiversityjobsSpider.create_spider, "Diversityjobs"),
            'aerosonic': (AerosonicSpider.create_spider, "Aerosonic"),
            'the_applicant_manager': (TheApplicantManagerSpider.create_spider, "TheApplicantManager"),
            'cmc': (CmcSpider.create_spider, "Cmc"),
            'yum': (YumSpider.create_spider, "Yum"),
            'pizzahut': (PizzahutSpider.create_spider, "Pizzahut"),
            'afi': (AfiSpider.create_spider, "Afi"),
            'cummins': (CumminsSpider.create_spider, "Cummins"),
            'us_dayforcehcm_only_num': (UsDayforcehcmOnlyNumSpider.create_spider, "UsDayforcehcmOnlyNum"),
            'careers_orkin': (CareersOrkinSpider.create_spider, "CareersOrkin"),
            'careers_cranepestcontrol': (CareersCranepestcontrolSpider.create_spider, "CareersCranepestcontrol"),
            'careers_clarkpest': (CareersClarkpestSpider.create_spider, "CareersClarkpest"),
            'careers_callnorthwest': (CareersCallnorthwestSpider.create_spider, "CareersCallnorthwest"),
            'amcor': (AmcorSpider.create_spider, "Amcor"),
            'socialpoint': (SocialpointSpider.create_spider, "Socialpoint"),
            'take2games': (Take2gamesSpider.create_spider, "Take2games"),
            'olivegarden': (OlivegardenSpider.create_spider, "Olivegarden"),
            'longhornsteakhouse': (LonghornsteakhouseSpider.create_spider, "Longhornsteakhouse"),
            'bahamabreeze': (BahamabreezeSpider.create_spider, "Bahamabreeze"),
            'yardhouse': (YardhouseSpider.create_spider, "Yardhouse"),
            'thecapitalgrille': (ThecapitalgrilleSpider.create_spider, "Thecapitalgrille"),
            'seasons52': (Seasons52Spider.create_spider, "Seasons52"),
            'cheddars': (CheddarsSpider.create_spider, "Cheddars"),
            'spotifyjobs': (SpotifyjobsSpider.create_spider, "Spotifyjobs"),
            'ea': (EaSpider.create_spider, "Ea"),
            'referrals': (ReferralsSpider.create_spider, "Referrals"),
            'sea': (SeaSpider.create_spider, "Sea"),
            'boots': (BootsSpider.create_spider, "Boots"),
            'ball': (BallSpider.create_spider, "Ball"),
            'solaredge': (SolaredgeSpider.create_spider, "Solaredge"),
            'careers_ui': (CareersUiSpider.create_spider, "CareersUi"),
            'unilab': (UnilabSpider.create_spider, "Unilab"),
            'blueprintgenetics': (BlueprintgeneticsSpider.create_spider, "Blueprintgenetics"),
            'selectminds': (SelectmindsSpider.create_spider, "Selectminds"),
            'depopcareers': (DepopcareersSpider.create_spider, "Depopcareers"),
            'etsy':(EtsySpider.create_spider, "Etsy"),
            'skyworksinc': (SkyworksincSpider.create_spider, "Skyworksinc"),
            'fmc': (FmcSpider.create_spider, "Fmc"),
            'royalcaribbeangroup': (RoyalcaribbeangroupSpider.create_spider, "Royalcaribbeangroup"),
            'checkpoint':(CheckpointSpider.create_spider, "Checkpoint"),
            'tbe_taleo_net': (TbeTaleoNetSpider.create_spider, "TbeTaleoNet"),
            'jobs_two_only_num': (JobsTwoOnlyNumSpider.create_spider, "JobsTwoOnlyNum"),
            'yum_china': (YumChinaSpider.create_spider, "YumChina"),
            'lennar': (LennarSpider.create_spider, "Lennar"),
            'expediagroup': (ExpediagroupSpider.create_spider, "Expediagroup"),
            'hubspot': (HubspotSpider.create_spider, "Hubspot"),
            'teradyne': (TeradyneSpider.create_spider, "Teradyne"),
            'werkenbijlkq': (WerkenbijlkqSpider.create_spider, "Werkenbijlkq"),
            'stahlgruber': (StahlgruberSpider.create_spider, "Stahlgruber"),
            'lkqeurope': (LkqeuropeSpider.create_spider, "Lkqeurope"),
            'pinterestcareers': (PinterestcareersSpider.create_spider, "Pinterestcareers"),
            'bunge': (BungeSpider.create_spider, "Bunge"),
            'clcgroup': (ClcgroupSpider.create_spider, "Clcgroup"),
            'westlaketalent': (WestlaketalentSpider.create_spider, "Westlaketalent"),
            'apachecorp': (ApachecorpSpider.create_spider, "Apachecorp"),
            'ifdsgroup': (IfdsgroupSpider.create_spider, "Ifdsgroup"),
            'mccann': (MccannSpider.create_spider, "Mccann"),
            'inflightcloud': (InflightcloudSpider.create_spider, "Inflightcloud"),
            'internationalpaper': (InternationalpaperSpider.create_spider, "Internationalpaper"),
            'avature': (AvatureSpider.create_spider, "Avature"),
            'kroll': (KrollSpider.create_spider, "Kroll"),
            'hubbell': (HubbellSpider.create_spider, "Hubbell"),
            'applicantpro': (ApplicantproSpider.create_spider, "Applicantpro"),
            'zynga': (ZyngaSpider.create_spider, "Zynga"),
            'quality_carriers': (QualityCarriersSpider.create_spider, "QualityCarriers"),
            'heritagehillsenior': (HeritagehillseniorSpider.create_spider, "Heritagehillsenior"),
            'mercbank': (MercbankSpider.create_spider, "Mercbank"),
            'dswinc': (DswincSpider.create_spider, "Dswinc"),
            'smartrecruiters_total': (SmartrecruitersTotalSpider.create_spider, "SmartrecruitersTotal"),
            'rgp': (RgpSpider.create_spider, "Rgp"),
            'careershealthcare': (CareershealthcareSpider.create_spider, "Careershealthcare"),
            'kellyocg': (KellyocgSpider.create_spider, "Kellyocg"),
            'centerlinedrivers': (CenterlinedriversSpider.create_spider, "Centerlinedrivers"),
            'couchbase': (CouchbaseSpider.create_spider, "Couchbase"),
            'chicos': (ChicosSpider.create_spider, "Chicos"),
            'sabacloud': (SabacloudSpider.create_spider, "Sabacloud"),
            'emergentbiosolutions': (EmergentbiosolutionsSpider.create_spider, "Emergentbiosolutions"),
            'franklincovey': (FranklincoveySpider.create_spider, "Franklincovey"),
            'haynes': (HaynesSpider.create_spider, "Haynes"),
            'bbsistaffingsa': (BbsistaffingsaSpider.create_spider, "Bbsistaffingsa"),
            'horizonbank': (HorizonbankSpider.create_spider, "horizonbank"),
            'dmoleculartherapeutics': (DmoleculartherapeuticsSpider.create_spider, "Dmoleculartherapeutics"),
            'steelcase': (SteelcaseSpider.create_spider, "Steelcase"),
            'shoecarnival': (ShoecarnivalSpider.create_spider, "Shoecarnival"),
            'gupy': (GupySpider.create_spider, "Gupy"),
            'carvana': (CarvanaSpider.create_spider, "Carvana"),
            'triumphgroup':(TriumphgroupSpider.create_spider, "Triumphgroup"),
            'eqt': (EqtSpider.create_spider, "Eqt"),
            'marketaxess': (MarketaxessSpider.create_spider, "Marketaxess"),
            'paramount': (ParamountSpider.create_spider, "Paramount"),
            'carlyle_avature': (CarlyleAvatureSpider.create_spider, "CarlyleAvature"),
            'expandenergy': (ExpandenergySpider.create_spider, "Expandenergy"),
            'seagatecareers': (SeagatecareersSpider.create_spider, "Seagatecareers"),
            'godaddy': (GodaddySpider.create_spider, "Godaddy"),
            'myworkdayjobs': (MyworkdayjobsSpider.create_spider, "Myworkdayjobs"),
            'allstate': (AllstateSpider.create_spider, "Allstate"),
            'ultipro_teleflex': (UltiproSpider.create_spider, "UltiproTeleflex"),
            'teleflex': (TeleflexSpider.create_spider, "Teleflex"),
            'carnival': (CarnivalSpider.create_spider, "Carnival"),
            'hansgrohe': (HansgroheSpider.create_spider, "Hansgrohe"),
            'biorad': (BioradSpider.create_spider, "Biorad"),
            'wynnresorts': (WynnresortsSpider.create_spider, "Wynnresorts"),
            'bill': (BillSpider.create_spider, "Bill"),
            'williamhillgroup': (WilliamhillgroupSpider.create_spider, "Williamhillgroup"),
            'aramarkcareers': (AramarkcareersgroupSpider.create_spider, "Aramarkcareers"),
            'bentley': (BentleySpider.create_spider, "Bentley"),
            'youradv': (YouradvSpider.create_spider, "Youradv"),
            'lawson': (LawsonSpider.create_spider, "Lawson"),
            'oldsecond': (OldsecondSpider.create_spider, "Oldsecond"),
            'hfsinclair': (HfsinclairSpider.create_spider, "Hfsinclair"),
            'eastman': (EastmanSpider.create_spider, "Eastman"),
            'agcocorp': (AgcocorpSpider.create_spider, "Agcocorp"),
            'jobs_aa': (JobsAaSpider.create_spider, "JobsAa"),
            'tapestry': (TapestrySpider.create_spider, "Tapestry"),
            'vailresortscareers': (VailresortscareersSpider.create_spider, "Vailresortscareers"),
            'qorvo': (QorvoSpider.create_spider, "Qorvo"),
            'bathandbodyworks': (BathandbodyworksSpider.create_spider, "Bathandbodyworks"),
            'rgarecareers': (RgarecareersSpider.create_spider, "Rgarecareers"),
            'cubesmart': (CubesmartSpider.create_spider, "Cubesmart"),
            'monroe_energy': (MonroeEnergySpider.create_spider, "MonroeEnergy"),
            'wolt': (WoltSpider.create_spider, "Wolt"),
            'hologic': (HologicSpider.create_spider, "Hologic"),
            'invitation_homes': (InvitationHomesSpider.create_spider, "InvitationHomes"),
            'align': (AlignSpider.create_spider, "Align"),
            'revgroup': (RevgroupSpider.create_spider, "Revgroup"),
            'apponetwo': (ApponeTwoSpider.create_spider, "Apponetwo"),
            'deluxe': (DeluxeSpider.create_spider, "Deluxe"),
            'vempranexa': (VempranexaSpider.create_spider, "Vempranexa"),
            'venparanexa': (VenparanexaSpider.create_spider, "Venparanexa"),
            'aosmd': (AosmdSpider.create_spider, "Aosmd"),
            'partech': (PartechSpider.create_spider, "Partech"),
            'mytheresa': (MytheresaSpider.create_spider, "Mytheresa"),
            'guarantybank': (GuarantybankSpider.create_spider, "Guarantybank"),
            'docgo': (DocgoSpider.create_spider, "Docgo"),
            'datadoghq': (DatadoghqSpider.create_spider, "Datadoghq"),
            'iconplc': (IconplcSpider.create_spider, "Iconplc"),
            'retaildata': (RetaildataSpider.create_spider, "Retaildata"),
            'uhsinc': (UhsincSpider.create_spider, "Uhsinc"),
            'westrockta_avature': (WestrocktaAvatureSpider.create_spider, "WestrocktaAvature"),
            'novocure': (NovocureSpider.create_spider, "Novocure"),
            'hiitsd': (HiitsdSpider.create_spider, "Hiitsd"),
            'huntingtoningalls': (HuntingtoningallsSpider.create_spider, "Huntingtoningalls"),
            'flooranddecor': (FlooranddecorSpider.create_spider, "Flooranddecor"),
            'zuora': (ZuoraSpider.create_spider, "Zuora"),
            'bjsrestaurants': (BjsrestaurantsSpider.create_spider, "Bjsrestaurants"),
            'stratasys': (StratasysSpider.create_spider, "Stratasys"),
            'ionq': (IonqSpider.create_spider, "Ionq"),
            'hcshiring': (HcshiringSpider.create_spider, "Hcshiring"),
            'maxeon': (MaxeonSpider.create_spider, "Maxeon"),
            'radware': (RadwareSpider.create_spider, "Radware"),
            'wowway': (WowwaySpider.create_spider, "Wowway"),
            'geniussports': (GeniussportsSpider.create_spider, "Geniussports"),
            'sweetgreen': (SweetgreenSpider.create_spider, "Sweetgreen"),
            'jeidwen': (JeidwenSpider.create_spider, "Jeidwen"),
            'careered': (CareeredSpider.create_spider, "Careered"),
            'globalindustrial': (GlobalindustrialSpider.create_spider, "Globalindustrial"),
            'cmco': (CmcoSpider.create_spider, "Cmco"),
            'aam': (AamSpider.create_spider, "Aam"),
            'symphonytalent': (SymphonytalentSpider.create_spider, "Symphonytalent"),
            'veeco': (VeecoSpider.create_spider, "Veeco"),
            'vizio': (VizioSpider.create_spider, "Vizio"),
            'aurora': (AuroraSpider.create_spider, "Aurora"),
            'jobs_dayforcehcm_only_num': (JobsDayforcehcmOnlyNumSpider.create_spider, "JobsDayforcehcmOnlyNum"),
            'badcock': (BadcockSpider.create_spider, "Badcock"),
            'paysafe': (PaysafeSpider.create_spider, "Paysafe"),
            'uscellular': (UscellularSpider.create_spider, "Uscellular"),
            'milwaukeejobs': (MilwaukeejobsSpider.create_spider, "Milwaukeejobs"),
            'phf_tbe_taleo': (PhfTbeTaleoSpider.create_spider, "PhfTbeTaleo"),
            'sapiens': (SapiensSpider.create_spider, "Sapiens"),
            'orioncarbons': (OrioncarbonsSpider.create_spider, "Orioncarbons"),
            'kornit': (KornitSpider.create_spider, "Kornit"),
            'tennantco': (TennantcoSpider.create_spider, "Tennantco"),
            'alticeusacareers': (AlticeusacareersSpider.create_spider, "Alticeusacareers"),
            'paycomonline': (PaycomonlineSpider.create_spider, "Paycomonline"),
            'uhaul': (UhaulSpider.create_spider, "Uhaul"),
            'vizirecruiter': (VizirecruiterSpider.create_spider, "Vizirecruiter"),
            'uzf4': (Uzf4Spider.create_spider, "Uzf4"),
            'oatly': (OatlySpider.create_spider, "Oatly"),
            'corecivic': (CorecivicSpider.create_spider, "Corecivic"),
            'avanos': (AvanosSpider.create_spider, "AvanosSpider"),
        }

        if spider_type not in spider_configs:
            raise ValueError(f"Unknown spider type: {spider_type}")

        spider_class, default_name = spider_configs[spider_type]

        if ('company_name' not in kwargs or 'base_url' not in kwargs or
                'company_url' not in kwargs or 'cwiq_code' not in kwargs):
            raise ValueError("spider requires company_name and base_url and company_url and cwiq_code")

        return (
            spider_class(kwargs['company_name'], kwargs['base_url'],
                         kwargs['company_url'], kwargs['cwiq_code']),
            kwargs.get('name', kwargs['company_name'])
        )


def create_and_run_spider_non_blocking(config: dict, factory: SpiderFactory, timeout: int = 1500) -> Tuple[str, dict]:
    """
    非阻塞方式创建和运行单个爬虫任务，支持超时控制

    这是系统的核心执行函数，确保单个爬虫失败不会影响整体流程。
    使用守护线程和超时机制，提供强大的容错能力。

    Args:
        config (dict): 爬虫配置字典，必须包含以下字段：
            - type (str): 爬虫类型，如 'workday', 'greenhouse' 等
            - company_name (str): 公司名称，用于标识和日志
            - base_url (str): 爬虫目标URL
            - company_url (str): 公司官网URL
            - cwiq_code (str): 公司唯一标识码
            - id (int): 配置ID，用于任务标识

        factory (SpiderFactory): 爬虫工厂实例，用于创建爬虫

        timeout (int, optional): 超时时间（秒），默认 1500 秒（25分钟）
            超时后任务会被标记为超时状态，不会阻塞其他任务

    Returns:
        Tuple[str, dict]: 返回爬虫键值和执行结果的元组
            - 键值格式: "{spider_name}_{id}" 或 "{company_name}_{id}"
            - 结果格式: {
                "status": "success" | "error" | "timeout",
                "message": "详细信息",
                "data": [...] (仅成功时包含)
              }

    工作流程:
        1. 创建结果容器 result_holder 用于线程间通信
        2. 生成任务键值用于标识和日志
        3. 定义内部函数 run_spider() 执行实际爬虫逻辑
        4. 启动守护线程执行爬虫任务
        5. 主线程等待指定超时时间
        6. 根据线程状态返回相应结果

    异常处理:
        - 捕获所有爬虫执行异常，返回错误状态而不中断流程
        - 使用守护线程确保主线程退出时自动清理
        - 超时机制防止单个爬虫长时间阻塞

    技术特点:
        - 非阻塞设计：单个爬虫失败不影响其他爬虫
        - 超时保护：防止单个爬虫长时间占用资源
        - 线程安全：使用 result_holder 在线程间安全传递结果
        - 守护线程：主线程退出时自动清理子线程

    使用示例:
        >>> config = {
        ...     'type': 'workday',
        ...     'company_name': 'Microsoft',
        ...     'base_url': 'https://microsoft.wd1.myworkdayjobs.com',
        ...     'company_url': 'https://microsoft.com',
        ...     'cwiq_code': 'MSFT001',
        ...     'id': 12345
        ... }
        >>> factory = SpiderFactory()
        >>> key, result = create_and_run_spider_non_blocking(config, factory, 1800)
        >>> print(f"Task {key}: {result['status']}")

    性能考虑:
        - 每个任务使用独立线程，避免相互影响
        - 超时时间可根据爬虫复杂度调整
        - 守护线程减少资源泄漏风险
    """
    result_holder = {}
    key = config.get("company_name", "unknown") + "_" + str(config.get("id", "unknown"))

    def run_spider():
        try:
            config_copy = config.copy()
            spider_type = config_copy.pop('type')
            spider_instance, spider_name = factory.create_spider(spider_type, **config_copy)
            full_key = f"{spider_name}_{config.get('id')}"
            result = spider_instance.run()
            result_holder['key'] = full_key
            result_holder['result'] = result
            # 不需要在这里记录日志，结果会在后面统一处理
        except Exception as e:
            # 根据错误类型使用不同颜色
            error_msg = str(e).lower()
            error_type = type(e).__name__.lower()

            # SQL和JSON错误 - 黄色
            if any(keyword in error_msg for keyword in ['sql', 'mysql', 'database', 'json', 'decode']) or \
               any(keyword in error_type for keyword in ['sql', 'json']):
                log_warn(f"[{key}] spider SQL/JSON error: {e}")
            # IO相关错误 - 红色
            elif any(keyword in error_msg for keyword in ['file', 'write', 'save', 'permission', 'access']) or \
                 any(keyword in error_type for keyword in ['oserror', 'ioerror', 'permission', 'filenotfound']):
                log_error(f"[{key}] spider IO error: {e}")
            # 解析错误 - 黄色
            elif any(keyword in error_msg for keyword in ['parse', 'html', 'xml', 'response']):
                log_warn(f"[{key}] spider parse error: {e}")
            # 其他错误 - 红色
            else:
                log_error(f"[{key}] spider error: {e}")
            result_holder['key'] = key
            result_holder['result'] = {"status": "error", "message": str(e)}
    # 守护线程会随着主线程退出而退出 daemon=True
    t = threading.Thread(target=run_spider, daemon=True)
    t.start()
    t.join(timeout)

    if t.is_alive():
        log_hint(f"[{key}] spider timed out after {timeout}s")
        return key, {"status": "timeout", "message": f"Timeout after {timeout}s"}
    else:
        return result_holder['key'], result_holder['result']


def process_spider_configs(spider_configs: List[dict], timeout_per_spider: int = 1500) -> Dict[str, dict]:
    """
    批量处理爬虫配置，使用线程池并发执行多个爬虫任务

    这是系统的核心并发处理函数，负责协调和管理所有爬虫任务的并发执行。
    使用 ThreadPoolExecutor 实现高并发处理，支持实时监控和统计。

    Args:
        spider_configs (List[dict]): 爬虫配置列表，每个配置包含完整的爬虫参数
            每个配置字典应包含：
            - type (str): 爬虫类型
            - company_name (str): 公司名称
            - base_url (str): 目标URL
            - company_url (str): 公司官网
            - cwiq_code (str): 公司标识码
            - id (int): 配置ID

        timeout_per_spider (int, optional): 单个爬虫超时时间（秒），默认 1500 秒
            建议根据爬虫复杂度调整：
            - 简单API爬虫: 600-900秒
            - 中等复杂度: 1200-1500秒
            - 复杂爬虫: 1800-2400秒

    Returns:
        Dict[str, dict]: 所有爬虫的执行结果字典
            键: 爬虫标识符 "{spider_name}_{id}"
            值: 执行结果字典 {
                "status": "success" | "error" | "timeout",
                "message": "详细信息",
                "data": [...] (仅成功时包含)
            }

    性能特点:
        - 高并发: 使用 800 个工作线程的线程池
        - 实时监控: 实时输出处理进度和完成情况
        - 容错性: 单个爬虫失败不影响其他爬虫执行
        - 统计信息: 自动统计成功率和完成情况

    工作流程:
        1. 创建 SpiderFactory 实例用于爬虫创建
        2. 初始化 800 线程的 ThreadPoolExecutor
        3. 提交所有爬虫任务到线程池
        4. 使用 wait() 函数监控任务完成情况
        5. 收集所有结果并进行异常处理
        6. 统计成功率并返回完整结果

    并发控制:
        - 最大工作线程: 800 个
        - 任务提交方式: 批量提交所有任务
        - 结果收集: 使用 wait() 等待所有任务完成
        - 异常隔离: 单个任务异常不影响其他任务

    监控和统计:
        - 实时进度: 显示 "已处理/总数" 的进度信息
        - 成功率统计: 计算并记录最终成功率
        - 详细日志: 记录每个任务的完成状态

    异常处理:
        - 任务级异常: 捕获单个任务的执行异常
        - 结果收集异常: 处理 future.result() 的异常
        - 统一错误格式: 所有异常都转换为标准错误格式

    使用示例:
        >>> spider_configs = [
        ...     {
        ...         'type': 'workday',
        ...         'company_name': 'Microsoft',
        ...         'base_url': 'https://microsoft.wd1.myworkdayjobs.com',
        ...         'company_url': 'https://microsoft.com',
        ...         'cwiq_code': 'MSFT001',
        ...         'id': 12345
        ...     },
        ...     {
        ...         'type': 'greenhouse',
        ...         'company_name': 'Google',
        ...         'base_url': 'https://boards.greenhouse.io/google',
        ...         'company_url': 'https://google.com',
        ...         'cwiq_code': 'GOOG001',
        ...         'id': 12346
        ...     }
        ... ]
        >>> results = process_spider_configs(spider_configs, timeout_per_spider=1800)
        >>> success_rate = len([r for r in results.values() if r['status'] == 'success']) / len(results)
        >>> print(f"处理完成，成功率: {success_rate:.2%}")

    性能优化建议:
        1. 根据系统资源调整线程池大小
        2. 根据爬虫类型优化超时时间设置
        3. 监控内存使用情况，必要时分批处理
        4. 定期检查和优化爬虫配置

    注意事项:
        - 线程池大小 (800) 应根据系统资源调整
        - 超时时间应根据网络状况和爬虫复杂度设置
        - 大量并发可能对目标网站造成压力，需要合理控制
        - 建议监控系统资源使用情况，避免过载
    """
    all_results = {}
    factory = SpiderFactory()
    total_configs = len(spider_configs)
    processed_count = 0

    with ThreadPoolExecutor(max_workers=800) as executor:
        futures = {
            executor.submit(create_and_run_spider_non_blocking, config, factory, timeout_per_spider): config for config in spider_configs
        }

        for future in wait(futures, return_when=FIRST_COMPLETED)[0] | wait(futures, return_when=FIRST_COMPLETED)[1]:
            config = futures[future]
            try:
                key, result = future.result()
            except Exception as e:
                key = config.get("type", "unknown") + "_" + str(config.get("id", "unknown"))
                result = {"status": "error", "message": str(e)}

            all_results[key] = result
            processed_count += 1

            # 使用进度条显示，每处理100个或达到特定百分比时更新
            if processed_count % 100 == 0 or processed_count == total_configs:
                progress_percent = (processed_count / total_configs) * 100
                progress_bar = create_progress_bar(progress_percent)
                log_hint(f"Progress: {progress_bar} {processed_count}/{total_configs} ({progress_percent:.1f}%)")

    success_count = sum(1 for r in all_results.values() if r.get("status") == "success")
    log_success(f"All processing completed: {success_count}/{total_configs} spiders succeeded")
    return all_results


def begin():
    """
    爬虫系统的主入口函数，协调整个爬虫流程的执行

    这是整个 Marzaha 爬虫系统的核心协调函数，负责管理从配置加载到数据输出的完整流程。
    包括配置加载、爬虫执行、数据处理、报告生成和结果统计等所有主要环节。

    主要功能:
        1. 时间管理: 计算批次开始时间（整点或半点）
        2. 配置加载: 动态加载所有爬虫配置（16,000+）
        3. 爬虫执行: 并发执行所有爬虫任务
        4. 数据处理: 数据库优化、检查和验证
        5. 报告生成: 并行生成多种格式的数据报告
        6. 数据清理: 清理过期批次数据
        7. 结果统计: 详细的执行统计和日志记录

    执行流程:
        开始 → 时间计算 → 加载配置 → 并发执行爬虫 → 数据库优化 →
        数据检查 → 并行生成报告 → 清理数据 → 统计结果 → 结束

    时间管理:
        - 批次时间基于整点或半点计算
        - 用于数据批次标识和时间统计
        - 确保数据的时间一致性

    数据处理阶段:
        1. 表优化: optimize_jobs_table() - 优化数据库表性能
        2. 数据查询: query_and_append_company_to_json() - 生成公司信息JSON
        3. 重复检查: check_repeat_data() - 检查和处理重复数据
        4. 活跃检查: check_active_data() - 验证活跃数据状态

    并行报告生成:
        使用 5 个线程并行生成以下报告：
        - getNoJobsParquet: 无职位公司报告
        - getStatusParquet: 状态统计报告
        - getInfoParquet: 信息汇总报告
        - getActiveJobsParquet: 活跃职位报告
        - getMaxBatchInfoParquet: 批次信息报告

    性能监控:
        - 执行时间统计: 精确到秒的总执行时间
        - 成功率统计: 成功/失败爬虫的详细统计
        - 实时日志: 每个阶段的详细日志记录
        - 资源监控: 数据库连接和内存使用监控

    异常处理:
        - 各阶段独立的异常处理
        - 详细的错误日志记录
        - 确保数据库连接正确关闭
        - 失败任务的详细错误信息

    使用场景:
        - 定时任务调度器调用
        - 手动执行单次爬虫流程
        - 系统启动时的初始化执行

    调用方式:
        >>> begin()  # 执行完整的爬虫流程

    注意事项:
        - 函数执行时间较长（通常几小时）
        - 需要确保数据库连接稳定
        - 建议在资源充足的环境中运行
        - 执行期间会产生大量日志

    Returns:
        None: 函数无返回值，所有结果通过日志输出
    """
    now = datetime.now()
    if now.minute < 30:
        start_time = now.replace(minute=0, second=0, microsecond=0)
    else:
        start_time = now.replace(minute=30, second=0, microsecond=0)

    log_hint("Starting spider processing...")

    # 加载爬虫配置
    config_loader = SpiderConfigLoader()
    spider_configs = config_loader.load_config()

    log_success(f"Loaded {len(spider_configs)} spider configs")
    results = process_spider_configs(spider_configs)

    # 创建DBOperations实例
    db_handler = DBOperations()

    db_handler.optimize_jobs_table()

    batch_results = db_handler.query_and_append_company_to_json(start_time)
    log_database_operation("query_and_append_company_to_json", True, f"Result: {batch_results}")

    # 调用 check 方法
    check_results = db_handler.check_repeat_data()
    log_database_operation("check_repeat_data", True, f"Result: {check_results}")
    check_active_results = db_handler.check_active_data()
    log_database_operation("check_active_data", True, f"Result: {check_active_results}")

    # 并行执行数据库操作
    with ThreadPoolExecutor(max_workers=5) as executor:
        futures = [
            executor.submit(db_handler.getNoJobsParquet, start_time),
            executor.submit(db_handler.getStatusParquet, start_time),
            executor.submit(db_handler.getInfoParquet, start_time),
            executor.submit(db_handler.getActiveJobsParquet, start_time),
            executor.submit(db_handler.getMaxBatchInfoParquet, start_time)
        ]
        parquet_results = [f.result() for f in futures]
        log_file_operation("generate", "parquet files", True, f"Results: {parquet_results}")

    delete_results = db_handler.delete_batch_jobs()
    log_database_operation("delete_batch_jobs", True, f"Result: {delete_results}")

    # 关闭数据库会话
    db_handler.close()

    duration = (datetime.now() - start_time).total_seconds()
    log_success(f"All processing completed in {duration:.2f} seconds")

    # 输出详细结果
    for key, result in results.items():
        status = result.get("status", "unknown")
        name, _id = key.rsplit('_', 1)
        message = result.get('message', 'Unknown error')
        log_spider_result(name, status, message)


def run_scheduler():
    """
    启动定时任务调度器，实现爬虫系统的自动化定时执行

    这个函数实现了爬虫系统的自动化调度功能，支持定时执行、任务合并和实例控制。
    使用 APScheduler 的 BlockingScheduler 实现稳定的定时任务调度。

    调度器配置:
        - 调度间隔: 每 30 分钟执行一次 begin() 函数
        - 任务合并: coalesce=True 避免任务堆积和重复执行
        - 最大实例: max_instances=3 限制同时运行的任务实例数
        - 调度器类型: BlockingScheduler 阻塞式调度，适合独立进程

    工作机制:
        1. 首次执行: 立即执行一次 begin() 函数，确保系统启动后立即开始工作
        2. 定时调度: 设置 30 分钟间隔的定时任务，持续自动执行
        3. 异常处理: 捕获键盘中断和系统退出信号，优雅关闭调度器
        4. 持续运行: 主线程保持运行状态，维持调度器活跃

    调度参数详解:
        - interval: 间隔调度，每30分钟触发一次
        - coalesce=True: 如果由于某种原因错过了调度时间，只执行一次而不是补齐所有错过的执行
        - max_instances=3: 最多允许3个 begin() 函数同时运行，防止任务堆积

    使用场景:
        - 生产环境的持续运行: 7x24小时自动化数据采集
        - 无人值守的爬虫系统: 减少人工干预需求
        - 定时数据更新: 确保数据的及时性和完整性

    异常处理:
        - KeyboardInterrupt: 捕获 Ctrl+C 中断信号
        - SystemExit: 捕获系统退出信号
        - 优雅关闭: 调用 scheduler.shutdown() 正确关闭调度器
        - 日志记录: 记录调度器的启动和关闭状态

    注意事项:
        - 函数中存在重复调用 first_task_and_schedule() 的问题，建议修复
        - 调度器是阻塞式的，会占用主线程
        - 需要确保系统资源充足，支持长时间运行
        - 建议配合进程监控工具使用，确保服务稳定性

    启动方式:
        >>> run_scheduler()  # 启动定时调度器，程序将持续运行

    停止方式:
        - Ctrl+C: 发送键盘中断信号
        - kill 信号: 发送系统终止信号
        - 程序会捕获信号并优雅关闭

    监控建议:
        - 监控调度器运行状态
        - 检查任务执行日志
        - 关注系统资源使用情况
        - 定期检查数据产出质量

    性能考虑:
        - 30分钟间隔适合大多数数据更新需求
        - max_instances=3 平衡了并发性和资源使用
        - coalesce=True 避免了任务堆积问题

    Returns:
        None: 函数不返回，持续运行直到被中断
    """
    scheduler = BlockingScheduler()

    def first_task_and_schedule():
        """运行第一次任务并设置定时任务"""
        # 运行第一次任务
        begin()

        # 从当前时间开始，每隔 30 分钟运行
        scheduler.add_job(begin, 'interval', minutes=30, coalesce=True, max_instances=3)
        scheduler.start()
        log_success("Scheduler started with a 30-minute interval.")

    # 执行第一次任务并启动调度器
    first_task_and_schedule()

    # 主线程保持运行
    try:
        first_task_and_schedule()
        while True:
            pass
    except (KeyboardInterrupt, SystemExit):
        log_hint("Shutting down scheduler...")
        scheduler.shutdown()


if __name__ == "__main__":
    """
    主程序入口点 - 单次执行模式

    当直接运行 spider.py 文件时执行此代码块。
    这是单次执行模式，执行一次完整的爬虫流程后自动退出。

    执行流程:
        1. 初始化日志系统 - 设置日志格式、级别和输出目标
        2. 执行爬虫主流程 - 调用 begin() 函数执行完整流程
        3. 记录完成日志 - 标记任务完成状态
        4. 正常退出 - 程序自动结束，无需手动干预

    使用场景:
        - 手动执行单次爬虫任务
        - 测试和调试爬虫系统
        - 定时任务脚本调用
        - 容器化部署的单次任务

    与调度器模式的区别:
        - 单次执行: python spider.py (执行一次后退出)
        - 调度器模式: 需要调用 run_scheduler() (持续运行)

    异常处理:
        - 捕获所有顶级异常
        - 记录详细错误信息到日志
        - 确保程序不会因异常而崩溃

    日志配置:
        - 调用 setup_logging() 初始化日志系统
        - 支持文件和控制台双重输出
        - 自动日志轮转和历史保留

    执行命令:
        $ python spider.py

    预期输出:
        - 详细的执行日志
        - 进度和状态信息
        - 最终的统计结果
        - 完成确认消息

    注意事项:
        - 执行时间较长（通常几小时）
        - 需要稳定的网络连接
        - 确保数据库服务可用
        - 监控系统资源使用情况
    """
    try:
        # 设置日志系统 - 初始化日志配置，支持文件和控制台输出
        setup_logging()

        # 执行一次爬虫主流程 - 完整的数据采集和处理流程
        begin()

        # 程序执行完毕后自动退出，不需手动干预
        log_success("Spider job finished, exiting process.")

    except Exception as e:
        # 捕获并记录所有未处理的异常，确保错误信息被正确记录
        log_error(f"Error in main process: {e}")
        # 注意：这里可以考虑添加 sys.exit(1) 来明确表示异常退出
