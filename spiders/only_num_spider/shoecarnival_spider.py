
import re

from spiders.core_spider.base_spider import BaseSpider
from utils.country_utils import Country<PERSON>odeUtils
import logging
from datetime import datetime
from lxml import etree
from utils.http_utils.http_client import HTTPClient


# ultipro的模板方法
class ShoecarnivalSpider(BaseSpider):
    def __init__(self, company_name, base_url, company_url, cwiq_code):
        super().__init__(company_name, base_url, company_url, cwiq_code)
        self.company_name = company_name
        self.company_url = company_url
        self.cwiq_code = cwiq_code
        self.country_utils = CountryCodeUtils()
        self.headers = {
    'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
    'accept-language': 'zh-CN,zh;q=0.9',
    'cache-control': 'max-age=0',
    'if-none-match': 'W/"779d22f87d98bf64c25a362969edac5b"',
    'priority': 'u=0, i',
    'referer': 'https://careers.shoecarnival.com/jobs/store-associate-part-time-vero-beach-florida-united-states-59619629-1045-4c17-98c0-f3c26598b9c8',
    'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"',
    'sec-ch-ua-mobile': '?0',
    'sec-ch-ua-platform': '"Windows"',
    'sec-fetch-dest': 'document',
    'sec-fetch-mode': 'navigate',
    'sec-fetch-site': 'same-origin',
    'sec-fetch-user': '?1',
    'upgrade-insecure-requests': '1',
    'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36',
    # 'cookie': 'ctc=58ab46df13ef15d31841f46c862726cecb3c2f5f4819b53c4086bc45074dc311bcb978a01e8fba; ctc_session=cef4083bcf620560c1318877a287b38a8d0066745b7e09314bd9237a19f8e603ced143e9c43b86; _clinch_session=Wz%2B4hieONu8KkQ6HjcKSXp%2FIUnsfm%2B2dZvvDTPvMi890bYHlH%2B%2Fgy%2Fa1P0569ck8oKTimo9JhHgrwu1PAZjR%2FKDfFcJQJRBf5pghjG8Df04CcCEzjQKGxkJIvUldJZZDOU%2F2EM6D5d7o4qpFPoB39cgkGibnv%2BjWfopoWI8JFnvUHj%2BMCICfqqL6ChpLHka0UrmabEK3CHzR9MKyKkyjZItg%2FGpheVPrJ4Z4U%2FjYSlGSZSxpA6ssLKDAKvmfnn5TlweSPYPUN6LlFmOn1TYOlZ6xK%2FZa7Vs%3D--vh0ZENsOrCNkIkoU--EhSpBgRpPhn%2BxuZngxXNCg%3D%3D; aws-waf-token=298edbaa-be19-488d-9019-b33bf38c94f5:EgoAf2ZNx9kyAAAA:amUDpDMjQTne/73iwLVX+hYopNytl8Aoa4+03Lmg+SwCf3OItV5OKZE6zzCOxEMBQs99WvdkHGbBTrA0dBDv1jKk2ry74It/wwIk25GVEKk0YiQ4xUw3Ub4upI/ef/GB1/mENdJWesoT9q7DesN430b8CpE/AvFquWPbpceTcTxEpB2eaXmXyaEHqMb64hmf0RpzzmsOVojnW+sr873BN8Y6SUDX9udYXCfNIt2Rv2JUMh4G2DlmOPzaaugQs7f8zDwjKLbhGLzKR3wYrncQ',
}
        # 创建 HTTP 客户端，使用全局代理配置
        self.client = HTTPClient()

    @classmethod
    def create_spider(cls, company_name, base_url, company_url, cwiq_code):
        """工厂方法创建 MedtronicSpider 实例"""
        return cls(company_name, base_url, company_url, cwiq_code)

    def make_request(self, method, url, company_name, requset_count,**kwargs):
        """发送请求的通用方法"""
        kwargs['headers'] = self.headers
        return self.client.request(method, url, company_name, requset_count,log_ip=False, **kwargs)

    def get_total_jobs(self):
        """获取总职位数"""
        try:
            response = self.make_request('GET', self.base_url, self.company_name,requset_count=3)
            tree = etree.HTML(response.text)
            total = tree.xpath(
                "//div[@class='job-search-results-table job-search-results-content']/div[@class='row pagination-container']/div[@class='col-md-4']/div[@class='table-counts']/p/b[2]//text()")[
                0]

            return total
        except Exception as e:
            logging.error(f"Error getting total jobs: {e} for {self.company_name}")
            return 0

    def parse_job(self, job_data):
        """解析职位数据"""
        pass

    def fetch_jobs(self, max_workers):
        """获取所有职位信息"""
        try:
            total_jobs = self.get_total_jobs()
            if total_jobs == 0:
                logging.info(f"No jobs found {self.company_name}")
                return [], 0

            logging.info(f"Found {total_jobs} total jobs")
            return [], total_jobs
        except Exception as e:
            logging.error(f"Error in fetch_jobs: {e}:{self.company_name}")
            return [], 0

    def __del__(self):
        """确保 HTTP 客户端被正确关闭"""
        if hasattr(self, 'client'):
            self.client.close()
