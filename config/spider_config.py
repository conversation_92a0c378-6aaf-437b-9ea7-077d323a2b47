import yaml
import os
import logging
from typing import List, Dict, Any
from utils.colored_logger import log_exception


class SpiderConfigLoader:
    def __init__(self, config_path: str = None):
        self.config_path = config_path or os.path.join('config', 'spiders.yaml')

    def load_config(self) -> List[Dict[str, Any]]:
        """
        从YAML文件加载爬虫配置

        Returns:
            List[Dict[str, Any]]: 已启用的爬虫配置列表
        """
        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)

            if not config or 'spiders' not in config:
                raise ValueError("Invalid configuration file: 'spiders' section is missing")

            # 只返回enabled为true的爬虫配置，并添加编号信息
            enabled_spiders = [
                {**{k: v for k, v in spider.items() if k != 'enabled'}, 'id': idx + 1}
                for idx, spider in enumerate(config['spiders'])
                if spider.get('enabled', True)  # 默认为启用
            ]

            logging.info(f"Loaded {len(enabled_spiders)} enabled spider configurations")
            return enabled_spiders

        except FileNotFoundError as e:
            log_exception(e, f"Configuration file not found: {self.config_path}")
            raise
        except yaml.YAMLError as e:
            log_exception(e, "Error parsing YAML configuration")
            raise
        except Exception as e:
            log_exception(e, "Error loading spider configuration")
            raise