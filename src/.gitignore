# 特定文件
GPATH
GRTAGS
GTAGS
tasks.ini
tags
tag-ja
tag.lock
.netrwhist
.neoconf.json
.vimspector.json

# 配置文件（包含敏感信息，不应提交到版本控制）
config/settings.ini

# 文件通配
/pip
/pip3
/python
/python3
core.[0-9]*
*.7z
*.bak
*.exe
*.ini
*.log
*.py[d|c]
*.rar
*.so
*.swp
*.dll
*.tags
*.tar
*.tar.*
*.zip
*.tags
*.wildignore
*DS_Store

# 文件夹
leo/
docs/
assets/
build/
dist/
target/
release/
venv/
.venv/
.hg/
.idea/
.ipynb_checkpoints/
.local/
.cache/
.root/
.svn/
.vim/
.vscode/
.vs/
.ccls-cache/
__pycache__/

# loadrunner
*.sdf
combined_*.c
ThumbnailsCache.tmp
*.bak
pre_cci.c
*.ci
*.idx
*.c.pickle
mdrv_cmd.txt
options.txt

