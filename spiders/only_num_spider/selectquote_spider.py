import json
import random
import time
from datetime import datetime
import logging
from urllib.parse import urlparse
from concurrent.futures import ThreadPoolExecutor, as_completed
from spiders.core_spider.base_spider import BaseSpider
from utils.country_utils import CountryCodeUtils
from utils.http_utils.http_client import HTTPClient
from bs4 import BeautifulSoup
import re
from lxml import etree

class SelectQuoteSpider(BaseSpider):
    def __init__(self, company_name, base_url, company_url, cwiq_code):
        super().__init__(company_name, base_url, company_url, cwiq_code)
        self.company_name = company_name
        self.company_url = company_url
        self.cwiq_code = cwiq_code
        self.country_utils = CountryCodeUtils()
        self.headers = {
            'accept': 'application/json, text/plain, */*',
            'accept-language': 'zh-CN,zh;q=0.9',
            'cache-control': 'no-cache',
            'pragma': 'no-cache',
            'priority': 'u=1, i',
            'referer': 'https://careers.selectquote.com/career-home/jobs',
            'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"Windows"',
            'sec-fetch-dest': 'empty',
            'sec-fetch-mode': 'cors',
            'sec-fetch-site': 'same-origin',
            'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36',
            # 'cookie': 'i18n=en-US; searchSource=external; jrasession=7ca248b2-d9cf-4417-a779-1edea7a0bbc6; jasession=s%3APriJ6kFEmY5NyVPu1_FMAD48snWP2tUt.8WBXVNFsr6VO7zbgQMY%2BAWxzCUo%2BFu1L%2BLOKcKqZeUE; _janalytics_ses.4474=*; _janalytics_id.4474=93cf9611-7299-4092-a7d9-1e0e9cb3809b.1746684812.1.1746684821.1746684812.64e74394-2458-40e6-9267-5b458f0420d2',
        }

        # 创建 HTTP 客户端
        self.client = HTTPClient()

    @classmethod
    def create_spider(cls, company_name, base_url, company_url, cwiq_code):
        """工厂方法创建 MedtronicSpider 实例"""
        return cls(company_name, base_url, company_url, cwiq_code)

    def make_request(self, method, url, company_name,requset_count, **kwargs):
        """发送请求的通用方法"""
        kwargs['headers'] = self.headers
        return self.client.request(method, url, company_name, requset_count,log_ip=False, **kwargs)

    def get_total_jobs(self):
        try:
            params = {
                'page': '1',
                'sortBy': 'relevance',
                'descending': 'false',
                'internal': 'false',
            }
            cookies = {
                'i18n': 'en-US',
                'searchSource': 'external',
                'jrasession': '7ca248b2-d9cf-4417-a779-1edea7a0bbc6',
                'jasession': 's%3APriJ6kFEmY5NyVPu1_FMAD48snWP2tUt.8WBXVNFsr6VO7zbgQMY%2BAWxzCUo%2BFu1L%2BLOKcKqZeUE',
                '_janalytics_ses.4474': '*',
                '_janalytics_id.4474': '93cf9611-7299-4092-a7d9-1e0e9cb3809b.1746684812.1.1746684821.1746684812.64e74394-2458-40e6-9267-5b458f0420d2',
            }
            response = self.make_request('GET', self.base_url, self.company_name, requset_count=3,params=params,cookies=cookies )
            number = response.json()['totalCount']
            return number
        except Exception as e:
            logging.error(f"Error getting total jobs: {e} for {self.company_name}")
            return 0

    def parse_job(self, job_data):
        """解析职位数据"""
        pass

    def fetch_jobs(self,max_workers):
        """获取职位信息"""
        try:
            total_jobs = self.get_total_jobs()
            if total_jobs == 0:
                logging.info(f"No jobs found {self.company_name}")
                return [], 0

            logging.info(f"Found {total_jobs} total jobs")
            return [], total_jobs
        except Exception as e:
            logging.error(f"Error in fetch_jobs: {e}:{self.company_name}")
            return [],0

    def __del__(self):
        """确保 HTTP 客户端被正确关闭"""
        if hasattr(self, 'client'):
            self.client.close()
