import logging
import requests
from abc import ABC, abstractmethod
from datetime import datetime
import concurrent.futures
from JsonTypedDict.active_job_type import ActiveJob
from utils.db_operations import DBOperations


class BaseSpider(ABC):
    def __init__(self, company_name, base_url, company_url, cwiq_code):
        self.company_name = company_name
        self.base_url = base_url
        self.company_url = company_url
        self.cwiq_code = cwiq_code
        self.db_ops = DBOperations()
        self.session = requests.Session()

    @abstractmethod
    def parse_job(self, job_data):
        """解析单个工作数据"""
        pass

    @abstractmethod
    def fetch_jobs(self, max_workers):
        """获取工作列表"""
        pass

    def fetch_jobs_with_timeout(self, max_workers, timeout=1800):
        with concurrent.futures.ThreadPoolExecutor(max_workers=1) as executor:
            future = executor.submit(self.fetch_jobs, max_workers)
            try:
                current_jobs = future.result(timeout=timeout)  # 设置超时时间
            except concurrent.futures.TimeoutError:
                logging.error(f"fetch_jobs 执行时间超过 {timeout} 秒，已强制停止。")
                current_jobs = []  # 超时返回空列表
        return current_jobs

    def Loop_forging_id(self,current_jobs, previous_job_ids,total_jobs,previous_total_jobs,repeated_job_ids):
        fake_ids = []
        state = None
        if not repeated_job_ids:
            fake_len = len(previous_total_jobs) - len(total_jobs) + len(current_jobs)
            state = True
            for i in range(abs(fake_len)):
                fake_ids.append(f'0000000{i}')
        else:
            fake_len = int(total_jobs) - len(current_jobs) - int(previous_total_jobs) + len(previous_job_ids)
            if fake_len > 0:
                state = True
            else:
                state = False
            for i in range(abs(fake_len)):
                fake_ids.append(f'0000000{i}')
        return fake_ids,state

    def run(self):
        """运行爬虫"""
        start_time = datetime.now()
        scrape_time = start_time.time()
        scrape_date = start_time.date()

        try:
            # 获取最新批次
            latest_batch = self.db_ops.get_latest_batch(self.company_url)
            batch_id = latest_batch.batchId if latest_batch else 0

            # 获取上一批次的工作ID
            previous_job_ids = self.db_ops.get_job_ids_by_batch(
                self.company_url,
                batch_id,
                status=[0, 1]
            )

            #获取上一批次的total_jobs
            if self.company_url == 'https://careers.walmart.com/results':
                previous_total_jobs = self.db_ops.get_total_jobs_by_batch_id(batch_id,self.company_url)

            logging.info(f"Previous job IDs: {len(previous_job_ids)}")
            # 去重
            previous_job_ids = list(set(previous_job_ids))
            logging.info(f"Unique previous job IDs: {len(previous_job_ids)}")

            last_previous_job_ids = []
            # 获取上上一批次的工作ID
            if batch_id > 1:
                last_previous_job_ids = self.db_ops.get_job_ids_by_batch(
                    self.company_url,
                    batch_id -1,
                    status=[0, 1]
                )

            # 线程池执行数量
            max_workers = 25
            # 获取并解析新数据
            current_jobs,total_jobs =  self.fetch_jobs(max_workers)

            logging.info(f"Current jobs: {len(current_jobs)}")
            #去重
            current_jobs_dict = {item["jobId"]: item for item in current_jobs}
            current_jobs = list(current_jobs_dict.values())
            logging.info(f"Unique current jobs: {len(current_jobs)}")
            success_rate = 1
            # 检查数据量是否足够
            if not self.company_url=='https://careers.walmart.com/results':
                if total_jobs:
                    if total_jobs =="no total_jobs":
                        pass
                    else:
                        if type(total_jobs) == int:
                            pass
                        else:
                            total_jobs = int(total_jobs)
                        if not total_jobs == 0:
                            success_rate = len(current_jobs)/total_jobs
                            if success_rate < 0.95:
                                logging.info(f"成功率为{success_rate}")

            # 如果当前没有工作，则记录没有工作的信息
            if not current_jobs:
                self.db_ops.get_no_jobs(
                    company=self.company_name,
                    company_url=self.company_url,
                    batch_id=batch_id,
                )

            # 分类处理数据
            current_job_ids = [job["jobId"] for job in current_jobs]

            if self.company_url != 'https://careers.walmart.com/results':
                # 普通公司逻辑
                if not current_jobs:
                    current_job_ids = previous_job_ids
                    repeated_job_ids = previous_job_ids
                else:
                    repeated_job_ids = [jid for jid in current_job_ids if jid in previous_job_ids]

                new_job_ids = [jid for jid in current_job_ids if jid not in previous_job_ids]
                deleted_job_ids = [jid for jid in previous_job_ids if jid not in current_job_ids]

            else:
                if not current_jobs:
                    current_job_ids = previous_job_ids
                    repeated_job_ids = previous_job_ids
                else:
                    repeated_job_ids = [jid for jid in current_job_ids if jid in previous_job_ids]

                    # 沃尔玛特殊逻辑
                fake_ids, state = self.Loop_forging_id(current_jobs, previous_job_ids, total_jobs, previous_total_jobs, repeated_job_ids)

                if state is True:
                    new_job_ids = [jid for jid in current_job_ids if jid not in previous_job_ids]
                    deleted_job_ids = [jid for jid in previous_job_ids if jid not in current_job_ids] + fake_ids
                elif state is False:
                    new_job_ids = [jid for jid in current_job_ids if jid not in previous_job_ids]
                    deleted_job_ids = [jid for jid in previous_job_ids if jid not in current_job_ids] + fake_ids
                else:
                    new_job_ids = [jid for jid in current_job_ids if jid not in previous_job_ids]
                    deleted_job_ids = [jid for jid in previous_job_ids if jid not in current_job_ids]

            # 记录日志
            logging.info(f"{self.company_name} Spider Results:")
            logging.info(f"New jobs: {len(new_job_ids)}")
            logging.info(f"Repeated jobs: {len(repeated_job_ids)}")
            logging.info(f"Deleted jobs: {len(deleted_job_ids)}")

            # 处理新数据
            seen_job_ids = set()  # 用于去重的集合
            temp_jobs = []  # 存储不重复的新工作
            new_jobs = []  # 初始化空列表
            new_active_jobs = []

            for job in current_jobs:
                job_id = job["jobId"]
                # 检查是否是新工作且未重复
                if job_id in new_job_ids and job_id not in seen_job_ids:
                    seen_job_ids.add(job_id)  # 记录已处理的job_id
                    temp_jobs.append(job)

            last_batch_repost_ids = []
            #处理上上批次出现过的数据
            if last_previous_job_ids:
                last_batch_repost_ids = [jib for jib in new_job_ids if jib in last_previous_job_ids]

            # 获取历史发布过、历史没发布过、一周内没发布过、一天内发布过和一天内没发布过的工作列表
            query_repost_job_ids, no_repost_job_ids, no_week_repost_job_ids, day_repost_job_ids, no_day_repost_job_ids \
                = self.db_ops.is_job_reposted(company_url=self.company_url, job_ids=seen_job_ids, now_time=start_time)

            # 更新历史发布过的工作上架时间
            self.db_ops.update_repost_jobs(company_url=self.company_url, job_ids=query_repost_job_ids,
                                           post_time=start_time)

            # 添加历史没发布过的工作信息
            self.db_ops.save_no_repost_jobs(company_url=self.company_url, job_ids=no_repost_job_ids,
                                            post_time=start_time)
            for job in temp_jobs:
                job_id = job["jobId"]
                new_job = {}
                # 构建历史没发布过的工作字典
                if job_id in no_repost_job_ids:
                    new_job = {
                        **job,
                        "batchId": batch_id + 1,
                        "scrape_time": scrape_time,
                        "scrape_date": scrape_date,
                        "jobRepost": 0,
                        "week_jobRepost": 0,
                        "day_jobRepost": 0
                    }
                # 构建历史发布过但一周内没发布过的工作字典
                if job_id in no_week_repost_job_ids:
                    new_job = {
                        **job,
                        "batchId": batch_id + 1,
                        "scrape_time": scrape_time,
                        "scrape_date": scrape_date,
                        "jobRepost": 1,
                        "week_jobRepost": 0,
                        "day_jobRepost": 0
                    }
                # 构建一周内发布过但一天内没发布过的工作字典
                if job_id in no_day_repost_job_ids:
                    new_job = {
                        **job,
                        "batchId": batch_id + 1,
                        "scrape_time": scrape_time,
                        "scrape_date": scrape_date,
                        "jobRepost": 1,
                        "week_jobRepost": 1,
                        "day_jobRepost": 0
                    }
                # 构建一天内发布过的工作字典
                if job_id in day_repost_job_ids:
                    new_job = {
                        **job,
                        "batchId": batch_id + 1,
                        "scrape_time": scrape_time,
                        "scrape_date": scrape_date,
                        "jobRepost": 1,
                        "week_jobRepost": 1,
                        "day_jobRepost": 1
                    }
                new_active_job: ActiveJob = {
                    "jobId": job["jobId"],
                    "jobTitle": job["jobTitle"],
                    "company": job["company"],
                    "city": job["city"],
                    "jobDescription": job["jobDescription"],
                    "jobUrl": job["jobUrl"],
                    "companyUrl": job["companyUrl"],
                    "activeTime": datetime.now(),
                }
                # 将新工作添加到列表中
                new_jobs.append(new_job)
                new_active_jobs.append(new_active_job)

            new_jobs = [
                {**job,  # 解构原有的job字典
                 "last_batch_repost": 1  # 如果jobId在last_batch_repost_ids中，添加last_batch_repost: 1
                 }
                if job["jobId"] in last_batch_repost_ids
                else {**job,  # 否则，添加last_batch_repost: 0
                      "last_batch_repost": 0
                      }
                for job in new_jobs
            ]

            self.db_ops.save_new_jobs(new_jobs)
            self.db_ops.save_new_active_jobs(new_active_jobs)
            self.db_ops.delete_inactive_jobs(deleted_job_ids, company_url=self.company_url)

            # 处理重复和删除的数据
            self.db_ops.process_rep_del_jobs(
                company_url=self.company_url,
                batch_id=batch_id,
                cwiq_code=self.cwiq_code,
                scrape_time=scrape_time,
                scrape_date=scrape_date,
                jobIdGroupOld=deleted_job_ids,
                jobIdGroupRep=repeated_job_ids
            )

            # 创建新批次信息
            self.db_ops.create_batch_info(
                company=self.company_name,
                company_url=self.company_url,
                cwiq_code = self.cwiq_code,
                batch_id=batch_id,
                scrape_time=scrape_time,
                scrape_date=scrape_date,
                total_jobs=total_jobs,
                success_rate=success_rate
            )

            # 计算运行时间
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()
            logging.info(f"{self.company_name} Spider completed in {duration:.2f} seconds")

            return {
                "status": "success",
                "data": {
                    "new_jobs": len(new_job_ids),
                    "repeated_jobs": len(repeated_job_ids),
                    "deleted_jobs": len(deleted_job_ids),
                    "duration": duration
                }
            }

        except Exception as e:
            logging.error(f"Error in {self.company_name} spider: {str(e)}")
            return {
                "status": "error",
                "message": str(e)
            }
        finally:
            self.db_ops.close()
