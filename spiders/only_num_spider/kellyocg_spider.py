
import re

from spiders.core_spider.base_spider import BaseSpider
from utils.country_utils import CountryCodeUtils
import logging
from datetime import datetime
from lxml import etree
from utils.http_utils.http_client import HTTPClient


# ultipro的模板方法
class KellyocgSpider(BaseSpider):
    def __init__(self, company_name, base_url, company_url, cwiq_code):
        super().__init__(company_name, base_url, company_url, cwiq_code)
        self.company_name = company_name
        self.company_url = company_url
        self.cwiq_code = cwiq_code
        self.country_utils = CountryCodeUtils()
        self.headers = {
    'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
    'accept-language': 'zh-CN,zh;q=0.9',
    'cache-control': 'max-age=0',
    'content-type': 'application/x-www-form-urlencoded',
    'origin': 'null',
    'priority': 'u=0, i',
    'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"',
    'sec-ch-ua-mobile': '?0',
    'sec-ch-ua-platform': '"Windows"',
    'sec-fetch-dest': 'document',
    'sec-fetch-mode': 'navigate',
    'sec-fetch-site': 'same-origin',
    'sec-fetch-user': '?1',
    'upgrade-insecure-requests': '1',
    'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36',
    # 'cookie': '.AspNetCore.Antiforgery.9fXoN5jHCXs=CfDJ8I65xgDLxE1OlFMI2KJKxRbGqem80x_K6gDc8Ap4nB7iy9ZUPtK_tuXhzILgB0AtOCGuc5T2ogpUbSMhD1OcNj5qJlQad3BwbLD3FvaHl60g7-EHp4W-71anrs1JR9rnmOl3uFVuW9OjybjkeV0O4bk; fabjsession=ced6b123-7725-4cfc-8903-928ff0ebc2e6; ARRAffinity=da8e969327b21417923a577db2d7576a0ce664bdbd56e1463e1c348552969df2; ARRAffinitySameSite=da8e969327b21417923a577db2d7576a0ce664bdbd56e1463e1c348552969df2; CookieConsent={stamp:%2769DSQg4a8/dAwmN/X+BQ4oLXShr6UHT6uiEmyN8VBz+RWDTKKcA9kA==%27%2Cnecessary:true%2Cpreferences:true%2Cstatistics:true%2Cmarketing:true%2Cmethod:%27explicit%27%2Cver:2%2Cutc:1747737600178%2Cregion:%27us-06%27}; rxVisitor=1747737601663JNJI1QTFBAQ3SS8ALD8CAKRRB96CKDGK; dtCookie=v_4_srv_5_sn_6L79LBLIUNBSIPMOPTEA72UK1H2U9VPU_perc_100000_ol_0_mul_1_app-3A32d5e834c8655c7e_1; asource=LandingPageDirect; ai_user=113pIO+UYQTfmnMcxLX5Kw|2025-05-20T10:40:04.056Z; ai_session=xhkv/K0D2eYsDwOjdtp93m|1747737604722|1747737605926; rxvt=1747739406030|1747737602428; dtPC=5$537605568_865h-vFSBFJALJUKTRTPSEKIHMMVPUBAAMEPKU-0e0; dtSa=true%7CKU%7C-1%7CPage%3A%20search%7C-%7C1747737701670%7C537605568_865%7Chttps%3A%2F%2Fcareers.kellyocg.com%2Fsearch%7C%7C%7C%7C',
}
        # 创建 HTTP 客户端，使用全局代理配置
        self.client = HTTPClient()

    @classmethod
    def create_spider(cls, company_name, base_url, company_url, cwiq_code):
        """工厂方法创建 MedtronicSpider 实例"""
        return cls(company_name, base_url, company_url, cwiq_code)

    def make_request(self, method, url, company_name, requset_count,**kwargs):
        """发送请求的通用方法"""
        kwargs['headers'] = self.headers
        return self.client.request(method, url, company_name, requset_count,log_ip=False, **kwargs)

    def get_total_jobs(self):
        """获取总职位数"""
        try:
            data = {
                'SearchText': '',
                'SearchCity': '',
                '__RequestVerificationToken': 'CfDJ8I65xgDLxE1OlFMI2KJKxRbyfS08xdSMTOPgxRGxH6He70ielhWtzMxZ_ezpIRrspJA5XbNGKcRr1vKCKSHQmV4XuLSdLro3f2UETXKV3wVpkWmri7sJ8qyN_cZPhd8ufxWtfEd34OuXFpe0bBZHXBw',
                'SessionID': '00000000-0000-0000-0000-000000000000',
            }
            cookies = {
                '.AspNetCore.Antiforgery.9fXoN5jHCXs': 'CfDJ8I65xgDLxE1OlFMI2KJKxRbGqem80x_K6gDc8Ap4nB7iy9ZUPtK_tuXhzILgB0AtOCGuc5T2ogpUbSMhD1OcNj5qJlQad3BwbLD3FvaHl60g7-EHp4W-71anrs1JR9rnmOl3uFVuW9OjybjkeV0O4bk',
                'fabjsession': 'ced6b123-7725-4cfc-8903-928ff0ebc2e6',
                'ARRAffinity': 'da8e969327b21417923a577db2d7576a0ce664bdbd56e1463e1c348552969df2',
                'ARRAffinitySameSite': 'da8e969327b21417923a577db2d7576a0ce664bdbd56e1463e1c348552969df2',
                'CookieConsent': '{stamp:%2769DSQg4a8/dAwmN/X+BQ4oLXShr6UHT6uiEmyN8VBz+RWDTKKcA9kA==%27%2Cnecessary:true%2Cpreferences:true%2Cstatistics:true%2Cmarketing:true%2Cmethod:%27explicit%27%2Cver:2%2Cutc:1747737600178%2Cregion:%27us-06%27}',
                'rxVisitor': '1747737601663JNJI1QTFBAQ3SS8ALD8CAKRRB96CKDGK',
                'dtCookie': 'v_4_srv_5_sn_6L79LBLIUNBSIPMOPTEA72UK1H2U9VPU_perc_100000_ol_0_mul_1_app-3A32d5e834c8655c7e_1',
                'asource': 'LandingPageDirect',
                'ai_user': '113pIO+UYQTfmnMcxLX5Kw|2025-05-20T10:40:04.056Z',
                'ai_session': 'xhkv/K0D2eYsDwOjdtp93m|1747737604722|1747737605926',
                'rxvt': '1747739406030|1747737602428',
                'dtPC': '5$537605568_865h-vFSBFJALJUKTRTPSEKIHMMVPUBAAMEPKU-0e0',
                'dtSa': 'true%7CKU%7C-1%7CPage%3A%20search%7C-%7C1747737701670%7C537605568_865%7Chttps%3A%2F%2Fcareers.kellyocg.com%2Fsearch%7C%7C%7C%7C',
            }
            response = self.make_request('POST', self.base_url, self.company_name,requset_count=3,cookies=cookies, data=data)
            tree = etree.HTML(response.text)
            total = tree.xpath(
                "//div[@class='pg-wrapper pg-search']/div[@class='section section-2 search-filter']/div[@class='section-inner']/span[@class='total-job-found JQNumberOfJobs']//text()")[
                0]
            result = re.findall(r'\d+', total)
            num = int(result[0])
            return num
        except Exception as e:
            logging.error(f"Error getting total jobs: {e} for {self.company_name}")
            return 0

    def parse_job(self, job_data):
        """解析职位数据"""
        pass

    def fetch_jobs(self, max_workers):
        """获取所有职位信息"""
        try:
            total_jobs = self.get_total_jobs()
            if total_jobs == 0:
                logging.info(f"No jobs found {self.company_name}")
                return [], 0

            logging.info(f"Found {total_jobs} total jobs")
            return [], total_jobs
        except Exception as e:
            logging.error(f"Error in fetch_jobs: {e}:{self.company_name}")
            return [], 0

    def __del__(self):
        """确保 HTTP 客户端被正确关闭"""
        if hasattr(self, 'client'):
            self.client.close()
