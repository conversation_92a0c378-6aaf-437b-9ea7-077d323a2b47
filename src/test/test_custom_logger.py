#!/usr/bin/env python3
"""
自定义日志功能测试脚本 - test_custom_logger.py

测试新的日志文件功能：
1. 指定日志文件
2. 追加模式 vs 重新创建模式
3. 文件创建失败时的处理
"""

import os
import sys
import tempfile
import time

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.colored_logger import create_colored_logger


def test_default_logger():
    """测试默认日志记录器"""
    print("\n" + "=" * 60)
    print("测试1: 默认日志记录器")
    print("=" * 60)

    logger = create_colored_logger()

    logger.success("默认日志记录器测试 - 成功信息")
    logger.error("默认日志记录器测试 - IO错误")
    logger.parse_warn("默认日志记录器测试 - 解析错误")
    logger.normal("默认日志记录器测试 - 正常信息")
    logger.info("默认日志记录器测试 - 一般信息")

    print("✅ 默认日志记录器测试完成")


def test_custom_file_new():
    """测试自定义文件 - 重新创建模式"""
    print("\n" + "=" * 60)
    print("测试2: 自定义文件 - 重新创建模式")
    print("=" * 60)

    # 创建临时文件
    temp_file = tempfile.mktemp(suffix='.log', prefix='test_new_')
    print(f"测试文件: {temp_file}")

    # 先写入一些内容
    with open(temp_file, 'w') as f:
        f.write("这是旧的日志内容，应该被覆盖\n")

    print("旧文件内容:")
    with open(temp_file, 'r') as f:
        print(f"  {f.read().strip()}")

    # 创建日志记录器 - 重新创建模式
    logger = create_colored_logger(log_file=temp_file, log_add=False)

    logger.success("新文件模式测试 - 成功信息")
    logger.error("新文件模式测试 - IO错误")
    logger.parse_warn("新文件模式测试 - 解析错误")

    print("\n新文件内容:")
    with open(temp_file, 'r') as f:
        content = f.read()
        for line in content.strip().split('\n'):
            print(f"  {line}")

    # 清理
    os.unlink(temp_file)
    print("✅ 自定义文件重新创建模式测试完成")


def test_custom_file_append():
    """测试自定义文件 - 追加模式"""
    print("\n" + "=" * 60)
    print("测试3: 自定义文件 - 追加模式")
    print("=" * 60)

    # 创建临时文件
    temp_file = tempfile.mktemp(suffix='.log', prefix='test_append_')
    print(f"测试文件: {temp_file}")

    # 先写入一些内容
    with open(temp_file, 'w') as f:
        f.write("这是旧的日志内容，应该被保留\n")

    print("旧文件内容:")
    with open(temp_file, 'r') as f:
        print(f"  {f.read().strip()}")

    # 创建日志记录器 - 追加模式
    logger = create_colored_logger(log_file=temp_file, log_add=True)

    logger.success("追加模式测试 - 成功信息")
    logger.error("追加模式测试 - IO错误")
    logger.parse_warn("追加模式测试 - 解析错误")

    print("\n追加后文件内容:")
    with open(temp_file, 'r') as f:
        content = f.read()
        for line in content.strip().split('\n'):
            print(f"  {line}")

    # 清理
    os.unlink(temp_file)
    print("✅ 自定义文件追加模式测试完成")


def test_file_creation_failure():
    """测试文件创建失败的情况"""
    print("\n" + "=" * 60)
    print("测试4: 文件创建失败处理")
    print("=" * 60)

    # 尝试在不存在的目录中创建文件
    invalid_file = "/nonexistent/directory/test.log"
    print(f"尝试创建无效文件: {invalid_file}")

    print("预期行为: 程序应该输出错误信息并退出")
    print("注意: 这个测试会导致程序退出，所以我们跳过实际执行")
    print("如果要测试，请取消注释下面的代码:")
    print("# logger = create_colored_logger(log_file=invalid_file, log_add=False)")

    print("✅ 文件创建失败测试说明完成")


def test_multiple_loggers():
    """测试多个日志记录器"""
    print("\n" + "=" * 60)
    print("测试5: 多个日志记录器")
    print("=" * 60)

    # 创建多个临时文件
    temp_file1 = tempfile.mktemp(suffix='.log', prefix='test_multi1_')
    temp_file2 = tempfile.mktemp(suffix='.log', prefix='test_multi2_')

    print(f"文件1: {temp_file1}")
    print(f"文件2: {temp_file2}")

    # 创建两个不同的日志记录器
    logger1 = create_colored_logger(log_file=temp_file1, log_add=False)
    logger2 = create_colored_logger(log_file=temp_file2, log_add=False)

    logger1.success("日志记录器1 - 成功信息")
    logger2.error("日志记录器2 - IO错误")
    logger1.parse_warn("日志记录器1 - 解析错误")
    logger2.normal("日志记录器2 - 正常信息")

    print("\n文件1内容:")
    with open(temp_file1, 'r') as f:
        content = f.read()
        for line in content.strip().split('\n'):
            print(f"  {line}")

    print("\n文件2内容:")
    with open(temp_file2, 'r') as f:
        content = f.read()
        for line in content.strip().split('\n'):
            print(f"  {line}")

    # 清理
    os.unlink(temp_file1)
    os.unlink(temp_file2)
    print("✅ 多个日志记录器测试完成")


def main():
    """主函数"""
    print("🎨 Marzaha 爬虫系统 - 自定义日志功能测试")
    print("📋 测试内容:")
    print("   1. 默认日志记录器")
    print("   2. 自定义文件 - 重新创建模式")
    print("   3. 自定义文件 - 追加模式")
    print("   4. 文件创建失败处理")
    print("   5. 多个日志记录器")
    print()

    # 运行所有测试
    test_default_logger()
    test_custom_file_new()
    test_custom_file_append()
    test_file_creation_failure()
    test_multiple_loggers()

    print("\n" + "=" * 60)
    print("✅ 自定义日志功能测试完成")
    print("💡 功能说明:")
    print("   - log_file=None: 使用默认日志文件")
    print("   - log_file='path', log_add=False: 重新创建指定文件")
    print("   - log_file='path', log_add=True: 追加到指定文件")
    print("   - 文件创建失败时程序会退出并显示错误信息")
    print("=" * 60)


if __name__ == "__main__":
    main()
