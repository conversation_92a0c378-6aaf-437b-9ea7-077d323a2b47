import json
import random
import time
from datetime import datetime
import logging
from urllib.parse import urlparse
from concurrent.futures import ThreadPoolExecutor, as_completed
from spiders.core_spider.base_spider import BaseSpider
from utils.country_utils import CountryCodeUtils
from utils.http_utils.http_client import HTTPClient
from bs4 import BeautifulSoup
import re
from lxml import etree
class KornitSpider(BaseSpider):
    def __init__(self, company_name, base_url, company_url, cwiq_code):
        super().__init__(company_name, base_url, company_url, cwiq_code)
        self.company_name = company_name
        self.company_url = company_url
        self.cwiq_code = cwiq_code
        self.country_utils = CountryCodeUtils()
        self.headers = {
    'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
    'accept-language': 'zh-CN,zh;q=0.9',
    'cache-control': 'no-cache',
    'pragma': 'no-cache',
    'priority': 'u=0, i',
    'referer': 'https://careers.kornit.com/cmcareer/28-832/',
    'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"',
    'sec-ch-ua-mobile': '?0',
    'sec-ch-ua-platform': '"Windows"',
    'sec-fetch-dest': 'document',
    'sec-fetch-mode': 'navigate',
    'sec-fetch-site': 'same-origin',
    'sec-fetch-user': '?1',
    'upgrade-insecure-requests': '1',
    'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    # 'cookie': 'visid_incap_3040392=cEGK9CKCS/27nnkw1YKtJwcSNGgAAAAAQUIPAAAAAABPf3H/2Z8xAYrdBoFzTaHy; incap_ses_1830_3040392=/6B9UGm13iMfXUrGmndlGQgSNGgAAAAAlpvUEavWkvH5x7yJTzQvLg==; referrer11_00f=careers.kornit.com; _zitok=93c7f2843bea680150271748242959; incap_ses_124_3040392=z827UhlSWV1ZYMLqwom4AWoUNGgAAAAAfH4AD54IuS9fzk2LdNgtyQ==; ___utmvc=derL5Sr7kpVxig1idWhqDVdl+UzxnaClwiHSVhGvib/n+z0m86fJNmGAhcyAb/68bgFATpDV6cfOnHM3quxrHwGKkp0GgP1NTuYrTjXJWxleINZDyy1XcPUvKm2QzikjNWOVW6ObzNevG7/5DZo9b6vUU8X7xJ8Myn5514DFLDwlJ3ufBdmvyfbqUikS+8K5Jvm5WE66inr64PoSwEFfTGzW5KtgmDlO+O3quPpivi/sQlD+eQ8UcWIO7rYkN50vCgbpiKPUWQ28DVwcVfLrajwWnV58W4VDAc0jDyIpoumXKkqN76B3GrPts/Zzje71jEb7qcVhT8dakTN8pLI92glNBeL9uiNnHIXCTNW1SMh2s/OSQq+yf88ZmeZZez2XxpZXGlR2v67JZQRew8H+W7GVWPEBPqt9pFVF6E0rKF3kbrzJSE0HoRLKpgZTqA4pKnndcvIXti8zudxEJc8LkEkj+VYkyI+5xlL2xJ62Sv3Jt8m1i5oh7v/GitRF1AdXW8d1iw77fsaIgT5xQQkwj4TEBinV0Snbi8bcKOFCCXHvlgXHvZUzLSpRRdXW7Hc2zZ4weI34sSgb8IZRzlNcGD9xAsP3mclq4qCXrIaMPkskKQWS/5YDO2j5tvgeDupWNiLXU/eEbEKr/v95kEV3k39PCE/mko8ForaH5b3JLw4MJS9fFvW/K0c8GCy4p/s4CxHS1+hunq8f38DJpU+jKgETpyTjt/OHNduzpiIWFLf2W30acoJpXggB43jtMXpNRU+7oqoROBF6qMjLDGwzydilWmrrr/I3VNlHYRDhqSEYB7rv+cjo8uhgJV/KhBaHc/cugQ7YvpgKy3e1sBRouLz9WKRsRgbOcXWL4jrLtTxctRa4qrP5DPfw9GQfnZ9dVzKQHgjAWlWjF4C/2/IPpD5ZCjw6yECUtY7xyciMp6bTgJA8zhOjQSMhE05+pbjsLa3H8TterzPq4g66qTmtiXZok+ppp10M5bTVzVqo9jp9k9/MW8d8m11paF+D9oeMQ9lKte/4W2Wsaz0V/v5zN6t5uX7I2a20nI4NoC6tEq3XXx75cqzip6T5h2bI784Vn4KpD49TJqXP7Y27bvXGDTPnM2sSxb3eQUgWt2xVdfA9uMzKujy0F28sdQsmhtxqWdyumiASQwmbt007ZRVwSSZqDfEq6LbHD91alAFyXjiqREOsPAoyxeAMIy4cxajakBa33dAVeUlWwKz2/P6gVmPo3cIAWzQem2lXaCMC8XAwbT53P8HuuoXVnv1/DZfigVyre/ugd4CuauL6GEoGDyB4OssF9rfFWVjXjGUshunLppbcMWEgcDhG8djxIDfvNeQ39QWbZw/6L7nD/0ujVNG7gNGHQ1NA77pttzhu3TjmO13/KOVxF2u+nvQI7Y8HPDkPHR4xW5QMPME7/e/JCrIvI3D0X++VHBmpj9syx7MWZSO1WQCO4abi7FrYQiPBi0RZNIxRdYO/SQYUSFa15J6d/MlN8ry/2gd9MbE1Zn143CXIpN99ylpQUEwz+NTfvcaCFzpjyinihy/TeP+smejMaEfQPjNufmB8npQNZjiXDcbOoEs/oHgTukUtHBcYgltMyAO3XTJ3VvuN/cFVALQ8hYELyKphPJAtrnEwG9axtI+Z+cUeTugYr/x0jC/CnLEDMLbpC4dnLHAZhtQH5GapHqC9vdMeHGc8umqa18n75pc2R6AlwdXhaclDd5paIv0qQZhhb3FtrnWjm7KHFehy8vsi9hRVfzFnTtPxMevkLWeNmnTsiv6tgh8u/vrwQBelyWt1FAA807YvIEPRepync0T6rQKMJ3NRq0CNiQxXSD5bfPNgeUO/iPjYNrGj7bw6dBynYURuzGa2EzOyGD9tkNLTBi03cx8D4q5+gzxbNh5BL65hvOCaf2L9zriXXab1DyHcshN7tDVlP+m7bM7+gYREIDzHaqMz2sp308HxWDfEfWfTOKAc01qyvkABrTAj0MYxJtpRhEuhzacvZ/hudxL3LlwcQcwWu478t/VN4LNMbNasHhQEx7KyR4hVGNpaH8w3M8dJcrGnbEhvV55ZV8jmXjQd8C/tsaXUxsnAW2Hqd5fatrMAgEaKMC17zKvXrZul/fxe8IOIhoGU9TSWQqByi/xxlYhBs0OyV5X1zCQ4Rj64QwLx7xDlZkL/tNJi3FlzVH9K582A/hJ6Ks8rDbIPot/vDO7OdV1Td5DvVx4qYQrTF5+kqjmKL8dYECZWS0IgPGBJqzzvbzihLxzAx8n1x9SRFS3FXYmv/5FIu1ds33nke4WD++ZIDHcZtpcxwusIWYXO2FYxFcGHrJs+VBNNnMMYU5daVFxgrrkARbhOdJuJjziFZ9IlAgbcQ2K63x1TNdcg3+ICWnEgdT/zV/oEo2l6dCvUAUpc9TrTKz7MVaO3013ZhOw+t1VCtLxA6htc5kwFnurhTuPfO/Mv4GvGtdj2G/I2WxWMv7oa35BTNL8UsEg9UTmbjq0yad19jOKHhRr7n2JF0Zu6hskckltxEzv07lgrdan1gGnaf+owH0j0nj/ZptcBOO/lsjEzKkl2iQallV3P7SjG6/r+OytZgQyXKc5uLSYz2tQ3hiv4pMe1z8pinbiEwQeyiSZEJZuiFZqbjJYQuFDYsF2nhYxvMcDrQ5VrMnOp1Za3UIXuQLVPe+6fW/G561GgunnSWz/NQoGWlcVIK7HrWgQtd1v+MBi3zRJdYiw0CspyhftvNbVFMKE5mO48bV5s2Wlf2oaZjNWxvHUVC3ZLfs2M3hENs18/vvGlwaivlJAvk24jU1qkI0QTaCxkaWdlc3Q9MTkyMjQyLDE5MjA2NyxzPTg2OWY5Yjg5Nzc5ZjljN2NhNjg1YWE4YTk5ODY4MWEyYTU2NDY0NWJhOGFmOWI3NjZmNmU3Y2ExOTU4MDllNzY5YmE1ODk3NjgzOWQ3NDZl',
}

        # 创建 HTTP 客户端
        self.client = HTTPClient()

    @classmethod
    def create_spider(cls, company_name, base_url, company_url, cwiq_code):
        """工厂方法创建 MedtronicSpider 实例"""
        return cls(company_name, base_url, company_url, cwiq_code)

    def make_request(self, method, url, company_name, requset_count, **kwargs):
        """发送请求的通用方法"""
        kwargs['headers'] = self.headers
        return self.client.request(method, url, company_name, requset_count, log_ip=False, **kwargs)

    def get_total_jobs(self):
        """获取总职位数"""
        try:
            response = self.make_request('GET', self.base_url, self.company_name, requset_count=3)
            tree = etree.HTML(response.text)
            total = tree.xpath(
                "//div[@class='row'][2]/div[@class='controller-container col-md-12']/div[@class='sidebar col-lg-2']/div[@class='category hidden-xs ']/a[@class='loc all career-filter active ']//text()")[
                0]
            result = re.findall(r'\d+', total)
            total = int(result[0])
            return total
        except Exception as e:
            logging.error(f"Error getting total jobs: {e} for {self.company_name}")
            return 0

    def parse_job(self, job_data):
        """解析职位数据"""
        pass

    def fetch_jobs(self,max_workers):
        """获取所有职位信息"""
        try:
            total_jobs = self.get_total_jobs()
            if total_jobs == 0:
                logging.info(f"No jobs found {self.company_name}")
                return [], 0

            logging.info(f"Found {total_jobs} total jobs")

            return [], total_jobs

        except Exception as e:
            logging.error(f"Error in fetch_jobs: {e}:{self.company_name}")
            return [], 0


    def __del__(self):
        """确保 HTTP 客户端被正确关闭"""
        if hasattr(self, 'client'):
            self.client.close()

