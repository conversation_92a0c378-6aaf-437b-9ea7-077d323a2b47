import json
import random
import time
from datetime import datetime
import logging
from urllib.parse import urlparse
from concurrent.futures import ThreadPoolExecutor, as_completed
from spiders.core_spider.base_spider import BaseSpider
from utils.country_utils import CountryCodeUtils
from utils.http_utils.http_client import HTTPClient
from bs4 import BeautifulSoup
import re

class SearchOnlyNumSpider(BaseSpider):
    def __init__(self, company_name, base_url, company_url, cwiq_code):
        super().__init__(company_name, base_url, company_url, cwiq_code)
        self.company_name = company_name
        self.company_url = company_url
        self.cwiq_code = cwiq_code
        self.country_utils = CountryCodeUtils()
        self.headers = {
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
            'Accept-Language': 'zh-CN,zh;q=0.9',
            'Connection': 'keep-alive',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'same-origin',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/133.0.0.0 Safari/537.36 Edg/133.0.0.0',
            'sec-ch-ua': '"Not(A:Brand";v="99", "Microsoft Edge";v="133", "Chromium";v="133"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"Windows"',
        }
        self.parsed_url = urlparse(self.base_url)
        self.job_url_base = f"{self.parsed_url.scheme}://{self.parsed_url.netloc}"
        # 创建 HTTP 客户端
        self.client = HTTPClient()

    @classmethod
    def create_spider(cls, company_name, base_url, company_url, cwiq_code):
        """工厂方法创建 MedtronicSpider 实例"""
        return cls(company_name, base_url, company_url, cwiq_code)

    def make_request(self, method, url, company_name, requset_count, **kwargs):
        """发送请求的通用方法"""
        kwargs['headers'] = self.headers
        return self.client.request(method, url, company_name, requset_count, log_ip=False, **kwargs)

    def get_total_jobs(self):
        """获取总职位数"""
        params = {
            'q': '',
            'sortColumn': 'referencedate',
            'sortDirection': 'desc',
            'startrow': 0,
        }
        try:
            response = self.make_request('GET', self.base_url, self.company_name, requset_count=3, params=params)
            # 解析 HTML
            soup = BeautifulSoup(response.text, 'html.parser')

            if self.base_url == "https://jobs.nscorp.com/search" or self.base_url == "https://jobs.ferrari.com/search":
                span_tag = soup.find('span', id='tile-search-results-label')
                text = span_tag.get_text(strip=True)
                m = re.search(r'of\s+(\d+)\s+Jobs', text)
                total_jobs = int(m.group(1)) if m else 0
            elif self.base_url == "https://careers.evoqua.com/search":
                span_tag = soup.find('span', id='tile-search-results-label')
                text = span_tag.get_text(strip=True)
                m = re.search(r'\s+(\d+)\s+Job', text)
                total_jobs = int(m.group(1)) if m else 0
            elif self.base_url == "https://careers.wecenergygroup.com/search":
                span_tag = soup.find('span', id='tile-search-results-label')
                text = span_tag.get_text(strip=True)
                m = re.search(r'of\s+(\d+)\s+jobs', text)
                total_jobs = int(m.group(1)) if m else 0
            elif self.base_url == "https://carrieres.sysco.fr/search":
                span_tag = soup.find('span', id='tile-search-results-label')
                text = span_tag.get_text(strip=True)
                m = re.search(r'parmi\s+(\d+)', text)
                total_jobs = int(m.group(1)) if m else 0
            else:
                # 查找包含数据的 <span> 标签
                span_tag = soup.find('span', class_='paginationLabel')

                b_tag = span_tag.find_all('b')[1]
                total_jobs = int(b_tag.text)
            return total_jobs
        except Exception as e:
            logging.error(f"Error getting total jobs: {e} for {self.company_name}")
            return 0

    def parse_job(self, job_data):
        """解析职位数据"""
        pass

    def fetch_jobs(self,max_workers):
        """获取所有职位信息"""
        try:
            total_jobs = self.get_total_jobs()
            if total_jobs == 0:
                logging.info(f"No jobs found {self.company_name}")
                return [], 0

            logging.info(f"Found {total_jobs} total jobs")
            return [], total_jobs
        except Exception as e:
            logging.error(f"Error in fetch_jobs: {e}:{self.company_name}")
            return [], 0

    def __del__(self):
        """确保 HTTP 客户端被正确关闭"""
        if hasattr(self, 'client'):
            self.client.close()

