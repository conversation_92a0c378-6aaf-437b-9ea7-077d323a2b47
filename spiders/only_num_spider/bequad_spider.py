import json
import random
import time
from datetime import datetime
import logging
from urllib.parse import urlparse
from concurrent.futures import ThreadPoolExecutor, as_completed
from spiders.core_spider.base_spider import BaseSpider
from utils.country_utils import CountryCodeUtils
from utils.http_utils.http_client import HTTPClient
from bs4 import BeautifulSoup
import re
from lxml import etree

class BequadSpider(BaseSpider):
    def __init__(self, company_name, base_url, company_url, cwiq_code):
        super().__init__(company_name, base_url, company_url, cwiq_code)
        self.company_name = company_name
        self.company_url = company_url
        self.cwiq_code = cwiq_code
        self.country_utils = CountryCodeUtils()
        self.headers = {
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
            'Accept-Language': 'zh-CN,zh;q=0.9',
            'Cache-Control': 'no-cache',
            'Connection': 'keep-alive',
            'Pragma': 'no-cache',
            'Referer': 'https://www.bequad.com/job/united-states/senior-solutions-consultant/11056/43339968848',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'same-origin',
            'Sec-Fetch-User': '?1',
            'Upgrade-Insecure-Requests': '1',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36',
            'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"Windows"',
            # 'Cookie': 'CookieConsent=0|1|2|3; CookieBehavior=US|Implied; SearchVisitorId=a195a148-5284-13f1-621d-500d68af618e; SearchSessionId={%22SearchSessionId%22:%229cd8acab-5344-be9f-e644-b134494fab16%22%2C%22ImpressionParentId%22:%22%22%2C%22ViewParentId%22:%22%22%2C%22GoogleSearchRequestId%22:%22%22%2C%22GoogleJobId%22:%22%22%2C%22Created%22:%221746698390412%22}; PersonalizationCookie=[{%22Locations%22:[{%22Path%22:%*********-5332921-5405889-5284756%22%2C%22FacetType%22:4%2C%22GeolocationLatitude%22:34.2077%2C%22GeolocationLongitude%22:-118.51%2C%22LocationName%22:%22Fillmore%252C%2520CA%22}]%2C%22Categories%22:[]%2C%22PersonalizationType%22:0%2C%22DateCreated%22:%222025-05-08T09:59:50.918Z%22%2C%22CustomFacets%22:[]%2C%22TenantId%22:11056%2C%22OnetCode%22:null%2C%22Served%22:true}%2C{%22Locations%22:[{%22Path%22:%*********%22%2C%22FacetType%22:2}]%2C%22Categories%22:[]%2C%22PersonalizationType%22:1%2C%22DateCreated%22:%222025-05-08T10:09:17.572Z%22%2C%22CustomFacets%22:[]%2C%22TenantId%22:11056}]',
        }

        # 创建 HTTP 客户端
        self.client = HTTPClient()

    @classmethod
    def create_spider(cls, company_name, base_url, company_url, cwiq_code):
        """工厂方法创建 MedtronicSpider 实例"""
        return cls(company_name, base_url, company_url, cwiq_code)

    def make_request(self, method, url, company_name,requset_count, **kwargs):
        """发送请求的通用方法"""
        kwargs['headers'] = self.headers
        return self.client.request(method, url, company_name, requset_count,log_ip=False, **kwargs)

    def get_total_jobs(self):
        try:
            response = self.make_request('GET', self.base_url, self.company_name, requset_count=3)
            tree = etree.HTML(response.text)
            total = tree.xpath("//div[@id='search-results-wrap']/div[@class='sr-wrap']/section[@id='search-results']/h1[@class='headline-4']//text()")[0]
            number = ''.join(filter(str.isdigit, total))
            number = int(number)
            return number
        except Exception as e:
            logging.error(f"Error getting total jobs: {e} for {self.company_name}")
            return 0

    def parse_job(self, job_data):
        """解析职位数据"""
        pass

    def fetch_jobs(self,max_workers):
        """获取职位信息"""
        try:
            total_jobs = self.get_total_jobs()
            if total_jobs == 0:
                logging.info(f"No jobs found {self.company_name}")
                return [], 0

            logging.info(f"Found {total_jobs} total jobs")
            return [], total_jobs
        except Exception as e:
            logging.error(f"Error in fetch_jobs: {e}:{self.company_name}")
            return [],0

    def __del__(self):
        """确保 HTTP 客户端被正确关闭"""
        if hasattr(self, 'client'):
            self.client.close()
