import json
import random
import time
from datetime import datetime
import logging
from urllib.parse import urlparse
from concurrent.futures import ThreadPoolExecutor, as_completed
from spiders.core_spider.base_spider import BaseSpider
from utils.country_utils import CountryCodeUtils
from utils.http_utils.http_client import HTTPClient
from bs4 import BeautifulSoup
import re
from lxml import etree
class BigbearSpider(BaseSpider):
    def __init__(self, company_name, base_url, company_url, cwiq_code):
        super().__init__(company_name, base_url, company_url, cwiq_code)
        self.company_name = company_name
        self.company_url = company_url
        self.cwiq_code = cwiq_code
        self.country_utils = CountryCodeUtils()
        self.headers = {
            'accept': '*/*',
            'accept-language': 'zh-CN,zh;q=0.9',
            'priority': 'u=1, i',
            'referer': 'https://careers.bigbear.ai/jobs',
            'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"Windows"',
            'sec-fetch-dest': 'empty',
            'sec-fetch-mode': 'cors',
            'sec-fetch-site': 'same-origin',
            'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            # 'cookie': 'i18n=en-US; searchSource=external; jrasession=b89c49b9-a798-47df-81c5-78f5085caf65; jasession=s%3AdAx_TuRKKADMuY_JTuFadbmFijma3l5x.MRlrC6k9mITudqF20hkE33JT44ORhZFZJ4c3i5eajGE; _janalytics_ses.5734=*; OptanonAlertBoxClosed=2025-05-13T07:00:21.359Z; OptanonConsent=isGpcEnabled=0&datestamp=Tue+May+13+2025+15%3A00%3A21+GMT%2B0800+(%E4%B8%AD%E5%9B%BD%E6%A0%87%E5%87%86%E6%97%B6%E9%97%B4)&version=202405.1.0&browserGpcFlag=0&isIABGlobal=false&hosts=&consentId=75d47653-6215-4dc4-9dea-1c5eee8ee100&interactionCount=1&isAnonUser=1&landingPath=NotLandingPage&groups=C0003%3A1%2CC0001%3A1%2CC0002%3A1%2CC0004%3A1%2CBG7%3A1&intType=1; _janalytics_id.5734=0a9a6de3-052e-439d-b8dd-5844e6932d2a.1747119542.1.1747119636.1747119542.bc0b9c3a-984f-4d61-bb3e-5fa0528f6a01; pixel_consent=%7B%22cookie%22%3A%22pixel_consent%22%2C%22type%22%3A%22cookie_notice%22%2C%22value%22%3Atrue%2C%22timestamp%22%3A%222025-05-13T07%3A00%3A51.657Z%22%7D',
        }
        # 创建 HTTP 客户端
        self.client = HTTPClient()

    @classmethod
    def create_spider(cls, company_name, base_url, company_url, cwiq_code):
        """工厂方法创建 MedtronicSpider 实例"""
        return cls(company_name, base_url, company_url, cwiq_code)

    def make_request(self, method, url, company_name, requset_count, **kwargs):
        """发送请求的通用方法"""
        kwargs['headers'] = self.headers
        return self.client.request(method, url, company_name, requset_count, log_ip=False, **kwargs)

    def get_total_jobs(self):
        """获取总职位数"""
        try:
            cookies = {
                'i18n': 'en-US',
                'searchSource': 'external',
                'jrasession': 'b89c49b9-a798-47df-81c5-78f5085caf65',
                'jasession': 's%3AdAx_TuRKKADMuY_JTuFadbmFijma3l5x.MRlrC6k9mITudqF20hkE33JT44ORhZFZJ4c3i5eajGE',
                '_janalytics_ses.5734': '*',
                'OptanonAlertBoxClosed': '2025-05-13T07:00:21.359Z',
                'OptanonConsent': 'isGpcEnabled=0&datestamp=Tue+May+13+2025+15%3A00%3A21+GMT%2B0800+(%E4%B8%AD%E5%9B%BD%E6%A0%87%E5%87%86%E6%97%B6%E9%97%B4)&version=202405.1.0&browserGpcFlag=0&isIABGlobal=false&hosts=&consentId=75d47653-6215-4dc4-9dea-1c5eee8ee100&interactionCount=1&isAnonUser=1&landingPath=NotLandingPage&groups=C0003%3A1%2CC0001%3A1%2CC0002%3A1%2CC0004%3A1%2CBG7%3A1&intType=1',
                '_janalytics_id.5734': '0a9a6de3-052e-439d-b8dd-5844e6932d2a.1747119542.1.1747119636.1747119542.bc0b9c3a-984f-4d61-bb3e-5fa0528f6a01',
                'pixel_consent': '%7B%22cookie%22%3A%22pixel_consent%22%2C%22type%22%3A%22cookie_notice%22%2C%22value%22%3Atrue%2C%22timestamp%22%3A%222025-05-13T07%3A00%3A51.657Z%22%7D',
            }
            response = self.make_request('GET', self.base_url, self.company_name, requset_count=3, cookies=cookies)
            total = response.json()['totalCount']
            return total
        except Exception as e:
            logging.error(f"Error getting total jobs: {e} for {self.company_name}")
            return 0

    def parse_job(self, job_data):
        """解析职位数据"""
        pass

    def fetch_jobs(self,max_workers):
        """获取所有职位信息"""
        try:
            total_jobs = self.get_total_jobs()
            if total_jobs == 0:
                logging.info(f"No jobs found {self.company_name}")
                return [], 0

            logging.info(f"Found {total_jobs} total jobs")

            return [], total_jobs

        except Exception as e:
            logging.error(f"Error in fetch_jobs: {e}:{self.company_name}")
            return [], 0


    def __del__(self):
        """确保 HTTP 客户端被正确关闭"""
        if hasattr(self, 'client'):
            self.client.close()

