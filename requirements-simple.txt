# Marzaha 爬虫项目 - 简化依赖 (避免编译问题)
# 优先使用纯Python实现的包

# 任务调度
APScheduler==3.10.4

# 数据库 (纯Python实现)
PyMySQL==1.1.0
SQLAlchemy==2.0.23

# 数据处理
pandas==2.1.4
pyarrow==14.0.2

# HTTP请求
requests==2.31.0
urllib3==2.1.0

# 网页解析 (如果lxml安装失败，可以只用html.parser)
beautifulsoup4==4.12.2

# 配置文件
PyYAML==6.0.1

# 地理位置
pycountry==24.6.1
geopy==2.4.1

# 云存储
boto3==1.34.0
google-cloud-storage==2.10.0

# 系统监控
psutil==5.9.6

# 类型支持
typing-extensions==4.8.0
