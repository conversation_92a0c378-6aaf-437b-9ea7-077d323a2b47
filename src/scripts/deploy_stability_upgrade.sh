#!/bin/bash
# Marzaha 爬虫系统稳定性升级部署脚本
# 自动部署稳定性增强功能

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查依赖
check_dependencies() {
    log_info "检查系统依赖..."
    
    # 检查 Python
    if ! command -v python3 &> /dev/null; then
        log_error "Python3 未安装"
        exit 1
    fi
    
    # 检查虚拟环境
    if [ ! -d ".venv" ]; then
        log_warning "虚拟环境不存在，将创建新的虚拟环境"
        make venv
    fi
    
    # 检查必要的 Python 包
    .venv/bin/python -c "import psutil" 2>/dev/null || {
        log_info "安装 psutil..."
        .venv/bin/pip install psutil
    }
    
    .venv/bin/python -c "import yaml" 2>/dev/null || {
        log_info "安装 PyYAML..."
        .venv/bin/pip install PyYAML
    }
    
    log_success "依赖检查完成"
}

# 创建必要的目录
create_directories() {
    log_info "创建必要的目录..."
    
    mkdir -p monitoring/logs
    mkdir -p backups
    mkdir -p scripts
    mkdir -p config
    
    log_success "目录创建完成"
}

# 创建监控配置文件
create_monitor_config() {
    log_info "创建监控配置文件..."
    
    cat > config/monitor.json << 'EOF'
{
  "thresholds": {
    "cpu_percent": 80,
    "memory_percent": 85,
    "disk_percent": 90,
    "load_average": 4.0,
    "spider_success_rate": 80
  },
  "intervals": {
    "check_interval": 60,
    "alert_cooldown": 300,
    "history_retention": 3600
  },
  "alerts": {
    "email_enabled": false,
    "email_smtp": "smtp.gmail.com",
    "email_port": 587,
    "email_user": "",
    "email_password": "",
    "email_recipients": []
  }
}
EOF
    
    log_success "监控配置文件已创建: config/monitor.json"
}

# 创建备份配置文件
create_backup_config() {
    log_info "创建备份配置文件..."
    
    cat > config/backup.yaml << 'EOF'
host: localhost
port: 3306
user: root
password: ""
database: marzaha
backup_dir: /mnt/data1/backups
retention_days: 7
EOF
    
    log_success "备份配置文件已创建: config/backup.yaml"
}

# 创建系统监控脚本
create_system_monitor_script() {
    log_info "创建系统监控脚本..."
    
    cat > scripts/system_monitor.sh << 'EOF'
#!/bin/bash
# 系统监控脚本

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"
PYTHON="$PROJECT_DIR/.venv/bin/python"

cd "$PROJECT_DIR"

# 检查虚拟环境
if [ ! -f "$PYTHON" ]; then
    echo "错误: 虚拟环境不存在"
    exit 1
fi

# 启动监控
echo "启动系统监控..."
nohup "$PYTHON" utils/health_monitor.py --daemon > monitoring/system_monitor.log 2>&1 &

echo "系统监控已启动，PID: $!"
echo "日志文件: monitoring/system_monitor.log"
EOF
    
    chmod +x scripts/system_monitor.sh
    log_success "系统监控脚本已创建: scripts/system_monitor.sh"
}

# 创建自动备份脚本
create_auto_backup_script() {
    log_info "创建自动备份脚本..."
    
    cat > scripts/auto_backup.sh << 'EOF'
#!/bin/bash
# 自动备份脚本

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"
PYTHON="$PROJECT_DIR/.venv/bin/python"

cd "$PROJECT_DIR"

# 检查虚拟环境
if [ ! -f "$PYTHON" ]; then
    echo "错误: 虚拟环境不存在"
    exit 1
fi

# 执行备份
echo "$(date): 开始自动备份..."
"$PYTHON" utils/backup_manager.py backup --type full

# 清理旧备份
"$PYTHON" utils/backup_manager.py cleanup

echo "$(date): 自动备份完成"
EOF
    
    chmod +x scripts/auto_backup.sh
    log_success "自动备份脚本已创建: scripts/auto_backup.sh"
}

# 创建紧急恢复脚本
create_emergency_script() {
    log_info "创建紧急恢复脚本..."
    
    cat > scripts/emergency_recovery.sh << 'EOF'
#!/bin/bash
# 紧急恢复脚本

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"

cd "$PROJECT_DIR"

echo "🚨 紧急恢复模式"
echo "1. 停止所有进程..."
make emergency-stop

echo "2. 清理系统资源..."
make cleanup

echo "3. 检查系统状态..."
make health

echo "4. 重启爬虫系统..."
make run &

echo "✅ 紧急恢复完成"
EOF
    
    chmod +x scripts/emergency_recovery.sh
    log_success "紧急恢复脚本已创建: scripts/emergency_recovery.sh"
}

# 设置定时任务
setup_cron_jobs() {
    log_info "设置定时任务..."
    
    # 获取项目绝对路径
    PROJECT_PATH=$(pwd)
    
    # 创建临时 crontab 文件
    TEMP_CRON=$(mktemp)
    
    # 保存现有的 crontab
    crontab -l 2>/dev/null > "$TEMP_CRON" || true
    
    # 添加新的定时任务（如果不存在）
    if ! grep -q "marzaha.*health" "$TEMP_CRON"; then
        echo "# Marzaha 爬虫系统定时任务" >> "$TEMP_CRON"
        echo "*/30 * * * * cd $PROJECT_PATH && make health >> monitoring/health.log 2>&1" >> "$TEMP_CRON"
        echo "0 2 * * * cd $PROJECT_PATH && ./scripts/auto_backup.sh >> monitoring/backup.log 2>&1" >> "$TEMP_CRON"
        echo "*/5 * * * * cd $PROJECT_PATH && make cleanup >> monitoring/cleanup.log 2>&1" >> "$TEMP_CRON"
        
        # 安装新的 crontab
        crontab "$TEMP_CRON"
        log_success "定时任务已设置"
    else
        log_warning "定时任务已存在，跳过设置"
    fi
    
    # 清理临时文件
    rm -f "$TEMP_CRON"
}

# 测试功能
test_functionality() {
    log_info "测试稳定性功能..."
    
    # 测试健康检查
    log_info "测试健康检查..."
    make health
    
    # 测试备份功能
    log_info "测试备份功能..."
    make backup
    
    # 测试清理功能
    log_info "测试清理功能..."
    make cleanup
    
    log_success "功能测试完成"
}

# 显示使用说明
show_usage() {
    log_info "稳定性升级部署完成！"
    echo ""
    echo "🎯 可用命令："
    echo "  make health          - 系统健康检查"
    echo "  make monitor         - 启动系统监控"
    echo "  make backup          - 创建系统备份"
    echo "  make restore         - 恢复系统备份"
    echo "  make cleanup         - 清理系统资源"
    echo "  make emergency-stop  - 紧急停止所有进程"
    echo "  make full-check      - 完整系统检查"
    echo ""
    echo "📁 重要文件："
    echo "  config/monitor.json           - 监控配置"
    echo "  config/backup.yaml            - 备份配置"
    echo "  scripts/system_monitor.sh     - 系统监控脚本"
    echo "  scripts/auto_backup.sh        - 自动备份脚本"
    echo "  scripts/emergency_recovery.sh - 紧急恢复脚本"
    echo ""
    echo "📊 监控目录："
    echo "  monitoring/logs/              - 监控日志"
    echo "  backups/                      - 备份文件"
    echo ""
    echo "⏰ 定时任务已设置："
    echo "  每30分钟: 健康检查"
    echo "  每天2点: 自动备份"
    echo "  每5分钟: 系统清理"
    echo ""
    echo "🚀 下一步："
    echo "  1. 根据需要修改配置文件"
    echo "  2. 运行 'make monitor' 启动监控"
    echo "  3. 运行 'make health' 检查系统状态"
}

# 主函数
main() {
    log_info "开始部署 Marzaha 爬虫系统稳定性升级..."
    
    # 检查是否在项目根目录
    if [ ! -f "spider.py" ] || [ ! -f "Makefile" ]; then
        log_error "请在 Marzaha 项目根目录运行此脚本"
        exit 1
    fi
    
    # 执行部署步骤
    check_dependencies
    create_directories
    create_monitor_config
    create_backup_config
    create_system_monitor_script
    create_auto_backup_script
    create_emergency_script
    
    # 询问是否设置定时任务
    read -p "是否设置定时任务？(y/n): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        setup_cron_jobs
    fi
    
    # 询问是否运行测试
    read -p "是否运行功能测试？(y/n): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        test_functionality
    fi
    
    show_usage
    log_success "稳定性升级部署完成！"
}

# 运行主函数
main "$@"
