"""
配置加载工具模块 - config_loader.py

直接读取 config/settings.ini 文件，避免导入 settings 模块
"""

import configparser
import os
from pathlib import Path


class ConfigLoader:
    """配置加载器"""

    def __init__(self, config_file=None):
        """
        初始化配置加载器

        Args:
            config_file (str, optional): 配置文件路径，默认为 config/settings.ini
        """
        # 设置所有默认配置
        self._set_default_config()

        if config_file is None:
            # 获取项目根目录下的 config/settings.ini
            current_dir = Path(__file__).parent
            project_root = current_dir.parent  # 从 utils 目录回到 src 目录
            self.config_file = project_root / 'config' / 'settings.ini'
        else:
            self.config_file = Path(config_file)

        # 禁用插值功能，避免日志格式字符串被误解析
        self.config = configparser.ConfigParser(interpolation=None)
        self._load_config()

    def _set_default_config(self):
        """设置所有默认配置值"""
        self.defaults = {
            # Redis 默认配置
            'redis': {
                'broker_url': 'redis://localhost:6379/0',
                'result_backend': 'redis://localhost:6379/1'
            },

            # MySQL 默认配置
            'mysql': {
                'host': 'localhost',
                'user': 'root',
                'password': '123456',
                'database': 'crawler_leo',
                'port': 3306
            },

            # 日志默认配置
            'logger': {
                'console_level': 'INFO',
                'default_log_file': '/mnt/data1/leo/spider_colored.log',  # 默认写入文件
                'log_format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
                'date_format': '%Y-%m-%d %H:%M:%S',
                'default_log_add': False,
                'level_info': 10,
                'level_hint': 20,
                'level_warn': 30,
                'level_error': 40,
                'level_success': 25
            },

            # 系统默认配置
            'system': {
                'debug': False,
                'max_workers': 10,
                'timeout': 30,
                'retry_times': 3
            },

            # 爬虫默认配置
            'spider': {
                'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                'request_delay': 1,
                'max_retries': 3,
                'timeout': 30
            },

            # 路径默认配置
            'paths': {
                'data_dir': '/mnt/data1/leo',
                'log_dir': '/mnt/data1/leo/logs',
                'backup_dir': '/mnt/data1/leo/backups',
                'temp_dir': '/tmp/spider_temp'
            }
        }

    def _load_config(self):
        """加载配置文件，如果文件不存在或加载失败，使用默认配置"""
        if not self.config_file.exists():
            print(f"⚠️  配置文件不存在: {self.config_file}")
            print("💡 使用默认配置，可复制 config/settings.ini.example 为 config/settings.ini 进行自定义")
            return

        try:
            self.config.read(self.config_file, encoding='utf-8')
            print(f"✅ 配置文件加载成功: {self.config_file}")
        except Exception as e:
            print(f"❌ 配置文件加载失败: {e}")
            print("💡 使用默认配置")

    def get_bool(self, section, key, fallback=None):
        """获取布尔值配置，优先使用 settings.ini，否则使用默认值"""
        if fallback is None:
            fallback = self.defaults.get(section, {}).get(key, False)

        try:
            return self.config.getboolean(section, key)
        except (configparser.NoSectionError, configparser.NoOptionError, ValueError):
            return fallback

    def get_int(self, section, key, fallback=None):
        """获取整数配置，优先使用 settings.ini，否则使用默认值"""
        if fallback is None:
            fallback = self.defaults.get(section, {}).get(key, 0)

        try:
            return self.config.getint(section, key)
        except (configparser.NoSectionError, configparser.NoOptionError, ValueError):
            return fallback

    def get_str(self, section, key, fallback=None):
        """获取字符串配置，优先使用 settings.ini，否则使用默认值"""
        if fallback is None:
            fallback = self.defaults.get(section, {}).get(key, '')

        try:
            value = self.config.get(section, key)
            # 处理特殊值 'None' 字符串，转换为 Python None
            if value.lower() == 'none':
                return None
            return value
        except (configparser.NoSectionError, configparser.NoOptionError):
            return fallback

    def get_redis_config(self):
        """获取 Redis 配置"""
        return {
            'broker_url': self.get_str('redis', 'broker_url'),
            'result_backend': self.get_str('redis', 'result_backend')
        }

    def get_mysql_config(self):
        """获取 MySQL 配置"""
        return {
            'host': self.get_str('mysql', 'host'),
            'user': self.get_str('mysql', 'user'),
            'password': self.get_str('mysql', 'password'),
            'database': self.get_str('mysql', 'database'),
            'port': self.get_int('mysql', 'port')
        }

    def get_logger_config(self):
        """获取日志配置"""
        return {
            'CONSOLE_LEVEL': self.get_str('logger', 'console_level'),
            'DEFAULT_LOG_FILE': self.get_str('logger', 'default_log_file'),
            'LOG_FORMAT': self.get_str('logger', 'log_format'),
            'DATE_FORMAT': self.get_str('logger', 'date_format'),
            'DEFAULT_LOG_ADD': self.get_bool('logger', 'default_log_add'),
            'CUSTOM_LEVELS': {
                'INFO': self.get_int('logger', 'level_info'),
                'HINT': self.get_int('logger', 'level_hint'),
                'WARN': self.get_int('logger', 'level_warn'),
                'ERROR': self.get_int('logger', 'level_error'),
                'SUCCESS': self.get_int('logger', 'level_success'),
            },
            'LEVEL_DESCRIPTION': {
                'INFO': 'INFO - 一般信息（白色）',
                'HINT': 'HINT - 正常输出（蓝色）',
                'WARN': 'WARN - 解析错误（黄色）',
                'ERROR': 'ERROR - IO错误（红色）',
                'SUCCESS': 'SUCCESS - 成功信息（绿色，总是显示）'
            },
            'FILE_LOGGING_NOTE': '所有级别的日志都会写入文件，控制台根据 CONSOLE_LEVEL 过滤显示'
        }

    def get_system_config(self):
        """获取系统配置"""
        return {
            'DEBUG': self.get_bool('system', 'debug'),
            'MAX_WORKERS': self.get_int('system', 'max_workers'),
            'TIMEOUT': self.get_int('system', 'timeout'),
            'RETRY_TIMES': self.get_int('system', 'retry_times')
        }

    def get_spider_config(self):
        """获取爬虫配置"""
        return {
            'USER_AGENT': self.get_str('spider', 'user_agent'),
            'REQUEST_DELAY': self.get_int('spider', 'request_delay'),
            'MAX_RETRIES': self.get_int('spider', 'max_retries'),
            'TIMEOUT': self.get_int('spider', 'timeout')
        }

    def get_paths_config(self):
        """获取路径配置"""
        return {
            'DATA_DIR': self.get_str('paths', 'data_dir'),
            'LOG_DIR': self.get_str('paths', 'log_dir'),
            'BACKUP_DIR': self.get_str('paths', 'backup_dir'),
            'TEMP_DIR': self.get_str('paths', 'temp_dir')
        }

    def get_all_config(self):
        """获取所有配置"""
        return {
            'redis': self.get_redis_config(),
            'mysql': self.get_mysql_config(),
            'logger': self.get_logger_config(),
            'system': self.get_system_config(),
            'spider': self.get_spider_config(),
            'paths': self.get_paths_config()
        }


# 全局配置加载器实例
_config_loader = None


def get_config_loader():
    """获取全局配置加载器实例"""
    global _config_loader
    if _config_loader is None:
        _config_loader = ConfigLoader()
    return _config_loader


def get_mysql_config():
    """快捷方法：获取 MySQL 配置"""
    return get_config_loader().get_mysql_config()


def get_logger_config():
    """快捷方法：获取日志配置"""
    return get_config_loader().get_logger_config()


def get_redis_config():
    """快捷方法：获取 Redis 配置"""
    return get_config_loader().get_redis_config()


def get_system_config():
    """快捷方法：获取系统配置"""
    return get_config_loader().get_system_config()


def get_spider_config():
    """快捷方法：获取爬虫配置"""
    return get_config_loader().get_spider_config()


def get_paths_config():
    """快捷方法：获取路径配置"""
    return get_config_loader().get_paths_config()


# 测试函数
if __name__ == "__main__":
    print("🔧 测试配置加载器")
    print("=" * 50)

    loader = ConfigLoader()

    print("MySQL 配置:")
    mysql_config = loader.get_mysql_config()
    for key, value in mysql_config.items():
        if key == 'password':
            print(f"  {key}: {'*' * len(str(value))}")
        else:
            print(f"  {key}: {value}")

    print("\n日志配置:")
    logger_config = loader.get_logger_config()
    print(f"  控制台级别: {logger_config['CONSOLE_LEVEL']}")
    print(f"  日志文件: {logger_config['DEFAULT_LOG_FILE']}")
    print(f"  自定义级别: {logger_config['CUSTOM_LEVELS']}")

    print("\n✅ 配置加载器测试完成")
