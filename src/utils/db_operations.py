# utils/db_operations.py
import json
import os
import random
from datetime import datetime, timedelta,time
import pandas as pd

from sqlalchemy import and_, func, text, exists
from sqlalchemy.orm import aliased

from utils.mysql_handler import MySQLHandler
from models.data_models import <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>
from utils.colored_logger import log_error, log_info

class DBOperations:
    def __init__(self):
        self.db = MySQLHandler()

    def get_latest_batch(self, company_url):
        """返回完整 Batch 实例，但在 session 关闭前做 detach"""
        with self.db.get_session() as session:
            batch = session.query(Batch).filter_by(
                companyUrl=company_url
            ).order_by(Batch.batchId.desc()).first()

            if batch:
                session.expunge(batch)  # 把对象从 Session 分离
            return batch  # 安全返回 ORM 实例

    def get_total_jobs_by_batch_id(self, batch_id,company_url):
        with self.db.get_session() as session:
            batch = session.query(Batch).filter_by(
                batchId=batch_id,
                companyUrl= company_url
            ).first()

            if batch:
                return batch.total_jobs
            else:
                return 0

    def get_job_ids_by_batch(self, company_url, batch_id, status=None):
        """获取指定批次的 jobId 列表"""
        with self.db.get_session() as session:
            query = session.query(Job.jobId).filter(
                Job.companyUrl == company_url,
                Job.batchId == batch_id
            )
            if status is not None:
                if isinstance(status, (list, tuple)):
                    query = query.filter(Job.jobStatus.in_(status))
                else:
                    query = query.filter(Job.jobStatus == status)
            return [job_id for job_id, in query.all()]

    def get_jobs_by_ids(self, job_ids, batch_size=2000):
        """
        分批获取最新有效 job 信息（每批最多 batch_size 个 job_id）
        """

        def chunk_list(data, size):
            for i in range(0, len(data), size):
                yield data[i:i + size]

        result = {}

        for batch in chunk_list(job_ids, batch_size):
            with self.db.get_session() as session:
                JobAlias = aliased(Job)

                query = session.query(Job.jobId).filter(
                    Job.jobId.in_(batch),
                    Job.jobStatus.in_([0, 1]),
                    exists().where(
                        and_(
                            Job.jobId == JobAlias.jobId,
                            Job.batchId == session.query(func.max(JobAlias.batchId)).filter(
                                JobAlias.jobId == Job.jobId,
                                JobAlias.jobStatus.in_([0, 1])
                            ).scalar_subquery()
                        )
                    )
                )

                jobs = query.all()

                for job in jobs:
                    result[job.jobId] = {
                        "jobId": job.jobId,
                        "company": "",
                        "companyUrl": "",
                        "country": "",
                        "city": "",
                        "jobTitle": "",
                        "department": "",
                        "location": "",
                        "jobDescription": "",
                        "jobUrl": "",
                        "created_time": None,
                        "updated_time": None,
                        "post_time": datetime.now()
                    }

        return result

    def save_new_jobs(self, jobs_data):
        """批量保存新 job"""
        if not jobs_data:
            return False
        try:
            with self.db.get_session() as session:
                session.bulk_insert_mappings(Job, [{**job, "jobStatus": 1} for job in jobs_data])
            return True
        except Exception as e:
            log_error(f"Error saving new jobs: {str(e)}")
            return False

    def save_new_active_jobs(self, active_jobs_data):
        """保存 active jobs"""
        if not active_jobs_data:
            return False
        try:
            with self.db.get_session() as session:
                session.bulk_insert_mappings(ActiveJob, active_jobs_data)
            return True
        except Exception as e:
            log_error(f"Error saving active jobs: {str(e)}")
            return False

    def delete_inactive_jobs(self, job_ids, company_url):
        """删除不活跃 jobs"""
        if not job_ids or not company_url:
            return False
        try:
            with self.db.get_session() as session:
                session.query(ActiveJob).filter(
                    ActiveJob.jobId.in_(job_ids),
                    ActiveJob.companyUrl == company_url
                ).delete(synchronize_session=False)
            return True
        except Exception as e:
            log_error(f"Error deleting inactive jobs: {str(e)}")
            return False

    def process_rep_del_jobs(self, company_url, batch_id, cwiq_code, scrape_time, scrape_date, jobIdGroupOld,
                             jobIdGroupRep):
        """处理重复和删除的工作数据（安全版）"""
        try:
            with self.db.get_session() as session:
                del_jobs = session.query(
                    Job.jobId,
                    Job.company,
                    Job.companyUrl
                ).filter(
                    and_(
                        Job.jobId.in_(jobIdGroupOld),
                        Job.companyUrl == company_url,
                        Job.batchId == batch_id
                    )
                ).all()

                del_jobs_data = []
                for job in del_jobs:
                    active_time = session.query(Job.post_time).filter(
                        and_(
                            Job.jobId == job.jobId,
                            Job.jobStatus == 1
                        )
                    ).order_by(Job.batchId.desc()).first()

                    post_time_value = active_time.post_time if active_time else None

                    new_job_data = {
                        "jobId": job.jobId,
                        "company": job.company,
                        "cwiq_code": cwiq_code,
                        "companyUrl": job.companyUrl,
                        "batchId": batch_id + 1,
                        "jobStatus": -1,
                        "created_time": None,
                        "updated_time": None,
                        "scrape_time": scrape_time,
                        "scrape_date": scrape_date,
                        "jobTitle": "",
                        "department": "",
                        "country": "",
                        "location": "",
                        "jobDescription": "",
                        "jobUrl": "",
                        "city": "",
                        "post_time": post_time_value,
                        "inactive_time": datetime.now(),
                        "jobRepost": 0,
                        "day_jobRepost": 0,
                        "week_jobRepost": 0,
                        "last_batch_repost": 0
                    }
                    del_jobs_data.append(new_job_data)

                if del_jobs_data:
                    session.bulk_insert_mappings(Job, del_jobs_data)

                rep_jobs = session.query(
                    Job.jobId,
                    Job.company,
                    Job.companyUrl
                ).filter(
                    and_(
                        Job.jobId.in_(jobIdGroupRep),
                        Job.companyUrl == company_url,
                        Job.batchId == batch_id,
                    )
                ).all()

                rep_jobs_data = []
                for job in rep_jobs:
                    new_job_data = {
                        "jobId": job.jobId,
                        "company": job.company,
                        "companyUrl": job.companyUrl,
                        "cwiq_code": cwiq_code,
                        "batchId": batch_id + 1,
                        "jobStatus": 0,
                        "created_time": None,
                        "updated_time": None,
                        "scrape_time": scrape_time,
                        "scrape_date": scrape_date,
                        "jobTitle": "",
                        "department": "",
                        "country": "",
                        "location": "",
                        "jobDescription": "",
                        "jobUrl": "",
                        "city": "",
                        "jobRepost": 0,
                        "day_jobRepost": 0,
                        "week_jobRepost": 0,
                        "last_batch_repost": 0
                    }
                    rep_jobs_data.append(new_job_data)

                if rep_jobs_data:
                    session.bulk_insert_mappings(Job, rep_jobs_data)

            return True
        except Exception as e:
            log_error(f"Error processing jobs: {str(e)}")
            return False

    def create_batch_info(self, company, company_url,cwiq_code, batch_id, scrape_time, scrape_date,total_jobs,success_rate):
        """创建新的批次信息"""
        try:
            with self.db.get_session() as session:
                job_count = session.query(Job).filter(
                    and_(
                        Job.company == company,
                        Job.companyUrl == company_url,
                        Job.batchId == batch_id + 1,
                        Job.jobStatus.in_([0, 1])
                    )
                ).count()

                batch_info = Batch(
                    company=company,
                    companyUrl=company_url,
                    cwiq_code=cwiq_code,
                    batchId=batch_id + 1,
                    batchSize=job_count,
                    scrape_time=scrape_time,
                    scrape_date=scrape_date,
                    total_jobs=total_jobs,
                    success_rate=success_rate
                )
                session.add(batch_info)
            return True
        except Exception as e:
            log_error(f"Error creating batch info: {str(e)}")
            return False


    def get_no_jobs(self, company, company_url, batch_id):
        """记录无职位公司"""
        try:
            with self.db.get_session() as session:
                no_job = NoJob(company=company, companyUrl=company_url, batchId=batch_id + 1)
                session.add(no_job)
            return True
        except Exception as e:
            log_error(f"Error saving no jobs: {str(e)}")
            return False

    def is_job_reposted(self, company_url, job_ids, now_time):
        """判断职位是否历史发布过"""
        with self.db.get_session() as session:
            repost_jobs = session.query(HistoryJob.id, HistoryJob.jobId).filter(
                HistoryJob.companyUrl == company_url,
                HistoryJob.jobId.in_(job_ids)
            ).all()

            repost_job_ids = {job.jobId for job in repost_jobs}
            no_repost_job_ids = job_ids - repost_job_ids

            one_week_ago = now_time - timedelta(weeks=1)
            one_day_ago = now_time - timedelta(days=1)

            week_repost_jobs = session.query(HistoryJob.jobId).filter(
                HistoryJob.companyUrl == company_url,
                HistoryJob.jobId.in_(repost_job_ids),
                HistoryJob.post_time >= one_week_ago
            ).all()
            week_repost_job_ids = {job.jobId for job in week_repost_jobs}

            no_week_repost_job_ids = repost_job_ids - week_repost_job_ids

            day_repost_jobs = session.query(HistoryJob.jobId).filter(
                HistoryJob.companyUrl == company_url,
                HistoryJob.jobId.in_(week_repost_job_ids),
                HistoryJob.post_time >= one_day_ago
            ).all()
            day_repost_job_ids = {job.jobId for job in day_repost_jobs}

            no_day_repost_job_ids = week_repost_job_ids - day_repost_job_ids

        return repost_jobs, no_repost_job_ids, no_week_repost_job_ids, day_repost_job_ids, no_day_repost_job_ids

    def update_repost_jobs(self, company_url, job_ids, post_time):
        """更新历史发布的职位上架时间"""
        if not job_ids:
            return False
        try:
            with self.db.get_session() as session:
                session.bulk_update_mappings(
                    HistoryJob,
                    [{"id": id_, "companyUrl": company_url, "jobId": job_id, "post_time": post_time} for id_, job_id in job_ids]
                )
            return True
        except Exception as e:
            log_error(f"Error updating repost jobs: {str(e)}")
            return False

    def save_no_repost_jobs(self, company_url, job_ids, post_time):
        """保存未发布过的职位"""
        if not job_ids:
            return False
        try:
            with self.db.get_session() as session:
                session.bulk_insert_mappings(
                    HistoryJob,
                    [{"companyUrl": company_url, "jobId": job_id, "post_time": post_time} for job_id in job_ids]
                )
            return True
        except Exception as e:
            log_error(f"Error saving no repost jobs: {str(e)}")
            return False

    def fetch_data_sync(self, query: str) -> pd.DataFrame:
        """同步 SQL 查询并返回 DataFrame"""
        try:
            with self.db.engine.connect() as conn:
                return pd.read_sql(query, conn)
        except Exception as e:
            log_error(f"Error fetching data: {str(e)}")
            return pd.DataFrame()

    def getNoJobsParquet(self, start_time):
        """获取 NoJobs 数据并保存为 Parquet 文件"""
        query = """
            SELECT
                nj.company,
                nj.companyUrl AS company_url,
                nj.batchId AS batch_id
            FROM no_jobs nj
            WHERE nj.company IS NOT NULL
        """
        df = self.fetch_data_sync(query)

        if df.empty:
            return {"status": "success", "name": "NoJobsParquet", "message": "no jobs is null"}

        date_str = start_time.strftime('%Y%m%d')
        time_str = start_time.strftime('%H%M')
        over_time_str = (start_time + timedelta(minutes=30)).strftime('%H%M')

        parquet_file = f"{date_str}_{time_str}_{over_time_str}_no_jobs.parquet"
        parent_dir = '/mnt/data1/leo/no_jobs_info'
        os.makedirs(parent_dir, exist_ok=True)
        file_path = os.path.join(parent_dir, parquet_file)

        df.to_parquet(file_path, engine='pyarrow', compression='snappy', index=False)
        return {"status": "success", "name": "NoJobsParquet", "message": "write success"}

    def getStatusParquet(self, start_time):
        """获取 Job Status 数据并保存为 Parquet 文件"""
        query = """
            WITH latest_batches AS (
                SELECT company, MAX(batchId) as latest_batch_id
                FROM batch_tables
                GROUP BY company
            )
            SELECT
                j.scrape_date,
                j.scrape_time,
                j.company,
                j.companyUrl,
                j.cwiq_code,
                j.jobId,
                j.jobStatus,
                j.jobRepost as repost,
                j.week_jobRepost as week_repost,
                j.day_jobRepost as day_repost,
                j.last_batch_repost as last_batch_repost,
                j.post_time as active_time,
                j.inactive_time
            FROM jobs j
            INNER JOIN latest_batches lb
            ON j.company = lb.company AND j.batchId = lb.latest_batch_id
            WHERE j.jobStatus IN (-1, 1)
        """
        df = self.fetch_data_sync(query)

        if df.empty:
            return {"status": "success", "name": "StatusParquet", "message": "status is null"}

        df = df.drop_duplicates(subset=["companyUrl", "jobId"])
        df['scrape_date'] = pd.to_datetime(df['scrape_date']).dt.strftime('%Y/%m/%d')
        df['scrape_time'] = pd.to_timedelta(df['scrape_time']).apply(lambda x: str(x).split(" ")[-1])
        df['scrape_time'] = pd.to_datetime(df['scrape_time'], format='%H:%M:%S').dt.strftime('%H:%M:%S')

        # 获取 success_rate < 95% 的 company 列表
        rate_query = """
            SELECT companyUrl
            FROM batch_tables
            WHERE success_rate < 0.95
        """
        companies_with_low_rate = self.fetch_data_sync(rate_query)
        low_rate_companies = set(companies_with_low_rate['companyUrl'])  # 将公司名存入集合

        # 给原始 DataFrame 增加 success_rate 列，默认值为 0
        df['success_rate'] = 0

        df['success_rate'] = df['success_rate'].astype('object')

        # 如果该公司在 success_rate < 95% 的公司列表中，设置 success_rate 为 -1
        df.loc[df['companyUrl'].isin(low_rate_companies), 'success_rate'] = -1

        date_str = start_time.strftime('%Y%m%d')
        time_str = start_time.strftime('%H%M')
        over_time_str = (start_time + timedelta(minutes=30)).strftime('%H%M')

        parquet_file = f"{date_str}_{time_str}_{over_time_str}_job_status.parquet"
        parent_dir = '/mnt/data1/leo/all_companies_status'
        os.makedirs(parent_dir, exist_ok=True)
        file_path = os.path.join(parent_dir, parquet_file)

        df.to_parquet(file_path, engine='pyarrow', compression='snappy', index=False)
        return {"status": "success", "name": "StatusParquet", "message": "write success"}

    def getInfoParquet(self, start_time):
        """获取 Job Info 数据并保存为 Parquet 文件"""
        query = """
            WITH latest_batches AS (
                SELECT company, MAX(batchId) as latest_batch_id
                FROM batch_tables
                GROUP BY company
            )
            SELECT
                j.company,
                j.companyUrl as company_url,
                j.cwiq_code,
                j.jobId as job_id,
                j.jobTitle as job_title,
                j.country,
                j.location as state,
                j.city,
                j.jobRawData as zip,
                j.jobDescription as job_desc,
                j.jobUrl as job_url,
                j.created_time
            FROM jobs j
            INNER JOIN latest_batches lb
            ON j.company = lb.company AND j.batchId = lb.latest_batch_id
            WHERE j.jobStatus = 1
        """
        df = self.fetch_data_sync(query)

        if df.empty:
            return {"status": "success", "name": "InfoParquet", "message": "JobInfo is null"}

        date_str = start_time.strftime('%Y%m%d')
        time_str = start_time.strftime('%H%M')
        over_time_str = (start_time + timedelta(minutes=30)).strftime('%H%M')

        parquet_file = f"{date_str}_{time_str}_{over_time_str}_job_info.parquet"
        parent_dir = '/mnt/data1/leo/all_companies_info'
        os.makedirs(parent_dir, exist_ok=True)
        file_path = os.path.join(parent_dir, parquet_file)

        df.to_parquet(file_path, engine='pyarrow', compression='snappy', index=False)
        return {"status": "success", "name": "InfoParquet", "message": "write success"}

    def getActiveJobsParquet(self, start_time):
        """获取 Active Jobs 数据并保存为 Parquet 文件"""
        query = """
            SELECT
                aj.id AS id,
                aj.jobId AS job_id,
                aj.jobTitle AS job_title,
                aj.company AS company,
                aj.city AS city,
                aj.jobDescription AS job_description,
                aj.jobUrl AS job_url,
                aj.companyUrl AS company_url,
                aj.activeTime
            FROM active_jobs aj
        """
        df = self.fetch_data_sync(query)

        if df.empty:
            return {"status": "success", "name": "ActiveJobsParquet", "message": "ActiveJobs is null"}

        date_str = start_time.strftime('%Y%m%d')
        time_str = start_time.strftime('%H%M')
        over_time_str = (start_time + timedelta(minutes=30)).strftime('%H%M')

        parquet_file = f"{date_str}_{time_str}_{over_time_str}_active_jobs.parquet"
        parent_dir = '/mnt/data1/leo/active_jobs_info'
        os.makedirs(parent_dir, exist_ok=True)
        file_path = os.path.join(parent_dir, parquet_file)

        df.to_parquet(file_path, engine='pyarrow', compression='snappy', index=False)
        return {"status": "success", "name": "ActiveJobsParquet", "message": "write success"}

    def getMaxBatchInfoParquet(self, start_time):
        """获取 Batch 表中每个公司对应的最大批次，并保存为 Parquet 文件"""
        query = """
            SELECT
                bt.company,
                bt.batchId AS max_batch_id,
                bt.companyUrl AS company_url,
                bt.cwiq_code,
                bt.batchSize,
                bt.total_jobs,
                bt.success_rate
            FROM batch_tables bt
            WHERE bt.batchId = (
                SELECT MAX(batchId)
                FROM batch_tables
                WHERE company = bt.company
            )
        """
        # 执行查询并获取数据
        df = self.fetch_data_sync(query)

        if df.empty:
            return {"status": "success", "name": "MaxBatchInfoParquet", "message": "No data for max batch"}

        # 格式化日期和时间
        date_str = start_time.strftime('%Y%m%d')
        time_str = start_time.strftime('%H%M')
        over_time_str = (start_time + timedelta(minutes=30)).strftime('%H%M')

        # 构建文件名
        parquet_file = f"{date_str}_{time_str}_{over_time_str}_max_batch_info.parquet"
        parent_dir = '/mnt/data1/leo/max_batch_info'
        os.makedirs(parent_dir, exist_ok=True)
        file_path = os.path.join(parent_dir, parquet_file)

        # 保存为 Parquet 文件
        df.to_parquet(file_path, engine='pyarrow', compression='snappy', index=False)

        return {"status": "success", "name": "MaxBatchInfoParquet", "message": "write success"}

    def delete_batch_jobs(self):
        """异步删除 jobs 表中过期批次数据"""
        try:
            delete_sql = text("""
                DELETE j
                FROM jobs j
                JOIN (
                    SELECT companyUrl, MAX(batchId) - 2 AS target_batch_id
                    FROM jobs
                    GROUP BY companyUrl
                    HAVING MAX(batchId) - 2 >= 2
                ) sub ON j.companyUrl = sub.companyUrl AND j.batchId = sub.target_batch_id
            """)
            with self.db.get_session() as session:
                session.execute(delete_sql)
            return {"status": "success", "name": "delete_batch_jobs", "message": "delete success"}
        except Exception as e:
            return {"status": "error", "name": "delete_batch_jobs", "message": str(e)}

    def check_active_data(self):
        """检查 active_jobs 表中重复数据"""
        try:
            query = """
                SELECT id, jobId, company, jobTitle, jobUrl, companyUrl
                FROM active_jobs;
            """
            df = pd.read_sql(query, self.db.engine)
            unique_records = df.drop_duplicates(subset=['jobUrl'], keep='first')
            to_delete = df[~df.index.isin(unique_records.index)]
            with self.db.engine.begin() as conn:
                for _, row in to_delete.iterrows():
                    conn.execute(text("DELETE FROM active_jobs WHERE id = :id"), {"id": row['id']})
            return "delete success", 200
        except Exception as e:
            log_error(f"Error in check_active_data: {str(e)}")
            return f"Error: {str(e)}", 500

    def check_repeat_data(self):
        """检查 jobs 表中同一 jobId 不同 URL 的重复数据"""
        try:
            query = """
                WITH latest_batches AS (
                    SELECT company, MAX(batchId) AS latest_batch_id
                    FROM batch_tables
                    GROUP BY company
                )
                SELECT j.id, j.jobId, j.company, j.jobUrl, j.jobTitle, j.batchId
                FROM jobs j
                JOIN latest_batches lb ON j.company = lb.company AND j.batchId = lb.latest_batch_id
                WHERE j.jobStatus = 1;
            """
            df = pd.read_sql(query, self.db.engine)
            grouped = df.groupby(['jobId', 'company'])['jobUrl'].nunique()
            filtered = grouped[grouped > 1].index
            duplicates = df[df.set_index(['jobId', 'company']).index.isin(filtered)]
            unique_records = duplicates.drop_duplicates(subset=['jobId', 'company', 'jobTitle'])
            to_delete = duplicates[~duplicates.index.isin(unique_records.index)]
            with self.db.engine.begin() as conn:
                for _, row in to_delete.iterrows():
                    conn.execute(text("DELETE FROM jobs WHERE id = :id"), {"id": row['id']})
            return "check success", 200
        except Exception as e:
            log_error(f"Error in check_repeat_data: {str(e)}")
            return f"Error: {str(e)}", 500

    def query_and_append_company_to_json(self, start_time: datetime, json_path: str = './leo/batch_new_company.json'):
        """查询并追加写入公司到 JSON 文件"""
        end_time = start_time + timedelta(minutes=30)
        sql = text("""
            SELECT company
            FROM batch_tables
            WHERE scrape_date = :date_str
              AND scrape_time BETWEEN :start_time AND :end_time
              AND batchId = 1
        """)
        date_str = start_time.strftime('%Y-%m-%d')
        start_time_str = start_time.strftime('%H:%M:%S')
        end_time_str = end_time.strftime('%H:%M:%S')

        with self.db.engine.connect() as conn:
            result = conn.execute(sql, {"date_str": date_str, "start_time": start_time_str, "end_time": end_time_str})
            companies = [row[0] for row in result]

        if os.path.exists(json_path):
            with open(json_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
        else:
            data = {}

        time_tag = start_time.strftime('%Y%m%d_%H%M') + '_' + end_time.strftime('%H%M')
        data[time_tag] = companies

        os.makedirs(os.path.dirname(json_path), exist_ok=True)
        with open(json_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)

        log_info(f"[写入完成] {time_tag} => {len(companies)} companies 写入 {json_path}")

        return {"status": "success", "companies": companies}

    def optimize_jobs_table(self):
        """
        执行 OPTIMIZE TABLE jobs;
        仅在每天 21:00–22:00 之间执行，用于清理物理地址上的空洞页，优化查询效率
        """
        now = datetime.now().time()
        if not (time(21, 0) <= now < time(21, 30)):
            log_info(
                f"Skipping OPTIMIZE TABLE jobs; current time "
                f"{now.strftime('%H:%M:%S')} outside 21:00–22:00 window"
            )
            return

        with self.db.get_session() as session:
            try:
                session.execute(text("OPTIMIZE TABLE jobs;"))
                session.commit()
                log_info("OPTIMIZE TABLE jobs executed successfully")
            except Exception as e:
                log_error(f"Failed to optimize jobs table: {str(e)}")

    def close(self):
        """关闭数据库连接"""
        self.db.close()
