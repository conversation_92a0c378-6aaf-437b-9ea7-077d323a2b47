import json
import random
import time
from datetime import datetime
import logging
from urllib.parse import urlparse
from concurrent.futures import ThreadPoolExecutor, as_completed
from spiders.core_spider.base_spider import BaseSpider
from utils.country_utils import CountryCodeUtils
from utils.http_utils.http_client import HTTPClient
from bs4 import BeautifulSoup
import re
def _simplify_job_data(job):
    """
    将原始职位数据简化为只包含必要信息的格式

    Args:
        job (str): 职位链接

    Returns:
        dict: 简化后的职位数据，只包含ID和路径信息
    """
    try:
        externalPath = job
        id = externalPath.split('/')[-1]
    except Exception as e:
        logging.error(f"Error parsing job data{e}: {job}")
        return {
            'id': None,  # REQ号作为ID
            'externalPath': None
        }
    return {
        'id': id,
        'externalPath': externalPath
    }

class Us242DayforcehcmSpider(BaseSpider):
    def __init__(self, company_name, base_url, company_url, cwiq_code):
        super().__init__(company_name, base_url, company_url, cwiq_code)
        self.company_name = company_name
        self.company_url = company_url
        self.cwiq_code = cwiq_code
        self.country_utils = CountryCodeUtils()
        self.headers = {
            'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
            'accept-language': 'zh-CN,zh;q=0.9',
            'cache-control': 'max-age=0',
            'priority': 'u=0, i',
            'sec-ch-ua': '"Not(A:Brand";v="99", "Microsoft Edge";v="133", "Chromium";v="133"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"Windows"',
            'sec-fetch-dest': 'document',
            'sec-fetch-mode': 'navigate',
            'sec-fetch-site': 'none',
            'sec-fetch-user': '?1',
            'upgrade-insecure-requests': '1',
            'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/133.0.0.0 Safari/537.36 Edg/133.0.0.0',
        }

        self.parsed_url = urlparse(self.base_url)
        self.job_url_base = f"{self.parsed_url.scheme}://{self.parsed_url.netloc}"
        if (self.company_url == "https://globaleur242.dayforcehcm.com/CandidatePortal/en-GB/amphenoluk/Site/ALTD"
                or self.company_url == "https://us242.dayforcehcm.com/CandidatePortal/en-US/thg/Site/ALLCAREERS"
                or self.company_url == "https://us242.dayforcehcm.com/CandidatePortal/en-US/exl/Site/EXLMexico"):
            self.JobBoardXrefCode = self.company_url.split('/')[-1]
        else:
            self.JobBoardXrefCode = "CANDIDATEPORTAL"
        # 创建 HTTP 客户端
        self.client = HTTPClient()

    @classmethod
    def create_spider(cls, company_name, base_url, company_url, cwiq_code):
        """工厂方法创建 MedtronicSpider 实例"""
        return cls(company_name, base_url, company_url, cwiq_code)

    def make_request(self, method, url, company_name, requset_count, **kwargs):
        """发送请求的通用方法"""
        kwargs['headers'] = self.headers
        return self.client.request(method, url, company_name, requset_count, log_ip=False, **kwargs)

    def get_total_jobs(self):
        """获取总职位数"""
        json = {
            'filter': {
                'Query': '',
                'JobBoardXrefCode': self.JobBoardXrefCode,
                'LocationId': '',
                'LocationSearch': '',
            },
        }
        try:
            response = self.make_request('POST', self.base_url, self.company_name, requset_count=3, json=json)
            # 解析 HTML
            soup = BeautifulSoup(response.text, 'html.parser')

            alert_tag = soup.find(role="alert")
            text = alert_tag.text.strip()
            parts = text.split(":")
            if len(parts) > 1:
                number = parts[1].split(".")[0].strip()
            else:
                number = "0"

            return int(number)
        except Exception as e:
            logging.error(f"Error getting total jobs: {e} for {self.company_name}")
            return 0

    def parse_job(self, job_data):
        """解析职位数据"""
        try:
            if job_data:
                # 解析地点信息
                location = job_data.get("jobLocation", [])
                address = location[0].get("address", {}) if location else {}
                country = address.get("addressCountry", "") if address else ""
                country_code = self.country_utils.get_alpha3(country)
                city = address.get("addressLocality", "") if address else ""
                state = address.get("addressRegion", "") if address else ""

                return {
                    "jobId": job_data.get("job_id", ""),
                    "company": self.company_name,
                    "cwiq_code": self.cwiq_code,
                    "companyUrl": self.company_url,
                    "country": country_code,
                    "city": city,
                    "jobTitle": job_data.get("title", ""),
                    # "department": job_info.get("hiringOrganization", {}).get("name", ""),
                    "location": state,
                    "jobDescription": job_data.get("description", ""),
                    "jobUrl": job_data.get("job_url", ""),
                    "created_time": None,
                    "updated_time": None,
                    "post_time": datetime.now(),
                }
        except Exception as e:
            logging.error(f"Error parsing job data: {e} for {self.company_name}")
            return None

    def fetch_job_details(self, external_path):
        """获取职位详细信息"""
        try:
            job_data = {}
            time.sleep(random.uniform(0.5, 1))
            job_url = f"{self.job_url_base}{external_path}"
            response = self.make_request('GET', job_url, self.company_name, requset_count=3)
            # 解析 HTML
            soup = BeautifulSoup(response.text, 'html.parser')

            # 查找包含数据的 <script> 标签
            script_tag = soup.find('script', type='application/ld+json')
            if script_tag:
                json_content = script_tag.text.strip()  # 自动去除<script>标签并保留内部文本
                job_data = json.loads(json_content)
            else:
                parent_div = soup.find('div', class_='posting-title-container')  # 定位父容器
                h1_tag = parent_div.find('h1')  # 在父容器内查找h1标签
                job_data['title'] = h1_tag.text.strip()

                description_div_tags = soup.find_all('div', class_='job-posting-section')
                description = ""
                for description_div_tag in description_div_tags:
                    description += description_div_tag.text.strip()
                job_data['description'] = description

                jobLocation = []
                addressLocality = {}
                address = {}
                span_tag = soup.find('span', class_='job-location')
                location = span_tag.text.strip()
                # 使用分隔符 "●" 拆分地址，并取第一个地址
                first_location = location.split("●")[0].strip()
                # 按照逗号拆分第一个地址
                parts = [p.strip() for p in first_location.split(",")]
                city = ""
                country = ""
                state = ""
                if len(parts) >= 4:
                    city = parts[-3]
                    state_zip = parts[-2].split()
                    state = state_zip[0]
                    country = parts[-1]
                elif len(parts) == 1:
                    city = parts[0]
                elif len(parts) == 2:
                    country = parts[1]
                    if country == "USA":
                        state = parts[0]
                    else:
                        city = parts[0]
                else:
                    city = parts[0]
                    state = parts[1]
                    country = parts[2]
                addressLocality['addressCountry'] = country
                addressLocality['addressLocality'] = city
                addressLocality['addressRegion'] = state
                address['address'] = addressLocality
                jobLocation.append(address)
                job_data['jobLocation'] = jobLocation

            # 添加 job_id 和 job_url
            job_id = job_url.split('/')[-1]
            job_data["job_id"] = job_id
            job_data["job_url"] = job_url

            return job_data
        except Exception as e:
            logging.error(f"Error fetching job details for {self.company_name},{job_url}: {e}")
            return None

    def fetch_jobs(self,max_workers):
        """获取所有职位信息"""
        try:
            total_jobs = self.get_total_jobs()
            if total_jobs == 0:
                logging.info(f"No jobs found {self.company_name}")
                return [],0

            logging.info(f"Found {total_jobs} total jobs")
            # 获取上一批次的职位数据
            latest_batch = self.db_ops.get_latest_batch(self.company_url)
            batch_id = latest_batch.batchId if latest_batch else 0
            previous_job_ids = set(self.db_ops.get_job_ids_by_batch(
                self.company_url,
                batch_id,
                status=[0, 1]
            ))

            all_jobs = self._fetch_job_listings(total_jobs)

            # 从现有职位中提取所有job_ids
            current_job_ids = {
                job.get('id', '')
                for job in all_jobs
            }

            # 找出重复的job_ids
            repeated_job_ids = current_job_ids.intersection(previous_job_ids)
            new_job_ids = current_job_ids - previous_job_ids

            # 一次性获取所有重复职位的数据
            repeated_jobs_dict = self.db_ops.get_jobs_by_ids(list(repeated_job_ids))

            # 分离新职位和重复职位
            new_jobs = [
                job for job in all_jobs
                if job.get('id', '') in new_job_ids
            ]
            repeated_jobs = list(repeated_jobs_dict.values())

            logging.info(f"Found {len(new_jobs)} new jobs and {len(repeated_jobs)} repeated jobs")

            # 只处理新职位的详细信息
            processed_jobs = []
            if new_jobs:
                with ThreadPoolExecutor(max_workers=max_workers) as executor:
                    future_to_job = {
                        executor.submit(self.fetch_job_details, job.get('externalPath', '')): job
                        for job in new_jobs
                    }

                    completed = 0
                    total_new = len(new_jobs)
                    for future in as_completed(future_to_job):
                        job_details = future.result()
                        if job_details:
                            parsed_job = self.parse_job(job_details)
                            if parsed_job:
                                processed_jobs.append(parsed_job)
                        completed += 1
                        progress = (completed / total_new) * 100
                        logging.info(f"Processing new job details: {completed}/{total_new} ({progress:.2f}%)")

            # 合并新处理的职位和重复的职位
            all_processed_jobs = processed_jobs + repeated_jobs
            logging.info(f"Successfully processed {len(all_processed_jobs)} total jobs")
            return all_processed_jobs,total_jobs

        except Exception as e:
            logging.error(f"Error in fetch_jobs: {e}:{self.company_name}")
            return [],0

    def _fetch_job_listings(self, total_jobs):
        """获取所有职位列表"""
        try:
            jobs_per_request = 25
            total_requests = (total_jobs // jobs_per_request) + (1 if total_jobs % jobs_per_request else 0)
            all_jobs = []

            with ThreadPoolExecutor(max_workers=25) as executor:
                futures = []
                for i in range(total_requests):
                    params = {
                        "page": i+1,
                    }
                    json = {
                        'filter': {
                            'Query': '',
                            'JobBoardXrefCode': self.JobBoardXrefCode,
                            'LocationId': '',
                            'LocationSearch': '',
                        },
                    }
                    futures.append(
                        executor.submit(
                            self.make_request,
                            'POST',
                            self.base_url,
                            self.company_name,
                            requset_count=3,
                            params=params,
                            json=json
                        )
                    )

                for future in as_completed(futures):
                    try:
                        response = future.result()
                        jobs = []
                        # 解析 HTML
                        soup = BeautifulSoup(response.text, 'html.parser')

                        # 查找包含数据的 <div> 标签
                        div_tags = soup.find_all('div', class_='posting-title')
                        for div_tag in div_tags:
                            a_tag = div_tag.find('a')  # 查找 <a> 标签
                            externalPath = a_tag['href']
                            jobs.append(externalPath)

                        simplified_jobs = [_simplify_job_data(job) for job in jobs]
                        all_jobs.extend(simplified_jobs)
                        logging.info(f"Progress: {len(all_jobs)}/{total_jobs} jobs fetched")
                    except Exception as e:
                        logging.error(f"Error processing job batch: {e}:{self.company_name}")

            return all_jobs
        except Exception as e:
            logging.error(f"Error fetching jobs: {e}:{self.company_name}")

    def __del__(self):
        """确保 HTTP 客户端被正确关闭"""
        if hasattr(self, 'client'):
            self.client.close()

