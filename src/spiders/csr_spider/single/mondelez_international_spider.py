import re
from urllib.parse import urlparse, urljoin

from spiders.core_spider.base_spider import BaseSpider
from utils.country_utils import CountryCodeUtils
import logging
from concurrent.futures import ThreadPoolExecutor, as_completed
import random
import time
from datetime import datetime

from utils.http_utils.http_client import HTTPClient

def find_label_by_value(data_list, target_value):
    """
    遍历 data_list，每个元素应包含 "locations" 键，
    在其列表中找 value == target_value，返回相应的 label。
    """
    for state in data_list:
        for loc in state.get("locations", []):
            if loc.get("value") == target_value:
                return loc.get("label")
    return None

class MondelezInternationalSpider(BaseSpider):
    def __init__(self, company_name, base_url, company_url, cwiq_code):
        super().__init__(company_name, base_url, company_url, cwiq_code)
        self.company_name = company_name
        self.company_url = company_url
        self.cwiq_code = cwiq_code
        self.country_utils = CountryCodeUtils()

        self.headers = {
            'Accept': '*/*',
            'Accept-Language': 'zh-CN,zh;q=0.9',
            'Connection': 'keep-alive',
            'Sec-Fetch-Dest': 'empty',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Site': 'cross-site',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36 Edg/135.0.0.0',
            'content-type': 'application/x-www-form-urlencoded',
            'sec-ch-ua': '"Microsoft Edge";v="135", "Not-A.Brand";v="8", "Chromium";v="135"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"Windows"',
            'x-algolia-api-key': '********************************',
            'x-algolia-application-id': 'QDCQK4BJ5R',
        }
        self.job_base_url = "https://www.mondelezinternational.com/careers/jobs/job?jobid="

        # 创建 HTTP 客户端，使用全局代理配置
        self.client = HTTPClient()

    @classmethod
    def create_spider(cls, company_name, base_url, company_url, cwiq_code):
        """工厂方法创建 MedtronicSpider 实例"""
        return cls(company_name, base_url, company_url, cwiq_code)

    def make_request(self, method, url,company_name, requset_count, **kwargs):
        """发送请求的通用方法"""
        kwargs['headers'] = self.headers
        return self.client.request(method, url, company_name, requset_count, log_ip=False, **kwargs)

    def get_total_jobs(self):
        """获取总翻页数"""
        data = '{"query":"","page":0,"hitsPerPage":20}'
        try:
            response = self.make_request('POST', self.base_url, self.company_name, requset_count=3, data=data)
            return response.json()['nbHits']
        except Exception as e:
            logging.error(f"Error getting total jobs: {e} for {self.company_name}")
            return 0

    def parse_job(self, job_data):
        """解析职位数据"""
        try:
            if job_data:
                job_id = job_data.get("objectID", "")
                job_url = self.job_base_url + job_id
                return {
                    "jobId": job_id,
                    "company": self.company_name,
                    "cwiq_code": self.cwiq_code,
                    "companyUrl": self.company_url,
                    "country": None,
                    "city": None,
                    "jobTitle": job_data.get("Job_Posting_Title", [])[0],
                    # "department": job_info.get("hiringOrganization", {}).get("name", ""),
                    "location": job_data.get("location", ""),
                    "jobDescription": job_data.get("Job_Posting_Description", [])[0],
                    "jobUrl": job_url,
                    "created_time": None,
                    "updated_time": None,
                    "post_time": datetime.now(),
                }
        except Exception as e:
            logging.error(f"Error parsing job data: {e} for {self.company_name}")
            return None

    def fetch_jobs(self,max_workers):
        """获取所有职位信息"""
        try:
            # 获取工作总数量
            total_jobs = self.get_total_jobs()
            if total_jobs == 0:
                logging.info(f"No jobs found {self.company_name}")
                return [],0

            logging.info(f"Found {total_jobs} total jobs")

            # 获取地点编码对应的地点信息
            data_url = "https://www.mondelezinternational.com/page-data/careers/jobs/page-data.json"
            response = self.make_request('GET', data_url, self.company_name, requset_count=3)
            data_list = response.json()['result']['pageContext']['componentProps'][1]['JobFilter']['countriesList']

            # 遍历每一页
            jobs_per_request = 500
            total_requests = (total_jobs // jobs_per_request) + (1 if total_jobs % jobs_per_request else 0)
            all_jobs = []
            processed_jobs = []

            with ThreadPoolExecutor(max_workers=25) as executor:
                futures = []
                # 提交所有请求任务
                for i in range(total_requests):
                    data = f'{{"query":"","page":{i},"hitsPerPage":{jobs_per_request}}}'
                    futures.append(
                        executor.submit(
                            self.make_request,
                            'POST',
                            self.base_url,
                            self.company_name,
                            requset_count=3,
                            data=data
                        )
                    )

                # 处理所有任务的结果
                for future in as_completed(futures):
                    try:
                        response = future.result()
                        jobs = response.json().get('hits', [])
                        all_jobs.extend(jobs)

                        # 解析每条数据
                        for job in jobs:
                            location = ""
                            loc_list = job.get("Job_Posting_Location_Data")
                            if isinstance(loc_list, list) and loc_list:
                                loc_data = loc_list[0]
                                if isinstance(loc_data, dict):
                                    refs = loc_data.get("Primary_Location_Reference")
                                    if isinstance(refs, list) and refs:
                                        code = refs[0]
                                        loc = find_label_by_value(data_list, code)
                                        if isinstance(loc, str):
                                            location = loc
                            job['location'] = location
                            parsed_job = self.parse_job(job)
                            if parsed_job:
                                processed_jobs.append(parsed_job)

                        # 显示进度
                        if total_jobs > 0:
                            progress = (len(all_jobs) / total_jobs) * 100
                            logging.info(f"Progress: {len(all_jobs)}/{total_jobs} jobs fetched ({progress:.2f}%)")
                    except Exception as e:
                        logging.error(f"Error processing job batch for {self.company_name}: {e}")

            logging.info(f"Successfully fetched {len(processed_jobs)} jobs")
            return processed_jobs,total_jobs

        except Exception as e:
            logging.error(f"Error in fetch_jobs: {e}:{self.company_name}")
            return [],0


    def __del__(self):
        """确保 HTTP 客户端被正确关闭"""
        if hasattr(self, 'client'):
            self.client.close()
