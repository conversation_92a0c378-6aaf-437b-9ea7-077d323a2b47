import json
import random
import time
from datetime import datetime
import logging
from urllib.parse import urlparse
from concurrent.futures import ThreadPoolExecutor, as_completed
from spiders.core_spider.base_spider import BaseSpider
from utils.country_utils import CountryCodeUtils
from utils.http_utils.http_client import HTTPClient
from bs4 import BeautifulSoup
import re
from lxml import etree
class PhfTbeTaleoSpider(BaseSpider):
    def __init__(self, company_name, base_url, company_url, cwiq_code):
        super().__init__(company_name, base_url, company_url, cwiq_code)
        self.company_name = company_name
        self.company_url = company_url
        self.cwiq_code = cwiq_code
        self.country_utils = CountryCodeUtils()
        self.headers = {
    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
    'Accept-Language': 'zh-CN,zh;q=0.9',
    'Cache-Control': 'no-cache',
    'Connection': 'keep-alive',
    'Pragma': 'no-cache',
    'Referer': 'https://phf.tbe.taleo.net/phf01/ats/redirect/careers/viewRequisition.jsp?org=HELIXESG&locale=enUS&cws=1',
    'Sec-Fetch-Dest': 'document',
    'Sec-Fetch-Mode': 'navigate',
    'Sec-Fetch-Site': 'same-origin',
    'Sec-Fetch-User': '?1',
    'Upgrade-Insecure-Requests': '1',
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36',
    'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"',
    'sec-ch-ua-mobile': '?0',
    'sec-ch-ua-platform': '"Windows"',
    # 'Cookie': 'JSESSIONID=F22616ED3137889B9783BC86557CF979; ak_bmsc=C24EB27185F5B13F8699773660671E83~000000000000000000000000000000~YAAQbF7WF0YpY86WAQAAMCUzCxvHJg71/I48Y1fQPFnpnUial4m0zLDT/uu48T/1jSl7Q0qG52KAnTmN36sjDyYYtX9ZFK1Gpqi2B7jphF8YAUZ2NlFn2vzC8UHM6mTgYmYa05B1PE9eRQVRgRZ+6B5P93EP1QN+Zgb7+rwHXy/Td/x02NwdLdbnjyNh/x5G0ocnDyut/a1Bjjj7PIy9xsl82IW/7eGQECWSIVDgTqENpUEzEjS4CAj2No605fz/x1BC6DfMj33nVIm2aC2TrP6jlkZ30SwI64uiT8P+hOr1uxdjnYGvjB7beXtKtWLFejPIhzGdUuBI38ewMeloBsT+H9vgb/ufXBjjsf7YkpvlQ932bkqxuJJ/PijNqzhG; bm_sv=70C58387D81131CCECF60395CF133E79~YAAQbF7WF18pY86WAQAAYCgzCxtMbZClcMSaxBpy+0+jfs5+GjZkiKnI2cbf9nYkORLfLA2HKKFOmKkrjbocsq+uSBIXD/sSxqt3/6RpAJG/Uz2DOhcZo4KncvXSs0Hjhiw+TAnZ3J04cO3EsNT408lhVjq5eyPO4Ihdq0aK0Dmu23tA+7jhGEX5xMNuweSP7cBaUFJwCksCOpum1cAwF3k1DC0PRbKCaOdbM4LFhmSJV8o1GLdrDKCBnW74jkgsI5+O~1',
}
        # 创建 HTTP 客户端
        self.client = HTTPClient()

    @classmethod
    def create_spider(cls, company_name, base_url, company_url, cwiq_code):
        """工厂方法创建 MedtronicSpider 实例"""
        return cls(company_name, base_url, company_url, cwiq_code)

    def make_request(self, method, url, company_name, requset_count, **kwargs):
        """发送请求的通用方法"""
        kwargs['headers'] = self.headers
        return self.client.request(method, url, company_name, requset_count, log_ip=False, **kwargs)

    def get_total_jobs(self):
        """获取总职位数"""
        try:
            params = {
                'org': 'HELIXESG',
                'cws': '40',
            }
            response = self.make_request('GET', self.base_url, self.company_name, requset_count=3,params=params,)
            tree = etree.HTML(response.text)
            total = tree.xpath("//div[@class='panel-body']/span[@class='oracletaleocwsv2-panel-number']//text()")[0]
            return total
        except Exception as e:
            logging.error(f"Error getting total jobs: {e} for {self.company_name}")
            return 0

    def parse_job(self, job_data):
        """解析职位数据"""
        pass

    def fetch_jobs(self,max_workers):
        """获取所有职位信息"""
        try:
            total_jobs = self.get_total_jobs()
            if total_jobs == 0:
                logging.info(f"No jobs found {self.company_name}")
                return [], 0

            logging.info(f"Found {total_jobs} total jobs")

            return [], total_jobs

        except Exception as e:
            logging.error(f"Error in fetch_jobs: {e}:{self.company_name}")
            return [], 0


    def __del__(self):
        """确保 HTTP 客户端被正确关闭"""
        if hasattr(self, 'client'):
            self.client.close()

