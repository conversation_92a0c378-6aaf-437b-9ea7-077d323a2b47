from urllib.parse import urlparse, urljoin

from spiders.core_spider.base_spider import BaseSpider
from utils.country_utils import CountryCodeUtils
import logging
from concurrent.futures import ThreadPoolExecutor, as_completed
import random
import time
from datetime import datetime

from utils.http_utils.http_client import HTTPClient

class VisaSpider(BaseSpider):
    def __init__(self, company_name, base_url, company_url, cwiq_code):
        super().__init__(company_name, base_url, company_url, cwiq_code)
        self.company_name = company_name
        self.company_url = company_url
        self.cwiq_code = cwiq_code
        self.country_utils = CountryCodeUtils()

        self.headers = {
            'accept': 'application/json, text/plain, */*',
            'accept-language': 'zh-CN,zh;q=0.9',
            'content-type': 'application/json',
            'origin': 'https://corporate.visa.com',
            'priority': 'u=1, i',
            'referer': 'https://corporate.visa.com/',
            'sec-ch-ua': '"Not(A:Brand";v="99", "Microsoft Edge";v="133", "Chromium";v="133"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"Windows"',
            'sec-fetch-dest': 'empty',
            'sec-fetch-mode': 'cors',
            'sec-fetch-site': 'same-site',
            'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/133.0.0.0 Safari/537.36 Edg/133.0.0.0',
        }

        # 创建 HTTP 客户端，使用全局代理配置
        self.client = HTTPClient()

    @classmethod
    def create_spider(cls, company_name, base_url, company_url, cwiq_code):
        """工厂方法创建 MedtronicSpider 实例"""
        return cls(company_name, base_url, company_url, cwiq_code)

    def make_request(self, method, url,company_name, requset_count, **kwargs):
        """发送请求的通用方法"""
        kwargs['headers'] = self.headers
        return self.client.request(method, url, company_name, requset_count, log_ip=False, **kwargs)

    def get_total_jobs(self):
        """获取总翻页数"""
        params = {
            'q': '',
        }

        json = {
            'from': 0,
            'size': 10,
        }
        try:
            response = self.make_request('POST', self.base_url, self.company_name, requset_count=3,
                                         params=params, json=json)
            return response.json()['recordsMatched']
        except Exception as e:
            logging.error(f"Error getting total jobs: {e} for {self.company_name}")
            return 0

    def parse_job(self, job_data):
        """解析职位数据"""
        try:
            if job_data:
                description = (job_data.get("jobDescription", "") + job_data.get("qualifications", "") +
                               job_data.get("additionalInformation", ""))
                job_url = job_data.get("applyUrl", "")
                job_url = job_url.split("?")[0]
                # 解析地点信息
                country = job_data.get("country", "")
                country_code = self.country_utils.get_alpha3(country)

                return {
                    "jobId": job_data.get("postingId", ""),
                    "company": self.company_name,
                    "cwiq_code": self.cwiq_code,
                    "companyUrl": self.company_url,
                    "country": country_code,
                    "city": job_data.get("city", ""),
                    "jobTitle": job_data.get("jobTitle", ""),
                    # "department": job_info.get("hiringOrganization", {}).get("name", ""),
                    "location": job_data.get("region", ""),
                    "jobDescription": description,
                    "jobUrl": job_url,
                    "created_time": None,
                    "updated_time": None,
                    "post_time": datetime.now(),
                }
        except Exception as e:
            logging.error(f"Error parsing job data: {e} for {self.company_name}")
            return None

    def get_departments(self):
        """获取部门信息"""
        try:
            url = self.base_url.replace("jobs", "filters")
            response = self.make_request('GET', url, self.company_name, requset_count=3)
            data = response.json()['superDepartments']
            # 创建空列表用于存储部门名称
            departments_list = []

            for item in data:
                depts = item.get("departments", {})
                # 直接将当前字典中的所有键（部门名称）添加到列表中
                departments_list.extend(depts.keys())

            mid = len(departments_list) // 2
            first_departments_list = departments_list[:mid]
            second_departments_list = departments_list[mid:]
            return first_departments_list, second_departments_list
        except Exception as e:
            logging.error(f"Error getting filters ：{self.company_name}: {e}")
            return [], []

    def fetch_jobs(self, max_workers):
        """获取所有职位信息"""
        try:
            # 获取工作总量
            total_jobs = self.get_total_jobs()
            if total_jobs == 0:
                logging.info(f"No jobs found {self.company_name}")
                return [],0

            logging.info(f"Found {total_jobs} total jobs")

            # 获取部门信息
            first_departments_list, second_departments_list = self.get_departments()

            # 遍历每一页
            all_jobs = []
            processed_jobs = []

            params = {
                'q': '',
            }

            json = {
                'filters': [
                    {
                        'department': [],
                    },
                ],
                'from': 0,
                'size': 10,
            }

            # 发送请求
            try:
                json['filters'][0]['department'] = first_departments_list
                response = self.make_request('POST', self.base_url, self.company_name, requset_count=3,
                                             params=params, json=json)
                jobs = response.json()['jobDetails']
                all_jobs.extend(jobs)

                # 解析每条数据
                for job in jobs:
                    parsed_job = self.parse_job(job)
                    if parsed_job:
                        processed_jobs.append(parsed_job)

                # 显示进度
                if total_jobs > 0:  # 避免除以零
                    progress = (len(all_jobs) / total_jobs) * 100
                    logging.info(f"Progress: {len(all_jobs)}/{total_jobs} jobs fetched ({progress:.2f}%)")

                json['filters'][0]['department'] = second_departments_list
                response = self.make_request('POST', self.base_url, self.company_name, requset_count=3,
                                             params=params, json=json)
                jobs = response.json()['jobDetails']
                all_jobs.extend(jobs)

                # 解析每条数据
                for job in jobs:
                    parsed_job = self.parse_job(job)
                    if parsed_job:
                        processed_jobs.append(parsed_job)

                # 显示进度
                if total_jobs > 0:  # 避免除以零
                    progress = (len(all_jobs) / total_jobs) * 100
                    logging.info(f"Progress: {len(all_jobs)}/{total_jobs} jobs fetched ({progress:.2f}%)")

            except Exception as e:
                logging.error(f"Error processing job batch ：{self.company_name}: {e}")

            logging.info(f"Successfully fetched {len(processed_jobs)} jobs")
            return processed_jobs,total_jobs

        except Exception as e:
            logging.error(f"Error in fetch_jobs: {e}:{self.company_name}")
            return [],0

    def __del__(self):
        """确保 HTTP 客户端被正确关闭"""
        if hasattr(self, 'client'):
            self.client.close()
