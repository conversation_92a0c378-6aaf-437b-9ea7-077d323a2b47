import json
import random
import time
from datetime import datetime
import logging
from urllib.parse import urlparse
from concurrent.futures import ThreadPoolExecutor, as_completed
from spiders.core_spider.base_spider import BaseSpider
from utils.country_utils import CountryCodeUtils
from utils.http_utils.http_client import HTTPClient
# from utils.get_fail_length import append_to_csv,append_to_detail_csv
from bs4 import BeautifulSoup
import re


def _simplify_job_data(job_url):
    """
    将原始职位链接简化为只包含必要信息的格式

    Args:
        job_url (str): 职位URL地址

    Returns:
        dict: 简化后的职位数据，只包含ID和路径信息
    """
    try:
        job_id = job_url.split('/')[-1]
        return {
            'id': job_id,
            'externalPath': job_url
        }
    except Exception as e:
        logging.error(f"Error simplifying job data: {e}")


class JobsLeverSpider(BaseSpider):
    def __init__(self, company_name, base_url, company_url, cwiq_code):
        super().__init__(company_name, base_url, company_url, cwiq_code)
        self.company_name = company_name
        self.company_url = company_url
        self.cwiq_code = cwiq_code
        self.country_utils = CountryCodeUtils()
        self.headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
        }
        # 创建 HTTP 客户端
        self.client = HTTPClient()

    @classmethod
    def create_spider(cls, company_name, base_url, company_url, cwiq_code):
        """工厂方法创建 MedtronicSpider 实例"""
        return cls(company_name, base_url, company_url, cwiq_code)

    def make_request(self, method, url, company_name,requset_count, **kwargs):
        """发送请求的通用方法"""
        kwargs['headers'] = self.headers
        return self.client.request(method, url, company_name,requset_count, log_ip=False, **kwargs)

    def parse_job(self, job_data):
        """解析职位数据"""
        # 解析地点信息
        try:
            if job_data:
                location = job_data.get('location', None)
                country_name = location.split(",")[-1]
                country_code = self.country_utils.get_alpha3(country_name)
                city = location.split(",")[0] if location else ""
                state = location.split(",")[1] if location and len(location.split(",")) == 3 else ""

                return {
                    "jobId": job_data.get("job_id", None),
                    "company": self.company_name,
                    "cwiq_code": self.cwiq_code,
                    "companyUrl": self.company_url,
                    "country": country_code,
                    "city": city,
                    "jobTitle": job_data.get("job_title", None),
                    "location": state,
                    "jobDescription": job_data.get("content", None),
                    "jobUrl": job_data.get("job_url", None),
                    "created_time": None,
                    "updated_time": None,
                    "post_time": datetime.now(),
                }
        except Exception as e:
            logging.error(f"Error parsing job data: {e} for {self.company_name}")
            return None

    def fetch_job_details(self, job_url):
        """获取职位详细信息"""
        fail_list = []
        try:
            job_id = job_url.split('/')[-1]
            time.sleep(random.uniform(0.5, 1))
            response = self.make_request('GET', job_url, self.company_name, requset_count=3,)
            if response is None:
                return None

            # Parse the HTML content
            soup = BeautifulSoup(response.text, 'html.parser')

            script_content = None
            script_tag = soup.find('script')
            if script_tag:
                script_content = script_tag.string

            if script_content:
                try:
                    # 尝试解析 JSON 数据
                    script_json = json.loads(script_content)

                    # 提取所需字段
                    title = script_json.get("title", "")
                    job_location = script_json.get("jobLocation", {})

                    if isinstance(job_location, list):
                        # 如果 jobLocation 是列表，取第一个元素
                        first_location = job_location[0]
                        location = first_location.get("address", {}).get("addressLocality", "")
                    elif isinstance(job_location, dict):
                        # 如果 jobLocation 是字典
                        location = job_location.get("address", {}).get("addressLocality", "")
                    description = script_json.get("description", "")

                    job_details = {
                        "job_id": job_id,
                        "job_url": job_url,
                        "job_title": title,
                        "location": location,
                        "content": description
                    }
                    return job_details
                except json.JSONDecodeError:
                    logging.error("无法解析脚本内容为 JSON。")

        except Exception as e:
            logging.error(f"Error fetching job details for {self.company_name}: {e}")
            fail_list.append({
                "method": 'GET',
                "url": job_url,
                "company_name": self.company_name,
            })
            return fail_list

    def _fetch_job_listings(self):
        """获取所有职位列表"""
        failed_requests_list = []
        all_jobs = []
        try:
            response = self.make_request('GET', self.base_url, self.company_name, requset_count=3,)
            response.raise_for_status()
        except Exception as e:
            logging.error(f"请求失败: {e}")
            failed_requests_list.append({
                "method": 'GET',
                "url": self.base_url,
                "company_name": self.company_name,
            })
            return [], failed_requests_list

        try:
            soup = BeautifulSoup(response.text, 'html.parser')
            opening_divs = soup.find_all('div', class_='posting-apply')

            # 提取并简化职位数据

            for div in opening_divs:
                try:
                    job_link = div.find('a')
                    if job_link and job_link.get('href'):
                        job_url = job_link.get('href')
                        simplified_job = _simplify_job_data(job_url)
                        all_jobs.append(simplified_job)
                except Exception as e:
                    logging.error(f"解析单个job链接失败: {e} for {self.company_name}")
                    continue

            return all_jobs, failed_requests_list
        except Exception as e:
            logging.error(f"解析HTML失败: {e} for {self.company_name}")
            return [], failed_requests_list

    def fetch_jobs(self, max_workers):
        """获取所有职位信息"""
        fail_jobs = []
        try:
            # 获取最新批次信息
            latest_batch = self.db_ops.get_latest_batch(self.company_url)
            batch_id = latest_batch.batchId if latest_batch else 0
            previous_job_ids = set(self.db_ops.get_job_ids_by_batch(
                self.company_url,
                batch_id,
                status=[0, 1]
            ))

            # 获取所有职位列表
            all_jobs, failed_requests_list = self._fetch_job_listings()
            all_jobs = all_jobs + fail_jobs

            if not all_jobs:
                logging.info(f"No jobs found {self.company_name}")
                return [],0

            total_jobs = len(all_jobs)
            logging.info(f"Found {total_jobs} total jobs")

            # 从现有职位中提取所有job_ids
            current_job_ids = {job.get('id', '') for job in all_jobs}

            # 找出重复的job_ids
            repeated_job_ids = current_job_ids.intersection(previous_job_ids)
            new_job_ids = current_job_ids - previous_job_ids

            # 一次性获取所有重复职位的数据
            repeated_jobs_dict = self.db_ops.get_jobs_by_ids(list(repeated_job_ids))

            # 分离新职位和重复职位
            new_jobs = [
                job for job in all_jobs
                if job.get('id', '') in new_job_ids
            ]
            repeated_jobs = list(repeated_jobs_dict.values())

            logging.info(f"Found {len(new_jobs)} new jobs and {len(repeated_jobs)} repeated jobs")
            num_job_1 = []
            # 只处理新职位的详细信息
            processed_jobs = []
            if new_jobs:
                with ThreadPoolExecutor(max_workers=max_workers) as executor:
                    future_to_job = {
                        executor.submit(self.fetch_job_details, job.get('externalPath', '')): job
                        for job in new_jobs
                    }

                    completed = 0
                    total_new = len(new_jobs)
                    need_retry = []
                    for future in as_completed(future_to_job):
                        job_details = future.result()
                        if isinstance(job_details, list):
                            need_retry += [
                                (job_details[0]['method'], job_details[0]['url'] )]
                        else:
                            if job_details:
                                parsed_job = self.parse_job(job_details)
                                if parsed_job:
                                    processed_jobs.append(parsed_job)
                            completed += 1
                            progress = (completed / total_new) * 100
                            logging.info(f"Processing new job details: {completed}/{total_new} ({progress:.2f}%)")

                num_job_1 += [len(need_retry)]
                attempt = 0
                retries = 2
                while attempt < retries:
                    attempt += 1
                    if need_retry:
                        time.sleep(60)
                        with ThreadPoolExecutor(max_workers=max_workers) as executor:
                            future_to_job = {
                                executor.submit(self.retry_failed_details_requests, job[0], job[1],retries=1,
                                                delay=0): job
                                for job in need_retry
                            }
                            need_retry = []
                            for future in as_completed(future_to_job):
                                job_details = future.result()
                                if isinstance(job_details, list):
                                    need_retry += [(job_details[0]['method'], job_details[0]['url'])]
                                else:
                                    if job_details:
                                        parsed_job = self.parse_job(job_details)
                                        if parsed_job:
                                            processed_jobs.append(parsed_job)
                                    completed += 1
                                    progress = (completed / total_new) * 100
                                    logging.info(
                                        f"Processing new job details: {completed}/{total_new} ({progress:.2f}%)")
                            num_job_1 += [len(need_retry)]
                logging.info(f"attempt: {attempt}. num_job_1 = {num_job_1}")

            # if num_job_1:
            #     # 计算详情页尝试后的结果
            #     self.get_detail_fail_timeout_length(num_job_1)

            # 合并新处理的职位和重复的职位
            all_processed_jobs = processed_jobs + repeated_jobs
            logging.info(f"Successfully processed {len(all_processed_jobs)} total jobs")
            return all_processed_jobs,"no total_jobs"

        except Exception as e:
            logging.error(f"Error in fetch_jobs:{self.company_name}: {str(e)}")
            return [],0

    # def get_detail_fail_timeout_length(self,num_job_1):
    #     model = 'detail'
    #     if len(num_job_1) == 1:
    #         append_to_detail_csv(self.company_name, num_job_1[0], "NaN", "NaN",  model)
    #     if len(num_job_1) == 2:
    #         append_to_detail_csv(self.company_name, num_job_1[0], num_job_1[1], "NaN",  model)
    #     if len(num_job_1) == 3:
    #         append_to_detail_csv(self.company_name, num_job_1[0], num_job_1[1], num_job_1[2],  model)

    # def get_fail_timeout_length(self, job_details, fail_detail_jobs, fail_detail_two_jobs):
    #     model = 'detail'
    #     timeout_size = len(job_details)
    #     first_success = len(fail_detail_jobs)
    #     second_success = len(fail_detail_two_jobs)
    #     second_timeout_size = timeout_size - first_success
    #     final_timeout_size = second_timeout_size - second_success
    #     append_to_csv(self.company_name, timeout_size, second_timeout_size, final_timeout_size, model)
    #
    # def retry_request(self, method, url, company_name, retries=3, delay=5, **kwargs):
    #     attempt = 0
    #     fail_jobs = []
    #
    #     while attempt < retries:
    #         try:
    #             response = self.make_request(method, url, company_name, **kwargs)
    #             response.raise_for_status()
    #
    #             soup = BeautifulSoup(response.text, 'html.parser')
    #             opening_divs = soup.find_all('div', class_='posting-apply')
    #
    #             # 提取并简化职位数据
    #             for div in opening_divs:
    #                 try:
    #                     job_link = div.find('a')
    #                     if job_link and job_link.get('href'):
    #                         job_url = job_link.get('href')
    #                         simplified_job = _simplify_job_data(job_url)
    #                         fail_jobs.append(simplified_job)
    #                 except Exception as e:
    #                     logging.error(f"解析单个job链接失败: {e}")
    #                     continue
    #
    #             return fail_jobs
    #         except Exception as e:
    #             attempt += 1
    #             logging.error(f"Error on attempt {attempt} for {company_name}: {e}")
    #             if attempt < retries:
    #                 logging.info(f"Retrying in {delay} seconds...")
    #             else:
    #                 logging.error(f"All {retries} attempts failed for {company_name}")
    #                 return None

    def retry_failed_details_requests(self, method, job_url, retries=3, delay=5, **kwargs):
        fail_two_list = []
        try:
            attempt = 0
            job_id = job_url.split('/')[-1]
            while attempt < retries:
                try:
                    response = self.make_request(method, job_url, self.company_name,requset_count=1,)
                    if response is None:
                        return None

                    # Parse the HTML content
                    soup = BeautifulSoup(response.text, 'html.parser')

                    script_content = None
                    script_tag = soup.find('script')
                    if script_tag:
                        script_content = script_tag.string

                    if script_content:
                        try:
                            # 尝试解析 JSON 数据
                            script_json = json.loads(script_content)

                            # 提取所需字段
                            title = script_json.get("title", "")
                            job_location = script_json.get("jobLocation", {})

                            if isinstance(job_location, list):
                                # 如果 jobLocation 是列表，取第一个元素
                                first_location = job_location[0]
                                location = first_location.get("address", {}).get("addressLocality", "")
                            elif isinstance(job_location, dict):
                                # 如果 jobLocation 是字典
                                location = job_location.get("address", {}).get("addressLocality", "")
                            description = script_json.get("description", "")

                            job_details = {
                                "job_id": job_id,
                                "job_url": job_url,
                                "job_title": title,
                                "location": location,
                                "content": description
                            }
                            return job_details
                        except json.JSONDecodeError:
                            logging.error("无法解析脚本内容为 JSON。")

                except Exception as e:
                    attempt += 1
                    logging.error(f"Error on attempt {attempt} for {self.company_name}: {e}")
                    if attempt < retries:
                        logging.info(f"Retrying in {delay} seconds...")
                    else:
                        logging.error(f"All {retries} attempts failed for {self.company_name}")
                        fail_two_list.append({
                            "method": method,
                            "url": job_url,
                            "company_name": self.company_name,
                        })
                        return fail_two_list
        except Exception as e:
            logging.error(f"Error retry_failed_details_requests for {self.company_name}: {e}")

    def __del__(self):
        """确保 HTTP 客户端被正确关闭"""
        if hasattr(self, 'client'):
            self.client.close()
