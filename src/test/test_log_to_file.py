#!/usr/bin/env python3
"""
日志写入文件测试脚本 - test_log_to_file.py

演示如何将所有日志输出写入到指定文件
"""

import os
import sys
import tempfile

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.colored_logger import (
    set_global_log_file, log_success, log_io_error, log_parse_error, 
    log_info, log_normal, log_spider_result, log_exception
)


def test_log_to_new_file():
    """测试写入新文件（重新创建模式）"""
    print("\n" + "=" * 60)
    print("测试1: 写入新文件（重新创建模式）")
    print("=" * 60)
    
    # 创建临时文件
    temp_file = tempfile.mktemp(suffix='.log', prefix='spider_new_')
    print(f"日志文件: {temp_file}")
    
    # 设置全局日志文件 - 重新创建模式
    set_global_log_file(temp_file, log_add=False)
    
    # 写入各种类型的日志
    log_success("爬虫系统启动成功")
    log_normal("开始加载配置文件...")
    log_success("已加载 2131 个爬虫配置")
    log_io_error("数据库连接失败: Connection timeout")
    log_parse_error("JSON解析错误: Invalid format")
    log_normal("正在重试连接...")
    log_success("数据库连接恢复")
    
    # 测试爬虫结果日志
    log_spider_result("Microsoft", "success")
    log_spider_result("Google", "error", "database connection failed")
    log_spider_result("Amazon", "error", "html parse error")
    
    # 测试异常日志
    try:
        import json
        json.loads("invalid json")
    except json.JSONDecodeError as e:
        log_exception("JSON parsing failed", e)
    
    print("\n日志文件内容:")
    with open(temp_file, 'r', encoding='utf-8') as f:
        content = f.read()
        for i, line in enumerate(content.strip().split('\n'), 1):
            print(f"  {i:2d}: {line}")
    
    # 清理
    os.unlink(temp_file)
    print("✅ 新文件模式测试完成")


def test_log_to_append_file():
    """测试追加到文件"""
    print("\n" + "=" * 60)
    print("测试2: 追加到文件")
    print("=" * 60)
    
    # 创建临时文件并写入初始内容
    temp_file = tempfile.mktemp(suffix='.log', prefix='spider_append_')
    print(f"日志文件: {temp_file}")
    
    with open(temp_file, 'w', encoding='utf-8') as f:
        f.write("2025-01-01 00:00:00 - 这是旧的日志内容\n")
        f.write("2025-01-01 00:01:00 - 系统之前的运行记录\n")
    
    print("原有文件内容:")
    with open(temp_file, 'r', encoding='utf-8') as f:
        for i, line in enumerate(f.readlines(), 1):
            print(f"  {i:2d}: {line.strip()}")
    
    # 设置全局日志文件 - 追加模式
    set_global_log_file(temp_file, log_add=True)
    
    # 写入新的日志
    log_normal("系统重新启动")
    log_success("配置重新加载完成")
    log_io_error("发现新的错误")
    log_parse_error("页面结构发生变化")
    
    print("\n追加后文件内容:")
    with open(temp_file, 'r', encoding='utf-8') as f:
        for i, line in enumerate(f.readlines(), 1):
            print(f"  {i:2d}: {line.strip()}")
    
    # 清理
    os.unlink(temp_file)
    print("✅ 追加模式测试完成")


def test_log_file_failure():
    """测试文件创建失败的情况"""
    print("\n" + "=" * 60)
    print("测试3: 文件创建失败处理")
    print("=" * 60)
    
    # 尝试在不存在的目录中创建文件
    invalid_file = "/nonexistent/directory/spider.log"
    print(f"尝试创建无效文件: {invalid_file}")
    
    print("预期行为: 程序应该输出错误信息并退出")
    print("注意: 这个测试会导致程序退出，所以我们跳过实际执行")
    print("如果要测试，请取消注释下面的代码:")
    print("# set_global_log_file(invalid_file, log_add=False)")
    
    print("✅ 文件创建失败测试说明完成")


def test_real_world_scenario():
    """测试真实世界场景"""
    print("\n" + "=" * 60)
    print("测试4: 真实世界场景")
    print("=" * 60)
    
    # 创建临时文件
    temp_file = tempfile.mktemp(suffix='.log', prefix='spider_real_')
    print(f"日志文件: {temp_file}")
    
    # 设置日志文件
    set_global_log_file(temp_file, log_add=False)
    
    # 模拟真实的爬虫执行过程
    log_normal("Starting Marzaha spider system...")
    log_success("Loaded 2131 spider configurations")
    log_normal("Processing spiders: [████████████████████████████████] 2131/2131 (100.0%)")
    
    # 模拟一些错误和成功
    log_spider_result("Microsoft", "success")
    log_spider_result("Google", "success") 
    log_spider_result("Amazon", "error", "database connection failed")
    log_spider_result("Tesla", "error", "html parse error")
    log_spider_result("Apple", "timeout", "request timeout after 30s")
    
    # 模拟数据库操作
    log_success("Database optimization completed")
    log_success("Parquet files generated successfully")
    log_success("All processing completed in 3600.5 seconds")
    log_success("Success rate: 85.6% (1,825/2,131)")
    
    print(f"\n生成的日志文件大小: {os.path.getsize(temp_file)} 字节")
    print("日志文件内容（前10行）:")
    with open(temp_file, 'r', encoding='utf-8') as f:
        lines = f.readlines()
        for i, line in enumerate(lines[:10], 1):
            print(f"  {i:2d}: {line.strip()}")
        if len(lines) > 10:
            print(f"  ... 还有 {len(lines) - 10} 行")
    
    # 清理
    os.unlink(temp_file)
    print("✅ 真实世界场景测试完成")


def main():
    """主函数"""
    print("📝 Marzaha 爬虫系统 - 日志写入文件测试")
    print("📋 功能说明:")
    print("   - set_global_log_file(file_path, log_add=False): 重新创建文件")
    print("   - set_global_log_file(file_path, log_add=True): 追加到文件")
    print("   - 文件创建失败时程序会退出并显示错误信息")
    print("   - 所有的 log_* 函数都会写入到指定的文件中")
    print()
    
    # 运行所有测试
    test_log_to_new_file()
    test_log_to_append_file()
    test_log_file_failure()
    test_real_world_scenario()

    print("\n" + "=" * 60)
    print("✅ 日志写入文件功能测试完成")
    print("💡 使用方法:")
    print("   1. 在程序开始时调用 set_global_log_file('/path/to/log.file')")
    print("   2. 使用 log_add=False 重新创建文件（默认）")
    print("   3. 使用 log_add=True 追加到现有文件")
    print("   4. 之后所有的日志都会写入到指定文件")
    print("=" * 60)


if __name__ == "__main__":
    main()
