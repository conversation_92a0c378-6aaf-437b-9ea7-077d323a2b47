import json
import random
import time
from datetime import datetime
import logging
from urllib.parse import urlparse
from concurrent.futures import ThreadPoolExecutor, as_completed
from spiders.core_spider.base_spider import BaseSpider
from utils.country_utils import CountryCodeUtils
from utils.http_utils.http_client import HTTPClient
from bs4 import BeautifulSoup
import re

def _simplify_job_data(link):
    """
    将原始职位数据简化为只包含必要信息的格式

    Args:
        job (str): 职位链接

    Returns:
        dict: 简化后的职位数据，只包含ID和路径信息
    """
    try:
        externalPath = link
        id = str(link.split('=')[-1])
    except Exception as e:
        logging.error(f"Error parsing job data{e}: {link}")
        return {
            'id': None,  # REQ号作为ID
            'externalPath': None
        }
    return {
        'id': id,
        'externalPath': externalPath
    }


class OlaplexSpider(BaseSpider):
    def __init__(self, company_name, base_url, company_url, cwiq_code):
        super().__init__(company_name, base_url, company_url, cwiq_code)
        self.company_name = company_name
        self.company_url = company_url
        self.cwiq_code = cwiq_code
        self.country_utils = CountryCodeUtils()
        self.headers = {
            'accept': '*/*',
            'accept-language': 'zh-CN,zh;q=0.9',
            'cache-control': 'no-cache',
            'pragma': 'no-cache',
            'sec-ch-ua': '"Google Chrome";v="135", "Not-A.Brand";v="8", "Chromium";v="135"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"Windows"',
            'sec-fetch-dest': 'script',
            'sec-fetch-mode': 'no-cors',
            'sec-fetch-site': 'cross-site',
            'sec-fetch-storage-access': 'active',
            'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        }
        # 创建 HTTP 客户端
        self.client = HTTPClient()

    @classmethod
    def create_spider(cls, company_name, base_url, company_url, cwiq_code):
        """工厂方法创建 MedtronicSpider 实例"""
        return cls(company_name, base_url, company_url, cwiq_code)

    def make_request(self, method, url, company_name,requset_count, **kwargs):
        """发送请求的通用方法"""
        kwargs['headers'] = self.headers
        return self.client.request(method, url, company_name, requset_count,log_ip=False, **kwargs)

    def fetch_job_details(self, external_path):
        """获取职位详细信息"""
        try:
            id = external_path.split('=')[-1]
            params = {
                'for': 'olaplexcareers',
                'token': f'{id}',
            }
            response = self.make_request('GET', 'https://job-boards.greenhouse.io/embed/job_app', self.company_name,params=params,requset_count=3)
            soup = BeautifulSoup(response.text, "html.parser")

            # 查找所有 <script> 标签
            script_tags = soup.find_all("script")

            # 遍历 <script> 标签，寻找包含 "window.__remixContext =" 的内容
            remix_context_content = None
            for script in script_tags:
                if script.string and "window.__remixContext =" in script.string:
                    remix_context_content = script.string
                    break

            if remix_context_content:
                try:
                    # 提取 "window.__remixContext =" 后的 JSON 数据
                    match = re.search(r"window\.\__remixContext = (\{.*\});", remix_context_content)
                    if match:
                        json_content = match.group(1)  # 提取 JSON 内容部分

                        # 将 JSON 内容解析为 Python 对象
                        remix_context_data = json.loads(json_content)['state']['loaderData'].get('routes/embed.job_app')['jobPost']
                        return remix_context_data
                except json.JSONDecodeError as e:
                    from utils.colored_logger import log_exception
                    log_exception(e, f"Error decoding JSON for {self.company_name}")
                    return {}
        except Exception as e:
            logging.error(f"Error fetching job details for {self.company_name}: {e}")
            return None

    def parse_job(self, job_data):
        """解析职位数据"""
        try:
            if job_data:
                job_url = job_data.get("public_url", "")
                job_id = str(job_url.split('=')[-1])
                # 解析地点信息
                state = job_data.get('job_post_location', '')
                return {
                    "jobId": job_id,
                    "company": self.company_name,
                    "cwiq_code": self.cwiq_code,
                    "companyUrl": self.company_url,
                    "country": '',
                    "city": '',
                    "jobTitle": job_data.get("title", ""),
                    # "department": job_info.get("hiringOrganization", {}).get("name", ""),
                    "location": state,
                    "jobDescription": job_data.get('content',''),
                    "jobUrl": job_url,
                    "created_time": None,
                    "updated_time": None,
                    "post_time": datetime.now(),
                }
        except Exception as e:
            logging.error(f"Error parsing job data: {e} for {self.company_name}")
            return None

    def fetch_jobs(self,max_workers):
        """获取职位信息"""
        try:
            # 获取上一批次的职位数据
            latest_batch = self.db_ops.get_latest_batch(self.company_url)
            batch_id = latest_batch.batchId if latest_batch else 0
            previous_job_ids = set(self.db_ops.get_job_ids_by_batch(
                self.company_url,
                batch_id,
                status=[0, 1]
            ))

            all_jobs = self._fetch_job_listings()

            # 从现有职位中提取所有job_ids
            current_job_ids = {
                job.get('id', '')
                for job in all_jobs
            }

            # 找出重复的job_ids
            repeated_job_ids = current_job_ids.intersection(previous_job_ids)
            new_job_ids = current_job_ids - previous_job_ids

            # 一次性获取所有重复职位的数据
            repeated_jobs_dict = self.db_ops.get_jobs_by_ids(list(repeated_job_ids))

            # 分离新职位和重复职位
            new_jobs = [
                job for job in all_jobs
                if job.get('id', '') in new_job_ids
            ]
            repeated_jobs = list(repeated_jobs_dict.values())

            logging.info(f"Found {len(new_jobs)} new jobs and {len(repeated_jobs)} repeated jobs")

            # 只处理新职位的详细信息
            processed_jobs = []
            if new_jobs:
                with ThreadPoolExecutor(max_workers=max_workers) as executor:
                    future_to_job = {
                        executor.submit(self.fetch_job_details, job.get('externalPath', '')): job
                        for job in new_jobs
                    }

                    completed = 0
                    total_new = len(new_jobs)
                    for future in as_completed(future_to_job):
                        job_details = future.result()
                        if job_details:
                            parsed_job = self.parse_job(job_details)
                            if parsed_job:
                                processed_jobs.append(parsed_job)
                        completed += 1
                        progress = (completed / total_new) * 100
                        logging.info(f"Processing new job details: {completed}/{total_new} ({progress:.2f}%)")

            # 合并新处理的职位和重复的职位
            all_processed_jobs = processed_jobs + repeated_jobs
            logging.info(f"Successfully processed {len(all_processed_jobs)} total jobs")
            return all_processed_jobs, "no total_jobs"

        except Exception as e:
            logging.error(f"Error in fetch_jobs: {e}:{self.company_name}")
            return [],0

    def _fetch_job_listings(self):
        """获取所有职位列表"""
        try:
            response = self.make_request('GET', self.base_url, self.company_name,requset_count=3)
            jobs = []
            all_jobs = []
            for i in response.json()['departments']:
                if i['jobs']:
                    for c in i['jobs']:
                        jobs.append(c['absolute_url'])
            simplified_jobs = [_simplify_job_data(job) for job in jobs]
            all_jobs.extend(simplified_jobs)

            return all_jobs
        except Exception as e:
            logging.error(f"Error in fetch_job_listings: {e}:{self.company_name}")

    def __del__(self):
        """确保 HTTP 客户端被正确关闭"""
        if hasattr(self, 'client'):
            self.client.close()
