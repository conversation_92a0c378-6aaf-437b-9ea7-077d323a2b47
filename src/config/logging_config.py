# config/logging_config.py
import logging
import logging.handlers
import os
from datetime import datetime


def setup_logging(log_file=None):
    """
    配置日志系统

    Args:
        log_file (str, optional): 日志文件路径。如果为 None，则不写入文件，仅控制台输出

    说明:
        - 如果 log_file 为 None：仅控制台输出，不创建文件处理器
        - 如果 log_file 有值：创建文件处理器，控制台输出由彩色日志系统处理
    """
    # 配置根日志记录器
    root_logger = logging.getLogger()
    root_logger.setLevel(logging.INFO)

    # 清除现有的处理器，避免重复
    root_logger.handlers.clear()

    # 如果 log_file 为 None，则不设置文件处理器，仅控制台输出
    if log_file is None:
        print("💡 日志配置：仅控制台输出，不写入文件")
        # 不添加任何处理器，让彩色日志系统处理控制台输出
    else:
        # 创建日志文件目录
        log_dir = os.path.dirname(log_file)
        if log_dir and not os.path.exists(log_dir):
            os.makedirs(log_dir)
            print(f"📁 创建日志目录: {log_dir}")

        # 使用指定的日志文件名
        log_filename = log_file

        # 创建格式化器
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )

        # 创建文件处理器（按天滚动）
        file_handler = logging.handlers.TimedRotatingFileHandler(
            filename=log_filename,
            when='midnight',
            interval=1,
            backupCount=30,  # 保留30天的日志
            encoding='utf-8'
        )
        file_handler.setFormatter(formatter)
        file_handler.setLevel(logging.INFO)

        # 只添加文件处理器
        root_logger.addHandler(file_handler)
        print(f"📝 日志配置：文件输出到 {log_filename}")

    # 配置特定模块的日志级别
    logging.getLogger('urllib3').setLevel(logging.WARNING)
    logging.getLogger('requests').setLevel(logging.WARNING)
    logging.getLogger('celery').setLevel(logging.INFO)
