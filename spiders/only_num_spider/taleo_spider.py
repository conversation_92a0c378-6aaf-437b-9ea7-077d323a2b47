import json
import random
import time
from datetime import datetime
import logging
from urllib.parse import urlparse
from concurrent.futures import ThreadPoolExecutor, as_completed
from spiders.core_spider.base_spider import BaseSpider
from utils.country_utils import CountryCodeUtils
from utils.http_utils.http_client import HTTPClient
from bs4 import BeautifulSoup
import re
from lxml import etree

class TaleoSpider(BaseSpider):
    def __init__(self, company_name, base_url, company_url, cwiq_code):
        super().__init__(company_name, base_url, company_url, cwiq_code)
        self.company_name = company_name
        self.company_url = company_url
        self.cwiq_code = cwiq_code
        self.country_utils = CountryCodeUtils()
        self.headers = {
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
            'Accept-Language': 'zh-CN,zh;q=0.9',
            'Cache-Control': 'no-cache',
            'Connection': 'keep-alive',
            'Content-Type': 'application/x-www-form-urlencoded',
            'Origin': 'https://tre.tbe.taleo.net',
            'Pragma': 'no-cache',
            'Referer': 'https://tre.tbe.taleo.net/tre01/ats/careers/v2/jobSearch',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'same-origin',
            'Sec-Fetch-User': '?1',
            'Upgrade-Insecure-Requests': '1',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"Windows"',
            # 'Cookie': 'JSESSIONID=55307DB31441B236083B0009AC889427; ak_bmsc=67B03D4AFBE8841A2AA574E0CF308794~000000000000000000000000000000~YAAQlF7WF7yRv4KWAQAAO0Nurxup7kpwnd59a/hdwTsddXOmoVJou62815ZMDXxsDf7E23r1IeZEh7NXHnBYZgqB3iXnIwJDuNQKxx2VwdFD9IpvvK/IbuuYSllezzdXvMLcDiosDZy6Ttvgg4wa4N5rNuk1mMmcjazioAiCfRWCT4ufSzctRwCrLFpycroKmzCsx0IYFBD86QJVEtQTsyXzgSOvTG/EGl1pOXiRLVgNwNR/9S6FkrRCcpv39XaK33PvyYL2dKiMyNHW6O+BL1KHrbAlUnFzdm6WOKa8vEq5Uxmwink3dEo7onqQM0PwwqIGZX59lVd5/Dye8cWw2NFe4NHu+ZVRJV3r6s/04rjwK/jtbqUSzffsU7EYw3qh; bm_sv=67C2A6B6D0B67B79CF3131A0BEA8D1B5~YAAQlF7WF9KRv4KWAQAAlkVurxvy7c7JvT7hM2CvBlbmFf9cfczBjmveHuid76b74+jOKDRs7nfPDN1R8YPS5wrFI4EcVgpABWSfU69YdEUCz3grFYoNgT+yta1HnCj6/ZQytoiN1hyj35nPY+zre5/zpjA201lca8+VaS6SBn6O8j5Li2Azriyg9x/9aTPyRDXP5prtnQoSno4ylw3ss6Ut63CL2Cmdtmkvhui6VXWKTtS4pRb/p53PN+7FZQqk8ze+~1',
        }

        # 创建 HTTP 客户端
        self.client = HTTPClient()

    @classmethod
    def create_spider(cls, company_name, base_url, company_url, cwiq_code):
        """工厂方法创建 MedtronicSpider 实例"""
        return cls(company_name, base_url, company_url, cwiq_code)

    def make_request(self, method, url, company_name,requset_count, **kwargs):
        """发送请求的通用方法"""
        kwargs['headers'] = self.headers
        return self.client.request(method, url, company_name, requset_count,log_ip=False, **kwargs)

    def get_total_jobs(self):
        try:
            params = {
                'org': 'GROUCSLI',
                'cws': '37',
            }
            response = self.make_request('POST', self.base_url, self.company_name, requset_count=3,params=params)
            tree = etree.HTML(response.text)
            total = tree.xpath("//div[@class='panel panel-default oracletaleocwsv2-totals-callout']/div[@class='panel-body']/span[@class='oracletaleocwsv2-panel-number']//text()")[0]
            return total
        except Exception as e:
            logging.error(f"Error getting total jobs: {e} for {self.company_name}")
            return 0

    def parse_job(self, job_data):
        """解析职位数据"""
        pass

    def fetch_jobs(self,max_workers):
        """获取职位信息"""
        try:
            total_jobs = self.get_total_jobs()
            if total_jobs == 0:
                logging.info(f"No jobs found {self.company_name}")
                return [], 0

            logging.info(f"Found {total_jobs} total jobs")
            return [], total_jobs
        except Exception as e:
            logging.error(f"Error in fetch_jobs: {e}:{self.company_name}")
            return [],0

    def __del__(self):
        """确保 HTTP 客户端被正确关闭"""
        if hasattr(self, 'client'):
            self.client.close()
