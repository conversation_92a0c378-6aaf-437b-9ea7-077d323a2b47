#!/usr/bin/env python3
"""
默认模式 vs 仅控制台模式测试脚本 - test_default_vs_console_only.py

测试默认配置（写入文件）和仅控制台模式的区别
"""

import os
import sys
import tempfile

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_default_logging():
    """测试默认日志配置（应该写入文件）"""
    print("🎨 测试默认日志配置")
    print("=" * 60)
    
    try:
        from utils.colored_logger import log_info, log_success, log_error
        
        print("使用默认配置进行日志输出...")
        print("预期：控制台显示 + 写入文件")
        
        log_info("默认模式 - INFO 日志")
        log_success("默认模式 - SUCCESS 日志")
        log_error("默认模式 - ERROR 日志")
        
        print("✅ 默认日志配置测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 默认日志配置测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_console_only_logging():
    """测试仅控制台输出模式"""
    print("\n🎨 测试仅控制台输出模式")
    print("=" * 60)
    
    try:
        from utils.colored_logger import set_console_only_logging, log_info, log_success, log_error
        
        print("设置仅控制台输出模式...")
        set_console_only_logging('INFO')
        
        print("预期：仅控制台显示，不写入文件")
        
        log_info("仅控制台模式 - INFO 日志")
        log_success("仅控制台模式 - SUCCESS 日志")
        log_error("仅控制台模式 - ERROR 日志")
        
        print("✅ 仅控制台输出模式测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 仅控制台输出模式测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_explicit_file_logging():
    """测试明确指定文件的日志模式"""
    print("\n🎨 测试明确指定文件的日志模式")
    print("=" * 60)
    
    try:
        from utils.colored_logger import set_global_log_file, log_info, log_success, log_error
        
        # 创建临时文件
        temp_file = tempfile.mktemp(suffix='.log', prefix='explicit_file_')
        print(f"设置日志文件: {temp_file}")
        
        set_global_log_file(temp_file, log_add=False, console_level='INFO')
        
        print("预期：控制台显示 + 写入指定文件")
        
        log_info("明确文件模式 - INFO 日志")
        log_success("明确文件模式 - SUCCESS 日志")
        log_error("明确文件模式 - ERROR 日志")
        
        # 检查文件是否创建
        if os.path.exists(temp_file):
            with open(temp_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            print(f"✅ 文件已创建，包含 {len(lines)} 行日志")
            
            # 显示文件内容
            print("文件内容:")
            for i, line in enumerate(lines, 1):
                print(f"  {i}: {line.strip()}")
            
            os.unlink(temp_file)  # 清理文件
        else:
            print("❌ 文件未创建")
            return False
        
        print("✅ 明确指定文件的日志模式测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 明确指定文件的日志模式测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_config_file_none_setting():
    """测试配置文件中设置 None 的情况"""
    print("\n🎨 测试配置文件中设置 None")
    print("=" * 60)
    
    try:
        from utils.config_loader import ConfigLoader
        from utils.colored_logger import create_colored_logger
        
        # 创建临时配置文件，设置 default_log_file = None
        temp_config = tempfile.mktemp(suffix='.ini', prefix='config_none_')
        with open(temp_config, 'w', encoding='utf-8') as f:
            f.write("""[logger]
console_level = INFO
default_log_file = None
log_format = %(asctime)s - %(name)s - %(levelname)s - %(message)s
""")
        
        print(f"创建临时配置文件: {temp_config}")
        
        # 加载配置
        loader = ConfigLoader(temp_config)
        logger_config = loader.get_logger_config()
        
        print(f"配置中的 default_log_file: {repr(logger_config['DEFAULT_LOG_FILE'])}")
        
        # 使用这个配置创建日志记录器
        logger = create_colored_logger(
            console_level=logger_config['CONSOLE_LEVEL'],
            log_file=logger_config['DEFAULT_LOG_FILE']
        )
        
        print("使用配置文件中的 None 设置进行日志输出...")
        logger.info("配置文件 None 模式 - INFO 日志")
        logger.success("配置文件 None 模式 - SUCCESS 日志")
        
        # 清理
        os.unlink(temp_config)
        
        print("✅ 配置文件中设置 None 测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 配置文件中设置 None 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("📝 默认模式 vs 仅控制台模式测试")
    print("🎯 测试目标:")
    print("   1. 默认配置应该写入文件")
    print("   2. set_console_only_logging() 实现仅控制台输出")
    print("   3. 明确指定文件路径正常工作")
    print("   4. 配置文件中设置 None 正常工作")
    print()
    
    tests = [
        ("默认日志配置", test_default_logging),
        ("仅控制台输出模式", test_console_only_logging),
        ("明确指定文件模式", test_explicit_file_logging),
        ("配置文件 None 设置", test_config_file_none_setting)
    ]
    
    success_count = 0
    total_tests = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🧪 运行测试: {test_name}")
        if test_func():
            success_count += 1
            print(f"✅ {test_name} 测试通过")
        else:
            print(f"❌ {test_name} 测试失败")
    
    print("\n" + "=" * 60)
    print(f"测试结果: {success_count}/{total_tests} 通过")
    
    if success_count == total_tests:
        print("✅ 所有测试通过")
        print("💡 日志配置总结:")
        print("   - 默认配置：控制台 + 文件输出")
        print("   - set_console_only_logging()：仅控制台输出")
        print("   - set_global_log_file(None)：仅控制台输出")
        print("   - 配置文件 default_log_file = None：仅控制台输出")
    else:
        print("❌ 部分测试失败")

if __name__ == "__main__":
    main()
