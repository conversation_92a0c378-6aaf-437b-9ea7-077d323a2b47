import time
import random

from spiders.core_spider.base_spider import BaseSpider
from utils.country_utils import CountryCodeUtils
import logging
from datetime import datetime
from concurrent.futures import ThreadPoolExecutor, as_completed

from utils.http_utils.http_client import HTTPClient

class EightfoldSpider(BaseSpider):
    def __init__(self, company_name, base_url, company_url, cwiq_code):
        super().__init__(company_name, base_url, company_url, cwiq_code)
        self.company_name = company_name
        self.company_url = company_url
        self.cwiq_code = cwiq_code
        self.country_utils = CountryCodeUtils()

        self.headers = {
            'accept': '*/*',
            'accept-language': 'zh-CN,zh;q=0.9',
            'cache-control': 'max-age=0',
            'content-type': 'application/json',
            'priority': 'u=1, i',
            'referer': 'https://careers.micron.com/careers',
            'sec-ch-ua': '"Not(A:Brand";v="99", "Microsoft Edge";v="133", "Chromium";v="133"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"Windows"',
            'sec-fetch-dest': 'empty',
            'sec-fetch-mode': 'cors',
            'sec-fetch-site': 'same-origin',
            'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/133.0.0.0 Safari/537.36 Edg/133.0.0.0',
        }

        # 创建 HTTP 客户端，使用全局代理配置
        self.client = HTTPClient()

    @classmethod
    def create_spider(cls, company_name, base_url, company_url, cwiq_code):
        """工厂方法创建 MedtronicSpider 实例"""
        return cls(company_name, base_url, company_url, cwiq_code)

    def make_request(self, method, url,company_name, requset_count, **kwargs):
        """发送请求的通用方法"""
        kwargs['headers'] = self.headers
        return self.client.request(method, url, company_name, requset_count, log_ip=False, **kwargs)

    def get_total_jobs(self):
        """获取总工作数"""
        try:
            params = {
                'start': '0',
                'num': '10',
                'sort_by': 'relevance',
                'location': 'any',
            }
            response = self.make_request('GET', self.base_url, self.company_name, requset_count=3, params=params)
            return response.json()['count']
        except Exception as e:
            logging.error(f"Error getting total jobs: {e} for {self.company_name}")
            return 0

    def parse_job(self, job_data):
        """解析职位数据"""
        try:
            if job_data:
                location = job_data.get("location", "")
                # # 如果包含 ","，则按 "," 分割
                # if "," in location:
                #     parts = location.split(",")
                # else:
                #     parts = [location]
                # # 去除每部分的前后空格
                # parts = [part.strip() for part in parts]
                # # 处理分割后的情况
                # if len(parts) == 3:
                #     country = parts[2]
                #     city = parts[0]
                #     state = parts[1]
                # elif len(parts) == 2:
                #     country = parts[1]
                #     city = parts[0]
                #     state = ""
                # else:
                #     country = parts[0].strip()
                #     city = ""
                #     state = ""
                # country_code = self.country_utils.get_alpha3(country)
                return {
                    "jobId": str(job_data.get("id", "")),
                    "company": self.company_name,
                    "cwiq_code": self.cwiq_code,
                    "companyUrl": self.company_url,
                    "country": None,
                    "city": None,
                    "jobTitle": job_data.get("name", ""),
                    # "department": job_info.get("hiringOrganization", {}).get("name", ""),
                    "location": location,
                    "jobDescription": job_data.get("job_description", ""),
                    "jobUrl": job_data.get("canonicalPositionUrl", ""),
                    "created_time": None,
                    "updated_time": None,
                    "post_time": datetime.now(),
                }
        except Exception as e:
            logging.error(f"Error parsing job data: {e} for {self.company_name}")
            return None

    def fetch_jobs(self,max_workers):
        """获取所有职位信息"""
        try:
            # 获取工作总数量
            total_jobs = self.get_total_jobs()
            if total_jobs == 0:
                logging.info(f"No jobs found {self.company_name}")
                return [],0

            logging.info(f"Found {total_jobs} total jobs")

            jobs_per_request = 10
            total_requests = (total_jobs // jobs_per_request) + (1 if total_jobs % jobs_per_request else 0)
            all_jobs = []
            processed_jobs = []

            with ThreadPoolExecutor(max_workers=10) as executor:
                futures = []
                # 提交所有请求任务
                for i in range(total_requests):
                    params = {
                        'start': i * jobs_per_request,
                        'num': jobs_per_request,
                        'sort_by': 'relevance',
                        'location': 'any',
                    }
                    futures.append(
                        executor.submit(
                            self.make_request,
                            'GET',
                            self.base_url,
                            self.company_name,
                            requset_count=3,
                            params=params
                        )
                    )
                    # 每个请求之间随机延时 0.1 到 0.5 秒
                    time.sleep(0.1)

                # 处理所有任务的结果
                for future in as_completed(futures):
                    try:
                        response = future.result()
                        jobs = response.json().get('positions', [])
                        all_jobs.extend(jobs)

                        # 解析每条数据
                        for job in jobs:
                            parsed_job = self.parse_job(job)
                            if parsed_job:
                                processed_jobs.append(parsed_job)

                        # 显示进度
                        if total_jobs > 0:
                            progress = (len(all_jobs) / total_jobs) * 100
                            logging.info(f"Progress: {len(all_jobs)}/{total_jobs} jobs fetched ({progress:.2f}%)")
                    except Exception as e:
                        logging.error(f"Error processing job batch for {self.company_name}: {e}")

            logging.info(f"Successfully fetched {len(processed_jobs)} jobs")
            return processed_jobs,total_jobs

        except Exception as e:
            logging.error(f"Error in fetch_jobs: {e}:{self.company_name}")
            return [],0


def __del__(self):
        """确保 HTTP 客户端被正确关闭"""
        if hasattr(self, 'client'):
            self.client.close()
