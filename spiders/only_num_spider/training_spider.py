import json
import random
import time
from datetime import datetime
import logging
from urllib.parse import urlparse
from concurrent.futures import ThreadPoolExecutor, as_completed
from spiders.core_spider.base_spider import BaseSpider
from utils.country_utils import CountryCodeUtils
from utils.http_utils.http_client import HTTPClient
from bs4 import BeautifulSoup
import re
from lxml import etree
class TrainingSpider(BaseSpider):
    def __init__(self, company_name, base_url, company_url, cwiq_code):
        super().__init__(company_name, base_url, company_url, cwiq_code)
        self.company_name = company_name
        self.company_url = company_url
        self.cwiq_code = cwiq_code
        self.country_utils = CountryCodeUtils()
        self.headers = {
            'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
            'accept-language': 'zh-CN,zh;q=0.9',
            'priority': 'u=0, i',
            'referer': 'https://f45-training-careers.careerplug.com/jobs?z=&d=100&n=&t=&locale=en-US',
            'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"Windows"',
            'sec-fetch-dest': 'document',
            'sec-fetch-mode': 'navigate',
            'sec-fetch-site': 'same-origin',
            'sec-fetch-user': '?1',
            'upgrade-insecure-requests': '1',
            'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            # 'cookie': '_career_plug_ats_session=fVT89Zftx3Hea5JDcEccWrDwbS2NQeidqjklA%2BWVeowdlk6vWt0jHQDA6Rtl3I%2FQ4x6xxp2RpfMAP1XblxCkhDwgwtZdziOwE5sQes0rGmBoQD8iRA%2BWpHFlo7pp22r1ECvYWj3sm5yCrzE%2BaPiGUKezzc6pjQgXuJ8CxpiYh0H5WTyHCwGjm0p6wQy%2FqfKde3zXxyJZysUAfQ2G98muRwwtgZIps%2BvI%2FnXG9zsMCnjQXlrQGSaR3Ih5GFw7LQoHFhUBhxqCzBA82fnkQveFcpK4lwk5Frm66DD9iRNvYkXPkCKkrknjV5jC1Wvws8Shu22THM8cfyfELNNtZOm7wW3A%2BsOtp57TcNEDm0c6LhHAfJlcJz0A%2FTlIzglHLK1AETJ6r%2F6hnIdoDwjuQ8jb4qV4BehXJexBNzGXtRsxaQmagvP7TXwUbQkS7JkP6XF0RyeEPVJ0qJxi8MVZv%2FpMUr%2FpstQJi2%2BC6RpwFe0NOOmbNFFKJXDkpxtFIoznp3JcUMvLFc395l2JxAhXOiYKdhOycBE9cbE5zL89G%2FqQMBreBEcsuvHa0WDG9qfHB0n%2BNtmTIx5RSlkw3Q7Q4zLw8v9zo%2FsKUhBtlnExeT7PUl%2FaexlbgnGS2fNkxwQ7Jm69zFne9Rn8iiHbWUJ%2BSI%2Fgfu%2Bjb4Od2%2FqFhPfJQi1sW5KQKXJiztr8ELK5uCOhOSyu%2BeuXC4ncKWPVY%2FRwG2nF5bTOWT0UlAfYJ7EhthsXFpj6cilu%2FNxy0qh2I1xOOM3S8wQUCuYdCztBxnVooiRgdIXFxmURBcivEDL8pS4r8h0TgX2a4%2FFf2S8UmtARJ3Dm0KkUjb1pX1sjJzudpv8g%2BoaLGUMDHAuqS5Rw%2Biq9cEq1f%2FH7vW6meofru%2FWD1Ib62ivdcQ7UbG5ARdv5AUEEKahgR%2BwF%2FWKdArkW4COXSSD7k2kapm2IBclNp6tGYyPNeAVyJcsDsbs8V8cZkGPoKt%2B2jNUBKeOHFMHiN5CymXFHDn0XqiOR%2FbgCViPWyXs6GlAu1CDQa%2F8MGmp7--zZ7yYB3esJiLnD%2Be--IgMqQ2LwhsiP095sNDRIPw%3D%3D',
        }

        # 创建 HTTP 客户端
        self.client = HTTPClient()

    @classmethod
    def create_spider(cls, company_name, base_url, company_url, cwiq_code):
        """工厂方法创建 MedtronicSpider 实例"""
        return cls(company_name, base_url, company_url, cwiq_code)

    def make_request(self, method, url, company_name, requset_count, **kwargs):
        """发送请求的通用方法"""
        kwargs['headers'] = self.headers
        return self.client.request(method, url, company_name, requset_count, log_ip=False, **kwargs)

    def get_total_jobs(self):
        """获取总职位数"""
        try:
            params = {
                'z': '',
                'd': '10',
                'n': '',
                't': '',
                'locale': 'en-US',
            }
            response = self.make_request('GET', self.base_url, self.company_name, requset_count=3,params=params)
            tree = etree.HTML(response.text)
            total_jobs = tree.xpath("//p[@class='alert alert-info']/text()")[0]
            result = re.findall(r'\d+', total_jobs)
            num = int(result[0])
            return num
        except Exception as e:
            logging.error(f"Error getting total jobs: {e} for {self.company_name}")
            return 0

    def parse_job(self, job_data):
        """解析职位数据"""
        pass

    def fetch_jobs(self,max_workers):
        """获取所有职位信息"""
        try:
            total_jobs = self.get_total_jobs()
            if total_jobs == 0:
                logging.info(f"No jobs found {self.company_name}")
                return [], 0

            logging.info(f"Found {total_jobs} total jobs")

            return [], total_jobs

        except Exception as e:
            logging.error(f"Error in fetch_jobs: {e}:{self.company_name}")
            return [], 0


    def __del__(self):
        """确保 HTTP 客户端被正确关闭"""
        if hasattr(self, 'client'):
            self.client.close()

