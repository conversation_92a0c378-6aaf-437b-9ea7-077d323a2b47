#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Marzaha 爬虫系统健康监控器
实时监控系统状态并提供告警功能
"""

import time
import json
import logging
import psutil
import threading
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, Optional
import smtplib
from email.mime.text import MIMEText

class HealthMonitor:
    """系统健康监控器"""
    
    def __init__(self, config_file: str = "config/monitor.json"):
        self.config = self.load_config(config_file)
        self.monitoring = False
        self.monitor_thread = None
        self.metrics_history = []
        self.alert_cooldown = {}
        
        # 创建监控目录
        self.log_dir = Path("monitoring/logs")
        self.log_dir.mkdir(parents=True, exist_ok=True)
        
        # 设置日志
        self.setup_logging()
        
    def load_config(self, config_file: str) -> Dict:
        """加载监控配置"""
        default_config = {
            "thresholds": {
                "cpu_percent": 80,
                "memory_percent": 85,
                "disk_percent": 90,
                "load_average": 4.0,
                "spider_success_rate": 80
            },
            "intervals": {
                "check_interval": 60,
                "alert_cooldown": 300,
                "history_retention": 3600
            },
            "alerts": {
                "email_enabled": False,
                "email_smtp": "smtp.gmail.com",
                "email_port": 587,
                "email_user": "",
                "email_password": "",
                "email_recipients": []
            }
        }
        
        try:
            with open(config_file, 'r') as f:
                user_config = json.load(f)
                default_config.update(user_config)
        except FileNotFoundError:
            # 创建默认配置文件
            with open(config_file, 'w') as f:
                json.dump(default_config, f, indent=2)
            logging.info(f"Created default config: {config_file}")
        
        return default_config
    
    def setup_logging(self):
        """设置日志"""
        log_file = self.log_dir / "health_monitor.log"
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_file),
                logging.StreamHandler()
            ]
        )
    
    def get_system_metrics(self) -> Dict[str, Any]:
        """获取系统指标"""
        try:
            # CPU 指标
            cpu_percent = psutil.cpu_percent(interval=1)
            cpu_count = psutil.cpu_count()
            load_avg = psutil.getloadavg()[0] if hasattr(psutil, 'getloadavg') else 0
            
            # 内存指标
            memory = psutil.virtual_memory()
            swap = psutil.swap_memory()
            
            # 磁盘指标
            disk = psutil.disk_usage('/')
            
            # 网络指标
            network = psutil.net_io_counters()
            
            # 进程指标
            process_count = len(psutil.pids())
            
            metrics = {
                'timestamp': time.time(),
                'datetime': datetime.now().isoformat(),
                'cpu': {
                    'percent': cpu_percent,
                    'count': cpu_count,
                    'load_average': load_avg
                },
                'memory': {
                    'total': memory.total,
                    'available': memory.available,
                    'percent': memory.percent,
                    'used': memory.used,
                    'free': memory.free
                },
                'swap': {
                    'total': swap.total,
                    'used': swap.used,
                    'percent': swap.percent
                },
                'disk': {
                    'total': disk.total,
                    'used': disk.used,
                    'free': disk.free,
                    'percent': disk.percent
                },
                'network': {
                    'bytes_sent': network.bytes_sent,
                    'bytes_recv': network.bytes_recv,
                    'packets_sent': network.packets_sent,
                    'packets_recv': network.packets_recv
                },
                'processes': {
                    'count': process_count
                }
            }
            
            return metrics
            
        except Exception as e:
            logging.error(f"Error getting system metrics: {e}")
            return {}
    
    def get_spider_metrics(self) -> Dict[str, Any]:
        """获取爬虫相关指标"""
        try:
            # 检查爬虫进程
            spider_processes = []
            for proc in psutil.process_iter(['pid', 'name', 'cmdline', 'cpu_percent', 'memory_percent']):
                try:
                    if 'spider.py' in ' '.join(proc.info['cmdline'] or []):
                        spider_processes.append({
                            'pid': proc.info['pid'],
                            'cpu_percent': proc.info['cpu_percent'],
                            'memory_percent': proc.info['memory_percent']
                        })
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue
            
            # 检查数据库连接
            db_status = self.check_database_connection()
            
            # 检查日志文件
            log_status = self.check_log_files()
            
            return {
                'spider_processes': spider_processes,
                'process_count': len(spider_processes),
                'database_status': db_status,
                'log_status': log_status
            }
            
        except Exception as e:
            logging.error(f"Error getting spider metrics: {e}")
            return {}
    
    def check_database_connection(self) -> Dict[str, Any]:
        """检查数据库连接"""
        try:
            from utils.mysql_handler import MySQLHandler
            db = MySQLHandler()
            
            start_time = time.time()
            with db.get_session() as session:
                session.execute("SELECT 1")
            response_time = time.time() - start_time
            
            return {
                'status': 'healthy',
                'response_time': response_time
            }
        except Exception as e:
            return {
                'status': 'error',
                'error': str(e)
            }
    
    def check_log_files(self) -> Dict[str, Any]:
        """检查日志文件状态"""
        try:
            log_files = [
                "/mnt/data1/leo/spider_*.log",
                "monitoring/logs/*.log"
            ]
            
            status = {}
            for pattern in log_files:
                files = list(Path().glob(pattern))
                for file_path in files:
                    if file_path.exists():
                        stat = file_path.stat()
                        status[str(file_path)] = {
                            'size': stat.st_size,
                            'modified': stat.st_mtime,
                            'age_hours': (time.time() - stat.st_mtime) / 3600
                        }
            
            return status
        except Exception as e:
            logging.error(f"Error checking log files: {e}")
            return {}
    
    def check_thresholds(self, metrics: Dict[str, Any]) -> Dict[str, Any]:
        """检查阈值并生成告警"""
        alerts = []
        thresholds = self.config['thresholds']
        
        # CPU 检查
        if metrics.get('cpu', {}).get('percent', 0) > thresholds['cpu_percent']:
            alerts.append({
                'type': 'cpu_high',
                'severity': 'warning',
                'message': f"CPU使用率过高: {metrics['cpu']['percent']:.1f}%",
                'value': metrics['cpu']['percent'],
                'threshold': thresholds['cpu_percent']
            })
        
        # 内存检查
        if metrics.get('memory', {}).get('percent', 0) > thresholds['memory_percent']:
            alerts.append({
                'type': 'memory_high',
                'severity': 'warning',
                'message': f"内存使用率过高: {metrics['memory']['percent']:.1f}%",
                'value': metrics['memory']['percent'],
                'threshold': thresholds['memory_percent']
            })
        
        # 磁盘检查
        if metrics.get('disk', {}).get('percent', 0) > thresholds['disk_percent']:
            alerts.append({
                'type': 'disk_high',
                'severity': 'critical',
                'message': f"磁盘使用率过高: {metrics['disk']['percent']:.1f}%",
                'value': metrics['disk']['percent'],
                'threshold': thresholds['disk_percent']
            })
        
        # 负载检查
        load_avg = metrics.get('cpu', {}).get('load_average', 0)
        if load_avg > thresholds['load_average']:
            alerts.append({
                'type': 'load_high',
                'severity': 'warning',
                'message': f"系统负载过高: {load_avg:.2f}",
                'value': load_avg,
                'threshold': thresholds['load_average']
            })
        
        return {'alerts': alerts, 'alert_count': len(alerts)}
    
    def send_alert(self, alert: Dict[str, Any]):
        """发送告警"""
        alert_type = alert['type']
        current_time = time.time()
        cooldown = self.config['intervals']['alert_cooldown']
        
        # 检查冷却时间
        if alert_type in self.alert_cooldown:
            if current_time - self.alert_cooldown[alert_type] < cooldown:
                return
        
        self.alert_cooldown[alert_type] = current_time
        
        # 记录告警
        logging.warning(f"ALERT: {alert['message']}")
        
        # 发送邮件告警
        if self.config['alerts']['email_enabled']:
            self.send_email_alert(alert)
    
    def send_email_alert(self, alert: Dict[str, Any]):
        """发送邮件告警"""
        try:
            smtp_config = self.config['alerts']
            
            msg = MIMEText(f"""
Marzaha 爬虫系统告警

告警类型: {alert['type']}
严重程度: {alert['severity']}
告警信息: {alert['message']}
当前值: {alert['value']}
阈值: {alert['threshold']}
时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
            """)
            
            msg['Subject'] = f"[Marzaha Alert] {alert['type']}"
            msg['From'] = smtp_config['email_user']
            msg['To'] = ', '.join(smtp_config['email_recipients'])
            
            with smtplib.SMTP(smtp_config['email_smtp'], smtp_config['email_port']) as server:
                server.starttls()
                server.login(smtp_config['email_user'], smtp_config['email_password'])
                server.send_message(msg)
                
            logging.info(f"Email alert sent for {alert['type']}")
            
        except Exception as e:
            logging.error(f"Failed to send email alert: {e}")
    
    def save_metrics(self, metrics: Dict[str, Any]):
        """保存监控指标"""
        # 保存到 JSON Lines 文件
        metrics_file = self.log_dir / "system_metrics.jsonl"
        with open(metrics_file, 'a') as f:
            f.write(json.dumps(metrics) + '\n')
        
        # 保存到内存历史（用于趋势分析）
        self.metrics_history.append(metrics)
        
        # 清理旧数据
        retention = self.config['intervals']['history_retention']
        cutoff_time = time.time() - retention
        self.metrics_history = [
            m for m in self.metrics_history 
            if m.get('timestamp', 0) > cutoff_time
        ]
    
    def monitor_loop(self):
        """监控主循环"""
        logging.info("Health monitor started")
        
        while self.monitoring:
            try:
                # 获取系统指标
                system_metrics = self.get_system_metrics()
                spider_metrics = self.get_spider_metrics()
                
                # 合并指标
                all_metrics = {**system_metrics, **spider_metrics}
                
                # 检查阈值
                alert_result = self.check_thresholds(all_metrics)
                all_metrics.update(alert_result)
                
                # 发送告警
                for alert in alert_result['alerts']:
                    self.send_alert(alert)
                
                # 保存指标
                self.save_metrics(all_metrics)
                
                # 输出状态
                if alert_result['alert_count'] == 0:
                    logging.info("System status: HEALTHY")
                else:
                    logging.warning(f"System status: {alert_result['alert_count']} alerts")
                
            except Exception as e:
                logging.error(f"Error in monitor loop: {e}")
            
            # 等待下次检查
            time.sleep(self.config['intervals']['check_interval'])
        
        logging.info("Health monitor stopped")
    
    def start(self):
        """启动监控"""
        if self.monitoring:
            logging.warning("Monitor is already running")
            return
        
        self.monitoring = True
        self.monitor_thread = threading.Thread(target=self.monitor_loop)
        self.monitor_thread.daemon = True
        self.monitor_thread.start()
        
        logging.info("Health monitor starting...")
    
    def stop(self):
        """停止监控"""
        self.monitoring = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=5)
        logging.info("Health monitor stopped")
    
    def get_status(self) -> Dict[str, Any]:
        """获取当前状态"""
        system_metrics = self.get_system_metrics()
        spider_metrics = self.get_spider_metrics()
        alert_result = self.check_thresholds(system_metrics)
        
        return {
            'monitoring': self.monitoring,
            'system': system_metrics,
            'spiders': spider_metrics,
            'alerts': alert_result,
            'history_count': len(self.metrics_history)
        }

def main():
    """主函数 - 可以作为独立脚本运行"""
    import argparse
    
    parser = argparse.ArgumentParser(description='Marzaha Health Monitor')
    parser.add_argument('--config', default='config/monitor.json', help='Config file path')
    parser.add_argument('--daemon', action='store_true', help='Run as daemon')
    parser.add_argument('--status', action='store_true', help='Show current status')
    
    args = parser.parse_args()
    
    monitor = HealthMonitor(args.config)
    
    if args.status:
        # 显示当前状态
        status = monitor.get_status()
        print(json.dumps(status, indent=2, default=str))
        return
    
    if args.daemon:
        # 守护进程模式
        monitor.start()
        try:
            while True:
                time.sleep(1)
        except KeyboardInterrupt:
            monitor.stop()
    else:
        # 单次检查
        status = monitor.get_status()
        print("=== Marzaha System Health Check ===")
        print(f"CPU: {status['system']['cpu']['percent']:.1f}%")
        print(f"Memory: {status['system']['memory']['percent']:.1f}%")
        print(f"Disk: {status['system']['disk']['percent']:.1f}%")
        print(f"Spider Processes: {status['spiders']['process_count']}")
        print(f"Database: {status['spiders']['database_status']['status']}")
        print(f"Alerts: {status['alerts']['alert_count']}")

if __name__ == "__main__":
    main()
