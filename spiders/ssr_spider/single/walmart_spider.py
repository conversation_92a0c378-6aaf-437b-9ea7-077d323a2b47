import json
import random
import time
from datetime import datetime
import logging
from urllib.parse import urlparse
from concurrent.futures import ThreadPoolExecutor, as_completed
from spiders.core_spider.base_spider import BaseSpider
from utils.country_utils import CountryCodeUtils
from utils.http_utils.http_client import HTTPClient
from bs4 import BeautifulSoup
import re

def _simplify_job_data(job):
    """
    将原始职位数据简化为只包含必要信息的格式

    Args:
        job (str): 职位链接

    Returns:
        dict: 简化后的职位数据，只包含ID和路径信息
    """
    try:
        externalPath = job
        id = externalPath.split("/")[-1].split("-")[0]
    except Exception as e:
        logging.error(f"Error parsing job data{e}: {job}")
        return {
            'id': None,  # REQ号作为ID
            'externalPath': None
        }
    return {
        'id': id,
        'externalPath': externalPath
    }

class WalmartSpider(BaseSpider):
    def __init__(self, company_name, base_url, company_url, cwiq_code):
        super().__init__(company_name, base_url, company_url, cwiq_code)
        self.company_name = company_name
        self.company_url = company_url
        self.cwiq_code = cwiq_code
        self.country_utils = CountryCodeUtils()
        self.headers = {
            'accept': '*/*',
            'accept-language': 'zh-CN,zh;q=0.9',
            'priority': 'u=1, i',
            'sec-ch-ua': '"Not(A:Brand";v="99", "Microsoft Edge";v="133", "Chromium";v="133"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"Windows"',
            'sec-fetch-dest': 'empty',
            'sec-fetch-mode': 'cors',
            'sec-fetch-site': 'same-origin',
            'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/133.0.0.0 Safari/537.36 Edg/133.0.0.0',
        }
        # 创建 HTTP 客户端
        self.client = HTTPClient()

    @classmethod
    def create_spider(cls, company_name, base_url, company_url, cwiq_code):
        """工厂方法创建 MedtronicSpider 实例"""
        return cls(company_name, base_url, company_url, cwiq_code)

    def make_request(self, method, url, company_name, requset_count, **kwargs):
        """发送请求的通用方法"""
        kwargs['headers'] = self.headers
        return self.client.request(method, url, company_name, requset_count, log_ip=False, **kwargs)

    def get_total_jobs(self):
        """获取总职位数"""
        params = {
            'q': '',
            'page': 1,
            'sort': 'date',
            'expand': 'department,brand,type,rate',
            'jobCareerArea': 'all',
            'type': 'jobs',
        }
        try:
            response = self.make_request('GET', self.base_url, self.company_name, requset_count=3, params=params)
            soup = BeautifulSoup(response.text, 'html.parser')
            ul_tag = soup.find("ul", id="search-results")
            total_jobs = int(ul_tag.get("data-total-results"))
            return total_jobs
        except Exception as e:
            logging.error(f"Error getting total jobs: {e} for {self.company_name}")
            return 0

    def parse_job(self, job_data):
        """解析职位数据"""
        # 解析地点信息
        try:
            if job_data:
                country = job_data.get("country", "")
                country_code = self.country_utils.get_alpha3(country)
                return {
                    "jobId": job_data.get("jobCode", ""),
                    "company": self.company_name,
                    "cwiq_code": self.cwiq_code,
                    "companyUrl": self.company_url,
                    "country": country_code,
                    "city": job_data.get("jobLocation", ""),
                    "jobTitle": job_data.get("jobTitle", ""),
                    # "department": job_info.get("hiringOrganization", {}).get("name", ""),
                    "location": job_data.get("jobState", ""),
                    "jobDescription": job_data.get("jobDescription", ""),
                    "jobUrl": job_data.get("jobSlug", ""),
                    "created_time": None,
                    "updated_time": None,
                    "post_time": datetime.now(),
                }
        except Exception as e:
            logging.error(f"Error parsing job data: {e} for {self.company_name}")
            return None

    def fetch_job_details(self, external_path):
        """获取职位详细信息"""
        try:
            time.sleep(random.uniform(0.5, 1))
            response = self.make_request('GET', external_path, self.company_name, requset_count=3)
            soup = BeautifulSoup(response.text, 'html.parser')
            script_tag = soup.find('script', string=re.compile(r'window\.walmartDataLayer'))
            # 去除前缀和末尾的分号
            json_str = script_tag.text.strip()
            if json_str.startswith("window.walmartDataLayer ="):
                json_str = json_str.split("=", 1)[1].strip().rstrip(';')
                json_str = json_str.replace('"," clean","', ', clean')
                json_str = re.sub(
                    r'("jobDescription": ")(.*)(" ,"brandImageUrl":)',
                    lambda m: m.group(1) +
                              m.group(2).replace('"', r'\"') +
                              m.group(3),
                    json_str,
                    flags=re.DOTALL
                )
            job_data = json.loads(json_str)
            return job_data[0]
        except Exception as e:
            logging.error(f"Error fetching job details for {self.company_name}{external_path}: {e}")
            return None

    def fetch_jobs(self,max_workers):
        """获取所有职位信息"""
        try:
            total_jobs = self.get_total_jobs()
            if total_jobs == 0:
                logging.info(f"No jobs found {self.company_name}")
                return [],0

            logging.info(f"Found {total_jobs} total jobs")

            # 获取上一批次的职位数据
            latest_batch = self.db_ops.get_latest_batch(self.company_url)
            batch_id = latest_batch.batchId if latest_batch else 0
            previous_job_ids = set(self.db_ops.get_job_ids_by_batch(
                self.company_url,
                batch_id,
                status=[0, 1]
            ))

            all_jobs = self._fetch_job_listings(total_jobs)

            # 从现有职位中提取所有job_ids
            current_job_ids = {
                job.get('id', '')
                for job in all_jobs
            }

            # 找出重复的job_ids
            repeated_job_ids = current_job_ids.intersection(previous_job_ids)
            new_job_ids = current_job_ids - previous_job_ids

            # 一次性获取所有重复职位的数据
            repeated_jobs_dict = self.db_ops.get_jobs_by_ids(list(repeated_job_ids))

            # 分离新职位和重复职位
            new_jobs = [
                job for job in all_jobs
                if job.get('id', '') in new_job_ids
            ]
            repeated_jobs = list(repeated_jobs_dict.values())

            logging.info(f"Found {len(new_jobs)} new jobs and {len(repeated_jobs)} repeated jobs")

            # 只处理新职位的详细信息
            processed_jobs = []
            if new_jobs:
                with ThreadPoolExecutor(max_workers=max_workers) as executor:
                    future_to_job = {
                        executor.submit(self.fetch_job_details, job.get('externalPath', '')): job
                        for job in new_jobs
                    }

                    completed = 0
                    total_new = len(new_jobs)
                    for future in as_completed(future_to_job):
                        job_details = future.result()
                        if job_details:
                            parsed_job = self.parse_job(job_details)
                            if parsed_job:
                                processed_jobs.append(parsed_job)
                        completed += 1
                        progress = (completed / total_new) * 100
                        logging.info(f"Processing new job details: {completed}/{total_new} ({progress:.2f}%)")

            # 合并新处理的职位和重复的职位
            all_processed_jobs = processed_jobs + repeated_jobs
            logging.info(f"Successfully processed {len(all_processed_jobs)} total jobs")
            return all_processed_jobs,total_jobs

        except Exception as e:
            logging.error(f"Error in fetch_jobs: {e}:{self.company_name}")
            return [],0

    def _fetch_job_listings(self, total_jobs):
        """获取所有职位列表"""
        try:
            jobs_per_request = 25
            total_requests = (total_jobs // jobs_per_request) + (1 if total_jobs % jobs_per_request else 0)
            all_jobs = []

            stop_fetching = False  # 外层停止标志

            for i in range(total_requests):
                if stop_fetching:
                    break  # 如果已经要求停止了，就彻底跳出外层循环

                params = {
                    'q': '',
                    'page': i + 1,
                    'sort': 'date',
                    'expand': 'department,brand,type,rate',
                    'jobCareerArea': 'all',
                    'type': 'jobs',
                }
                response = self.make_request('GET', self.base_url, self.company_name, requset_count=3, params=params)
                jobs = []

                soup = BeautifulSoup(response.text, 'html.parser')
                ul_tag = soup.find("ul", id="search-results")
                a_tags = ul_tag.find_all("li", class_="search-result job-listing")

                current_date = datetime.now().strftime("%m/%d/%y")  # 注意，这里是字符串了

                for a_tag in a_tags:
                    detail_url = a_tag.find("a", class_="job-listing__link")
                    b_tag = a_tag.find("span", class_="job-listing__created")

                    if not detail_url or not b_tag:
                        continue  # 防止出错，元素找不到就跳过

                    date_text = b_tag.text.strip()

                    # 对比日期
                    if current_date != date_text:
                        stop_fetching = True  # 设置外层停止标志
                        break  # 立刻停止当前页面的职位处理

                    externalPath = detail_url['href']
                    jobs.append(externalPath)

                simplified_jobs = [_simplify_job_data(job) for job in jobs]
                all_jobs.extend(simplified_jobs)

            return all_jobs

        except Exception as e:
            logging.error(f"Error fetching job listings for {self.company_name}: {e}")

    # def _fetch_job_listings(self, total_jobs):
    #     """获取所有职位列表"""
    #     try:
    #         jobs_per_request = 25
    #         total_requests = (total_jobs // jobs_per_request) + (1 if total_jobs % jobs_per_request else 0)
    #         all_jobs = []
    #
    #         with ThreadPoolExecutor(max_workers=25) as executor:
    #             futures = []
    #             for i in range(total_requests):
    #                 params = {
    #                     'q': '',
    #                     'page': i+1,
    #                     'sort': 'rank',
    #                     'expand': 'department,brand,type,rate',
    #                     'jobCareerArea': 'all',
    #                     'type': 'jobs',
    #                 }
    #                 futures.append(
    #                     executor.submit(
    #                         self.make_request,
    #                         'GET',
    #                         self.base_url,
    #                         self.company_name,
    #                         requset_count=3,
    #                         params=params
    #                     )
    #                 )
    #
    #             for future in as_completed(futures):
    #                 try:
    #                     response = future.result()
    #                     jobs = []
    #                     # 解析 HTML
    #                     soup = BeautifulSoup(response.text, 'html.parser')
    #
    #                     ul_tag = soup.find("ul", id="search-results")
    #                     a_tags = ul_tag.find_all("a", class_="job-listing__link")
    #                     for a_tag in a_tags:
    #                         externalPath = a_tag['href']
    #                         jobs.append(externalPath)
    #
    #                     # 使用新函数处理每个职位数据
    #                     simplified_jobs = [_simplify_job_data(job) for job in jobs]
    #                     all_jobs.extend(simplified_jobs)
    #                     logging.info(f"Progress: {len(all_jobs)}/{total_jobs} jobs fetched")
    #                 except Exception as e:
    #                     logging.error(f"Error processing job batch: {e}:{self.company_name}")
    #
    #         return all_jobs
    #     except Exception as e:
    #         logging.error(f"Error fetching jobs: {e}:{self.company_name}")


    def __del__(self):
        """确保 HTTP 客户端被正确关闭"""
        if hasattr(self, 'client'):
            self.client.close()

