# Marzaha 爬虫项目依赖分析报告

## 📋 依赖概览

基于对项目源码的深入分析和实际安装测试，Marzaha爬虫系统需要以下核心依赖包来正常运行。本文档包含了完整的依赖分析、安装指南和问题解决方案。

## 🎯 依赖文件说明

| 文件名 | 用途 | 特点 |
|--------|------|------|
| `requirements-tested.txt` | **推荐使用** | 经过测试验证的稳定版本 |
| `requirements-core.txt` | 核心运行时依赖 | 最小化依赖集合 |
| `requirements-simple.txt` | 简化安装 | 避免编译问题 |
| `requirements-prod.txt` | 生产环境 | 包含性能优化包 |
| `requirements.txt` | 完整依赖 | 包含开发和可选功能 |

## 🎯 核心运行时依赖 (requirements-core.txt)

### 任务调度
- **APScheduler==3.10.4** - 定时任务调度器
  - 用途: `spider.py` 中的定时调度功能
  - 关键功能: 每30分钟执行一次爬虫任务

### 数据库操作
- **PyMySQL==1.1.0** - MySQL数据库连接驱动
  - 用途: `utils/mysql_handler.py` 数据库连接
  - 关键功能: 提供MySQL数据库访问能力

- **SQLAlchemy==2.0.23** - ORM框架
  - 用途: `models/data_models.py` 数据模型定义
  - 关键功能: 对象关系映射，数据库操作抽象

### 数据处理
- **pandas==2.1.4** - 数据分析库
  - 用途: `utils/db_operations.py` 数据处理和分析
  - 关键功能: 数据清洗、转换、统计分析

- **pyarrow==14.0.2** - 列式存储格式支持
  - 用途: Parquet文件格式的读写
  - 关键功能: 高效的数据存储和传输

- **bottleneck>=1.3.4** - pandas性能优化 (可选)
  - 用途: 提升pandas数值计算性能
  - 关键功能: 滚动窗口操作加速

### HTTP请求
- **requests==2.31.0** - HTTP请求库
  - 用途: `utils/http_utils/http_client.py` 网络请求
  - 关键功能: 发送HTTP请求，获取网页内容

- **urllib3==2.1.0** - HTTP客户端库
  - 用途: requests的底层依赖，连接池管理
  - 关键功能: 连接复用，重试机制

### 网页解析
- **beautifulsoup4==4.12.2** - HTML解析器
  - 用途: 所有爬虫文件中的HTML内容解析
  - 关键功能: 提取网页中的职位信息

- **lxml==4.9.4** - XML/HTML解析器
  - 用途: BeautifulSoup的解析后端，XPath支持
  - 关键功能: 高性能的XML/HTML解析

### 配置管理
- **PyYAML==6.0.1** - YAML文件解析
  - 用途: `config/spider_config.py` 配置文件加载
  - 关键功能: 解析16,000+爬虫配置

### 地理位置处理
- **pycountry==24.6.1** - 国家代码处理
  - 用途: `utils/country_utils.py` 地理位置标准化
  - 关键功能: 国家代码转换和验证
  - 更新说明: 修复了版本兼容性问题

- **geopy==2.4.1** - 地理编码
  - 用途: `utils/country_utils.py` 地理位置解析
  - 关键功能: 地址到坐标的转换

### 云存储
- **boto3==1.34.0** - AWS SDK
  - 用途: `utils/s3/s3_upload_tool.py` S3存储
  - 关键功能: 数据文件上传到AWS S3

- **google-cloud-storage==2.10.0** - Google Cloud Storage
  - 用途: `utils/gcp/gcp_upload_tool.py` GCP存储
  - 关键功能: 数据文件上传到Google Cloud

### 系统监控
- **psutil==5.9.6** - 系统资源监控
  - 用途: `utils/monitor.py` 性能监控
  - 关键功能: CPU、内存、磁盘使用率监控

## 📦 安装方式

### 方式1: 测试版本安装 (最推荐)
```bash
make install-tested
# 或
pip install -r requirements-tested.txt
```
- ✅ 经过实际测试验证
- ✅ 版本兼容性确认
- ✅ 避免已知问题

### 方式2: 智能安装
```bash
make install
# 或
python install.py
```
- 自动检测环境
- 智能安装缺失的包
- 验证安装结果

### 方式3: 问题修复安装
```bash
make install-fix
# 或
python install_fix.py
```
- 自动处理编译问题
- 安装系统依赖
- 适用于Linux环境

### 方式4: 简化安装
```bash
make install-simple
# 或
pip install -r requirements-simple.txt
```
- 避免编译问题
- 纯Python实现
- 快速安装

### 方式5: 使用Makefile (完整选项)
```bash
make install-tested   # 测试版本 (推荐)
make install-simple   # 简化安装
make install-core     # 核心依赖
make install-prod     # 生产环境
make install-dev      # 开发环境
make install-full     # 完整依赖
```

## 🔍 依赖分析统计

- **核心依赖包数量**: 14个 (包含bottleneck)
- **总安装包数量**: ~20个 (包含子依赖)
- **预估安装大小**: ~250MB
- **Python版本要求**: >=3.8
- **测试环境**: Ubuntu 20.04, Python 3.10

## 📊 依赖分类占比

| 分类 | 包数量 | 占比 | 说明 |
|------|--------|------|------|
| 数据处理 | 4 | 29% | pandas, pyarrow, bottleneck, numpy |
| 网络请求 | 3 | 21% | requests, urllib3, beautifulsoup4 |
| 地理位置 | 2 | 14% | pycountry, geopy |
| 云存储 | 2 | 14% | boto3, google-cloud-storage |
| 数据库 | 2 | 14% | PyMySQL, SQLAlchemy |
| 其他 | 1 | 8% | APScheduler, PyYAML, psutil |

## 🚨 常见安装问题及解决方案

### 问题1: mysqlclient 编译失败
```
error: Can not find valid pkg-config name
```
**解决方案**: 使用PyMySQL替代，已在所有requirements文件中移除mysqlclient

### 问题2: pycountry 版本不存在
```
ERROR: No matching distribution found for pycountry==22.3.13
```
**解决方案**: 已更新为 pycountry==24.6.1


### 问题3: lxml 编译失败
**解决方案**:
```bash
# Ubuntu/Debian
sudo apt-get install libxml2-dev libxslt1-dev

# 或使用预编译版本
pip install --only-binary=lxml lxml
```

## 🛠️ 安装后检查和修复

### 完整检查
```bash
make check
# 或
python post_install_check.py
```
- 检查所有核心和可选模块
- 测试pandas功能和警告
- 验证数据库连接能力
- 提供详细的安装报告

### 修复工具
```bash
make fix-pandas       # 修复pandas警告
make install-fix      # 修复编译问题
```

## ⚠️ 注意事项

1. **网络要求**: 需要稳定的网络连接下载包
2. **权限要求**: 可能需要管理员权限安装某些包
3. **系统依赖**: lxml可能需要系统级的XML库支持
4. **云服务**: 云存储功能需要相应的服务账号配置
5. **虚拟环境**: 强烈建议使用虚拟环境避免包冲突

## 🚀 验证安装

### 快速验证
```bash
python -c "import pymysql, sqlalchemy, requests, bs4, yaml, apscheduler, pandas; print('✅ 核心依赖安装成功')"
```

### 完整验证
```bash
make check
```

### 测试pandas性能
```bash
python -c "
import pandas as pd
import numpy as np
df = pd.DataFrame(np.random.randn(1000, 4))
result = df.rolling(10).mean()
print('✅ pandas性能测试通过')
"
```

## 📈 性能优化建议

1. **使用虚拟环境**: 避免包冲突
```bash
   python -m venv .venv
   source .venv/bin/activate  # Linux/Mac
```

2. **固定版本**: 使用精确版本号确保稳定性
3. **分层安装**: 根据环境需求选择合适的依赖集合
4. **定期更新**: 关注安全更新和性能改进
5. **使用测试版本**: 优先使用 `requirements-tested.txt`
