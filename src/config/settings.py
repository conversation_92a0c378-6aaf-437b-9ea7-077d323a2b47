import logging

BROKER_URL = 'redis://localhost:6379/0'
CELERY_RESULT_BACKEND = 'redis://localhost:6379/1'

MYSQL_CONFIG = {
    'host': 'localhost',
    'user': 'root',
    'password': '123456',
    'database': 'crawler_leo',
    'port': 3306
}

# Logger 配置
LOGGER_CONFIG = {
    # 日志级别配置
    'CONSOLE_LEVEL': logging.INFO,      # 控制台输出级别（INFO及以上会输出到屏幕）
    'FILE_LEVEL': logging.DEBUG,       # 文件输出级别（所有级别都写入文件）

    # 默认日志文件路径
    'DEFAULT_LOG_FILE': '/mnt/data1/leo/spider_colored.log',

    # 日志格式配置
    'LOG_FORMAT': '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    'DATE_FORMAT': '%Y-%m-%d %H:%M:%S',

    # 文件模式配置
    'DEFAULT_LOG_ADD': False,          # 默认重新创建文件（False）还是追加（True）

    # 日志级别说明
    'LEVEL_DESCRIPTION': {
        logging.DEBUG: 'DEBUG - 调试信息（仅写入文件）',
        logging.INFO: 'INFO - 一般信息（输出到屏幕和文件）',
        logging.WARNING: 'WARNING - 警告信息（输出到屏幕和文件）',
        logging.ERROR: 'ERROR - 错误信息（输出到屏幕和文件）',
        logging.CRITICAL: 'CRITICAL - 严重错误（输出到屏幕和文件）'
    }
}
