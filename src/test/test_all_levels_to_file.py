#!/usr/bin/env python3
"""
所有级别写入文件测试脚本 - test_all_levels_to_file.py

测试所有级别的日志都写入文件，控制台根据级别过滤显示
"""

import os
import sys
import tempfile

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_all_levels_to_file():
    """测试所有级别都写入文件"""
    print("🎨 所有级别写入文件测试")
    print("=" * 60)
    
    try:
        from utils.colored_logger import (
            set_global_log_file, log_info, log_hint, log_warn, log_error, log_success
        )
        
        # 创建临时文件
        temp_file = tempfile.mktemp(suffix='.log', prefix='all_levels_')
        print(f"测试文件: {temp_file}")
        
        # 设置控制台级别为 WARN，但所有级别都应该写入文件
        print("设置控制台级别为 WARN")
        print("预期：控制台只显示 WARN、ERROR 和 SUCCESS")
        print("预期：文件记录所有级别（INFO、HINT、WARN、ERROR、SUCCESS）")
        set_global_log_file(temp_file, log_add=False, console_level='WARN')
        
        print("\n控制台输出:")
        log_info("INFO 级别日志 - 不应在控制台显示，但应写入文件")
        log_hint("HINT 级别日志 - 不应在控制台显示，但应写入文件")
        log_success("SUCCESS 级别日志 - 控制台和文件都应该有")
        log_warn("WARN 级别日志 - 控制台和文件都应该有")
        log_error("ERROR 级别日志 - 控制台和文件都应该有")
        
        print(f"\n文件内容 ({temp_file}):")
        with open(temp_file, 'r', encoding='utf-8') as f:
            lines = f.readlines()
            for i, line in enumerate(lines, 1):
                print(f"  {i:2d}: {line.strip()}")
        
        # 验证文件中包含所有级别
        file_content = ''.join(lines)
        levels_found = {
            'INFO': 'INFO 级别日志' in file_content,
            'HINT': 'HINT 级别日志' in file_content,
            'SUCCESS': 'SUCCESS 级别日志' in file_content,
            'WARN': 'WARN 级别日志' in file_content,
            'ERROR': 'ERROR 级别日志' in file_content
        }
        
        print(f"\n文件中找到的级别:")
        all_found = True
        for level, found in levels_found.items():
            status = "✅" if found else "❌"
            print(f"  {status} {level}: {'找到' if found else '未找到'}")
            if not found:
                all_found = False
        
        print(f"\n文件共有 {len(lines)} 行日志")
        
        if all_found:
            print("✅ 所有级别都成功写入文件")
        else:
            print("❌ 部分级别未写入文件")
        
        # 清理
        os.unlink(temp_file)
        return all_found
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_different_console_levels():
    """测试不同控制台级别下的文件写入"""
    print("\n" + "=" * 60)
    print("测试不同控制台级别下的文件写入")
    print("=" * 60)
    
    levels = ['INFO', 'HINT', 'WARN', 'ERROR']
    
    for console_level in levels:
        print(f"\n测试控制台级别: {console_level}")
        
        try:
            from utils.colored_logger import (
                set_global_log_file, log_info, log_hint, log_warn, log_error, log_success
            )
            
            # 创建临时文件
            temp_file = tempfile.mktemp(suffix='.log', prefix=f'level_{console_level.lower()}_')
            
            # 设置控制台级别
            set_global_log_file(temp_file, log_add=False, console_level=console_level)
            
            print(f"控制台级别设置为 {console_level}，测试所有级别日志:")
            
            # 写入所有级别的日志
            log_info(f"INFO 日志 - 控制台级别 {console_level}")
            log_hint(f"HINT 日志 - 控制台级别 {console_level}")
            log_success(f"SUCCESS 日志 - 控制台级别 {console_level}")
            log_warn(f"WARN 日志 - 控制台级别 {console_level}")
            log_error(f"ERROR 日志 - 控制台级别 {console_level}")
            
            # 检查文件内容
            with open(temp_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            print(f"文件中记录了 {len(lines)} 行日志")
            
            # 验证所有级别都在文件中
            file_content = ''.join(lines)
            expected_logs = ['INFO 日志', 'HINT 日志', 'SUCCESS 日志', 'WARN 日志', 'ERROR 日志']
            all_in_file = all(log in file_content for log in expected_logs)
            
            if all_in_file and len(lines) == 5:
                print(f"✅ 控制台级别 {console_level}: 所有日志都写入文件")
            else:
                print(f"❌ 控制台级别 {console_level}: 文件写入不完整")
            
            # 清理
            os.unlink(temp_file)
            
        except Exception as e:
            print(f"❌ 控制台级别 {console_level} 测试失败: {e}")

def main():
    """主函数"""
    print("📝 测试需求：所有级别的日志都要写入到log文件中")
    print("🎯 控制台根据设定级别过滤显示，文件记录所有级别")
    print()
    
    success1 = test_all_levels_to_file()
    test_different_console_levels()
    
    print("\n" + "=" * 60)
    if success1:
        print("✅ 所有级别写入文件功能正常")
    else:
        print("❌ 所有级别写入文件功能异常")
    
    print("💡 功能特性:")
    print("   - 所有级别（INFO、HINT、WARN、ERROR、SUCCESS）都写入文件")
    print("   - 控制台根据 CONSOLE_LEVEL 设置过滤显示")
    print("   - SUCCESS 级别总是在控制台显示")
    print("   - 文件记录完整的日志信息")
    print("=" * 60)

if __name__ == "__main__":
    main()
