# utils/http_client.py
import requests
import logging
from requests.adapters import <PERSON><PERSON><PERSON><PERSON>pter
from urllib3.util import Retry
import time
import threading
from concurrent.futures import ThreadPoolExecutor

from utils.http_utils.proxy_config import proxy_manager


class HTTPClientPool:
    """HTTP客户端连接池，单例模式管理所有HTTP连接"""
    _instance = None
    _lock = threading.Lock()

    def __new__(cls):
        with cls._lock:
            if cls._instance is None:
                cls._instance = super().__new__(cls)
                # 初始化连接池资源
                cls._instance.retry_pool = ThreadPoolExecutor(max_workers=1500)
                cls._instance.request_semaphore = threading.BoundedSemaphore(1500)
                cls._instance.retry_semaphore = threading.BoundedSemaphore(1500)
            return cls._instance

    def shutdown(self):
        """关闭连接池资源"""
        if hasattr(self, 'retry_pool'):
            self.retry_pool.shutdown(wait=True)


class HTTPClient:
    # 全局客户端池
    _client_pool = HTTPClientPool()

    def __init__(self, custom_proxies=None, retry_times=3, retry_delay=5, pool_size=600):
        """
        初始化 HTTP 客户端

        Args:
            custom_proxies: 自定义代理配置，如果为None则使用全局代理配置
            retry_times: 重试次数
            retry_delay: 重试延迟时间(秒)
            pool_size: 连接池大小
        """
        self.proxies = custom_proxies or proxy_manager.get_proxies()
        self.retry_delay = retry_delay
        self.session = self._create_session(retry_times, pool_size)
        self._log_current_ip()

    def _create_session(self, retry_times, pool_size):
        """创建具有重试机制的会话"""
        session = requests.Session()

        retry_strategy = Retry(
            total=retry_times,
            backoff_factor=1.5,
            status_forcelist=[429, 500, 502, 503, 504, 403],
            allowed_methods=frozenset(['GET', 'POST', 'HEAD', 'PUT', 'DELETE', 'OPTIONS', 'TRACE']),
            raise_on_status=True
        )

        adapter = HTTPAdapter(
            max_retries=retry_strategy,
            pool_maxsize=pool_size * 2,
            pool_connections=pool_size,
            pool_block=False
        )

        session.verify = True
        session.mount("http://", adapter)
        session.mount("https://", adapter)

        if self.proxies:
            session.proxies = self.proxies

        return session

    def _execute_request(self, method, url, kwargs):
        """执行单个请求"""
        response = None
        try:
            response = self.session.request(method, url, **kwargs)
            response.raise_for_status()

            if not response.content:
                raise requests.exceptions.RequestException("Empty response received")

            return response

        except Exception as a:
            try:
                if response.json():
                    if response.json().get('errorCode', ''):
                        return response
            except Exception as e:
                logging.warning(f"Error while parsing JSON: {str(e)}")
            logging.warning(
                f"Request failed for URL {url}. "
                f"Error type: {type(a).__name__}. "
                f"Error details: {str(a)}."
            )
            raise

    def request(self,method,  url, company_name, requset_count=3,log_ip=True, **kwargs):
        """
        发送 HTTP 请求，使用信号量控制并发

        Args:
            method: HTTP 方法
            url: 请求URL
            log_ip: 是否记录IP信息
            **kwargs: 其他请求参数
        """
        kwargs.setdefault('timeout', (10, 30))

        if log_ip:
            self._log_current_ip()

        for attempt in range(requset_count):
            # 更新代理（IP切换）
            self.proxies = proxy_manager.get_proxies()
            self.session.proxies = self.proxies
            # 使用请求信号量控制并发
            with self._client_pool.request_semaphore:
                try:
                    return self._execute_request(method, url, kwargs)
                except Exception as e:
                    if attempt == 2:
                        logging.error(
                            f"Request failed after all attempts for URL {url}. "
                            f"Final error: {str(e)}"
                            f"{company_name}"
                        )
                        raise

                    # 使用重试信号量和线程池处理重试延迟
                    with self._client_pool.retry_semaphore:
                        delay = min(
                            self.retry_delay * (2 ** attempt),  # 指数退避
                            30  # 最大延迟上限
                        )
                        # 添加随机抖动
                        jitter = (hash(url) % 10) * 0.1  # 0.0 到 0.9 的抖动
                        delay = delay * (1 + jitter)

                        future = self._client_pool.retry_pool.submit(time.sleep, delay)
                        future.result()  # 等待延迟完成

    def _log_current_ip(self):
        """获取并记录当前使用的IP地址"""
        try:
            response = self.session.get('http://httpbin.org/ip', timeout=(5, 10))
            if response.status_code == 200:
                ip_info = response.json()
                logging.info(f"Current IP: {ip_info.get('origin', 'Unknown')}")
        except Exception as e:
            logging.warning(f"Failed to get current IP: {e}")

    def close(self):
        """关闭会话"""
        self.session.close()

    def __enter__(self):
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        self.close()


# 确保程序退出时关闭连接池
import atexit

atexit.register(HTTPClientPool().shutdown)
