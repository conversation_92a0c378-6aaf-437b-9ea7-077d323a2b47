
import re

from spiders.core_spider.base_spider import BaseSpider
from utils.country_utils import CountryCodeUtils
import logging
from datetime import datetime
from lxml import etree
from utils.http_utils.http_client import HTTPClient


# ultipro的模板方法
class HorizonbankSpider(BaseSpider):
    def __init__(self, company_name, base_url, company_url, cwiq_code):
        super().__init__(company_name, base_url, company_url, cwiq_code)
        self.company_name = company_name
        self.company_url = company_url
        self.cwiq_code = cwiq_code
        self.country_utils = CountryCodeUtils()
        self.headers = {
    'accept': 'application/json, text/javascript, */*; q=0.01',
    'accept-language': 'zh-CN,zh;q=0.9',
    'content-type': 'application/json',
    'lumesse-language': 'EN',
    'origin': 'https://www.horizonbank.com',
    'password': 'guest',
    'priority': 'u=1, i',
    'referer': 'https://www.horizonbank.com/horizonjobs/index.html',
    'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"',
    'sec-ch-ua-mobile': '?0',
    'sec-ch-ua-platform': '"Windows"',
    'sec-fetch-dest': 'empty',
    'sec-fetch-mode': 'cors',
    'sec-fetch-site': 'cross-site',
    'sec-fetch-storage-access': 'active',
    'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    'username': 'QR0FK026203F3VBQBLO7VLO4R:guest:FO',
    # 'cookie': 'AWSALBCORS=mrMZsBVjgdTjqBhvUD39/QGwFkTIEYE+9TbIupubasocPiFzmlGj0dG9LxLxLdRGjuHX3+R9M2RfVoQmOp5cI//N6yJuWVJhGg1L30+22p0k8jpD9gtJ/lmRLOTo',
}

        # 创建 HTTP 客户端，使用全局代理配置
        self.client = HTTPClient()

    @classmethod
    def create_spider(cls, company_name, base_url, company_url, cwiq_code):
        """工厂方法创建 MedtronicSpider 实例"""
        return cls(company_name, base_url, company_url, cwiq_code)

    def make_request(self, method, url, company_name, requset_count,**kwargs):
        """发送请求的通用方法"""
        kwargs['headers'] = self.headers
        return self.client.request(method, url, company_name, requset_count,log_ip=False, **kwargs)

    def get_total_jobs(self):
        """获取总职位数"""
        try:
            params = {
                'firstResult': '0',
                'maxResults': '12',
                'sortBy': '52',
                'sortOrder': 'desc',
            }

            json_data = {
                'searchCriteria': {},
            }
            response = self.make_request('POST', self.base_url, self.company_name,requset_count=3,json=json_data,params=params)
            total = response.json()['globals']['jobsCount']
            return total
        except Exception as e:
            logging.error(f"Error getting total jobs: {e} for {self.company_name}")
            return 0

    def parse_job(self, job_data):
        """解析职位数据"""
        pass

    def fetch_jobs(self, max_workers):
        """获取所有职位信息"""
        try:
            total_jobs = self.get_total_jobs()
            if total_jobs == 0:
                logging.info(f"No jobs found {self.company_name}")
                return [], 0

            logging.info(f"Found {total_jobs} total jobs")
            return [], total_jobs
        except Exception as e:
            logging.error(f"Error in fetch_jobs: {e}:{self.company_name}")
            return [], 0

    def __del__(self):
        """确保 HTTP 客户端被正确关闭"""
        if hasattr(self, 'client'):
            self.client.close()
