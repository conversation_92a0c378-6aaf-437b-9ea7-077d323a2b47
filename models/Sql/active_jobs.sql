CREATE TABLE active_jobs (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '自增主键ID',
    jobId VARCHAR(255)  COMMENT '职位Id',
    jobTitle LONGTEXT COMMENT '职位名称',
    company VARCHAR(255) COMMENT '公司名称',
    city VARCHAR(255) COMMENT '所在城市',
    jobDescription LONGTEXT COMMENT '职位描述',
    jobUrl LONGTEXT COMMENT '职位URL',
    companyUrl VARCHAR(255) COMMENT '公司URL',
    activeTime DATETIME COMMENT '更新时间'
);


CREATE INDEX idx_active_job_company_id
ON active_jobs (companyUrl, jobId);