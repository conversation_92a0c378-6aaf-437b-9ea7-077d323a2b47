# Marzaha 爬虫系统配置文件示例
# 复制此文件为 settings.ini 并修改相应配置
# 注意：settings.ini 包含敏感信息，不会被提交到版本控制系统

[redis]
# Redis 配置
broker_url = redis://localhost:6379/0
result_backend = redis://localhost:6379/1

[mysql]
# MySQL 数据库配置
host = localhost
user = root
password = 123456
database = crawler_leo
port = 3306

[logger]
# 日志配置
# 控制台输出级别：INFO, HINT, WARN, ERROR
# 控制台会显示达到此级别及以上的日志
console_level = INFO

# 日志文件路径
# - 如果指定文件路径：所有级别的日志都会写入文件，控制台根据 console_level 过滤显示
# - 如果设置为 None：不写入文件，仅控制台输出（控制台仍然根据 console_level 过滤）
default_log_file = /mnt/data1/leo/spider_colored.log
# 仅控制台输出示例：
# default_log_file = None

# 日志格式
log_format = %(asctime)s - %(name)s - %(levelname)s - %(message)s
date_format = %Y-%m-%d %H:%M:%S
# 默认文件模式：False=重新创建，True=追加
default_log_add = False


[system]
# 系统配置
debug = False
max_workers = 10
timeout = 30
retry_times = 3

[spider]
# 爬虫配置
user_agent = Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36
request_delay = 1
max_retries = 3
timeout = 30

[paths]
# 路径配置
data_dir = /mnt/data1/leo
log_dir = /mnt/data1/leo/logs
backup_dir = /mnt/data1/leo/backups
temp_dir = /tmp/spider_temp
