import json
import random
import time
from datetime import datetime
import logging
from urllib.parse import urlparse
from concurrent.futures import ThreadPoolExecutor, as_completed
from spiders.core_spider.base_spider import BaseSpider
from utils.country_utils import CountryCodeUtils
from utils.http_utils.http_client import HTTPClient
from bs4 import BeautifulSoup
import re
def _simplify_job_data(job):
    """
    将原始职位数据简化为只包含必要信息的格式

    Args:
        job (str): 职位链接

    Returns:
        dict: 简化后的职位数据，只包含ID和路径信息
    """
    try:
        externalPath = job
        id = externalPath.split("/")[-3]
    except Exception as e:
        logging.error(f"Error parsing job data{e}: {job}")
        return {
            'id': None,  # REQ号作为ID
            'externalPath': None
        }
    return {
        'id': id,
        'externalPath': externalPath
    }

class IcimsSpider(BaseSpider):
    def __init__(self, company_name, base_url, company_url, cwiq_code):
        super().__init__(company_name, base_url, company_url, cwiq_code)
        self.company_name = company_name
        self.company_url = company_url
        self.cwiq_code = cwiq_code
        self.country_utils = CountryCodeUtils()
        self.headers = {
            'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
            'accept-language': 'zh-CN,zh;q=0.9',
            'cache-control': 'max-age=0',
            'priority': 'u=0, i',
            'sec-ch-ua': '"Not(A:Brand";v="99", "Microsoft Edge";v="133", "Chromium";v="133"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"Windows"',
            'sec-fetch-dest': 'document',
            'sec-fetch-mode': 'navigate',
            'sec-fetch-site': 'same-origin',
            'sec-fetch-user': '?1',
            'upgrade-insecure-requests': '1',
            'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/133.0.0.0 Safari/537.36 Edg/133.0.0.0',
        }

        # 创建 HTTP 客户端
        self.client = HTTPClient()

    @classmethod
    def create_spider(cls, company_name, base_url, company_url, cwiq_code):
        """工厂方法创建 MedtronicSpider 实例"""
        return cls(company_name, base_url, company_url, cwiq_code)

    def make_request(self, method, url, company_name, requset_count, **kwargs):
        """发送请求的通用方法"""
        kwargs['headers'] = self.headers
        return self.client.request(method, url, company_name, requset_count, log_ip=False, **kwargs)

    def get_total_pages(self):
        """获取总职位数"""
        params = {
            'pr': '0',
            'in_iframe': '1',
        }
        try:
            response = self.make_request('GET', self.base_url, self.company_name, requset_count=3, params=params)
            # 解析 HTML
            soup = BeautifulSoup(response.text, 'html.parser')

            div_tag = soup.find('div', class_='container-fluid iCIMS_SearchResultsHeader')
            if div_tag:
                h2_tag = div_tag.find('h2', class_='iCIMS_SubHeader iCIMS_SubHeader_Jobs')
                h2_html = str(h2_tag.text.strip())

                # 提取所有数字，取最后一个数字作为总页数
                numbers = re.findall(r'\d+', h2_html)
                total_pages = int(numbers[-1])
            else:
                total_pages = 0
            return total_pages
        except Exception as e:
            logging.error(f"Error getting total pages: {e}:{self.company_name}")
            return 0

    def parse_job(self, job_data):
        """解析职位数据"""
        try:
            if job_data:
                job_url = job_data.get("url", "")
                job_id = job_url .split("/")[-3]
                # 解析地点信息
                location = job_data.get("jobLocation", [])
                address = location[0].get("address", {}) if location else {}
                country = address.get("addressCountry", "") if address else ""
                country_code = self.country_utils.get_alpha3(country)
                city = address.get("addressLocality", "") if address else ""
                state = address.get("addressRegion", "") if address else ""

                return {
                    "jobId": job_id,
                    "company": self.company_name,
                    "cwiq_code": self.cwiq_code,
                    "companyUrl": self.company_url,
                    "country": country_code,
                    "city": city,
                    "jobTitle": job_data.get("title", ""),
                    # "department": job_info.get("hiringOrganization", {}).get("name", ""),
                    "location": state,
                    "jobDescription": job_data.get("description", ""),
                    "jobUrl": job_url,
                    "created_time": None,
                    "updated_time": None,
                    "post_time": datetime.now(),
                }
        except Exception as e:
            logging.error(f"Error parsing job data: {e} for {self.company_name}")
            return None

    def fetch_job_details(self, external_path):
        """获取职位详细信息"""
        try:
            time.sleep(random.uniform(0.5, 1))
            params = {
                'in_iframe': 1,
            }
            response = self.make_request('GET', external_path, self.company_name, requset_count=3, params=params)
            # 解析 HTML
            soup = BeautifulSoup(response.text, 'html.parser')

            # 查找包含数据的 <script> 标签
            script_tag = soup.find('script', type='application/ld+json')
            json_content = script_tag.text.strip()  # 自动去除<script>标签并保留内部文本
            job_data = json.loads(json_content)

            return job_data
        except Exception as e:
            logging.error(f"Error fetching job details for {self.company_name}: {e}")
            return None

    def fetch_jobs(self,max_workers):
        """获取所有职位信息"""
        try:
            total_pages = self.get_total_pages()
            if total_pages == 0:
                logging.info(f"No jobs found {self.company_name}")
                return [],0

            logging.info(f"Found {total_pages} total pages")
            # 获取上一批次的职位数据
            latest_batch = self.db_ops.get_latest_batch(self.company_url)
            batch_id = latest_batch.batchId if latest_batch else 0
            previous_job_ids = set(self.db_ops.get_job_ids_by_batch(
                self.company_url,
                batch_id,
                status=[0, 1]
            ))

            all_jobs = self._fetch_job_listings(total_pages)

            # 从现有职位中提取所有job_ids
            current_job_ids = {
                job.get('id', '')
                for job in all_jobs
            }

            # 找出重复的job_ids
            repeated_job_ids = current_job_ids.intersection(previous_job_ids)
            new_job_ids = current_job_ids - previous_job_ids

            # 一次性获取所有重复职位的数据
            repeated_jobs_dict = self.db_ops.get_jobs_by_ids(list(repeated_job_ids))

            # 分离新职位和重复职位
            new_jobs = [
                job for job in all_jobs
                if job.get('id', '') in new_job_ids
            ]
            repeated_jobs = list(repeated_jobs_dict.values())

            logging.info(f"Found {len(new_jobs)} new jobs and {len(repeated_jobs)} repeated jobs")

            # 只处理新职位的详细信息
            processed_jobs = []
            if new_jobs:
                with ThreadPoolExecutor(max_workers=max_workers) as executor:
                    future_to_job = {
                        executor.submit(self.fetch_job_details, job.get('externalPath', '')): job
                        for job in new_jobs
                    }

                    completed = 0
                    total_new = len(new_jobs)
                    for future in as_completed(future_to_job):
                        job_details = future.result()
                        if job_details:
                            parsed_job = self.parse_job(job_details)
                            if parsed_job:
                                processed_jobs.append(parsed_job)
                        completed += 1
                        progress = (completed / total_new) * 100
                        logging.info(f"Processing new job details: {completed}/{total_new} ({progress:.2f}%)")

            # 合并新处理的职位和重复的职位
            all_processed_jobs = processed_jobs + repeated_jobs
            logging.info(f"Successfully processed {len(all_processed_jobs)} total jobs")
            return all_processed_jobs,"no total_jobs"

        except Exception as e:
            logging.error(f"Error in fetch_jobs: {e}:{self.company_name}")
            return [],0

    def _fetch_job_listings(self, total_pages):
        """获取所有职位列表"""
        try:
            is_first = 1
            total_requests = total_pages
            all_jobs = []

            with ThreadPoolExecutor(max_workers=25) as executor:
                futures = []
                for i in range(total_requests):
                    time.sleep(1)
                    params = {
                        'pr': i,
                        'in_iframe': '1',
                    }
                    futures.append(
                        executor.submit(
                            self.make_request,
                            'GET',
                            self.base_url,
                            self.company_name,
                            requset_count=3,
                            params=params,
                        )
                    )

                for future in as_completed(futures):
                    try:
                        response = future.result()
                        jobs = []
                        # 解析 HTML
                        soup = BeautifulSoup(response.text, 'html.parser')

                        # 查找包含数据的 <div> 标签
                        div_tags = soup.find_all('div', class_='col-xs-12 title')
                        for div_tag in div_tags:
                            a_tag = div_tag.find('a')  # 查找 <a> 标签
                            externalPath = a_tag['href'].split("?")[0]
                            jobs.append(externalPath)

                        simplified_jobs = [_simplify_job_data(job) for job in jobs]
                        all_jobs.extend(simplified_jobs)
                        # 不同网站每页工作数量不一样，计算第一页工作数量确定该网站每页工作数量
                        if is_first == 1:
                            jobs_per_request = len(all_jobs)
                            is_first = 0
                        now_page = (len(all_jobs) // jobs_per_request) + (1 if len(all_jobs) % jobs_per_request else 0)
                        logging.info(f"Progress: {now_page}/{total_pages} pages fetched")
                    except Exception as e:
                        logging.error(f"Error processing job batch: {e}:{self.company_name}")

            return all_jobs
        except Exception as e:
            logging.error(f"Error processing company: {e}:{self.company_name}")

    def __del__(self):
        """确保 HTTP 客户端被正确关闭"""
        if hasattr(self, 'client'):
            self.client.close()
