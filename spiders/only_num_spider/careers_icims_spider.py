import json
import random
import time
from datetime import datetime
import logging
from urllib.parse import urlparse
from concurrent.futures import ThreadPoolExecutor, as_completed
from spiders.core_spider.base_spider import BaseSpider
from utils.country_utils import CountryCodeUtils
from utils.http_utils.http_client import HTTPClient
from bs4 import BeautifulSoup
import re
from lxml import etree

class CareersIcimsSpider(BaseSpider):
    def __init__(self, company_name, base_url, company_url, cwiq_code):
        super().__init__(company_name, base_url, company_url, cwiq_code)
        self.company_name = company_name
        self.company_url = company_url
        self.cwiq_code = cwiq_code
        self.country_utils = CountryCodeUtils()
        self.headers = {
            'accept': 'application/json, text/plain, */*',
            'accept-language': 'zh-CN,zh;q=0.9',
            'cache-control': 'no-cache',
            'pragma': 'no-cache',
            'priority': 'u=1, i',
            'referer': 'https://careers.icims.com/careers-home/jobs?limit=100&page=1',
            'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"Windows"',
            'sec-fetch-dest': 'empty',
            'sec-fetch-mode': 'cors',
            'sec-fetch-site': 'same-origin',
            'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            # 'cookie': 'icimsCookiesEnabledCheck=1; s_plt=5.37; s_pltp=undefined; AMCVS_88CA58E65A265B560A495E1E%40AdobeOrg=1; at_check=true; AMCVS_615F35385CE886010A495ED3%40AdobeOrg=1; s_ips=1078; s_cc=true; AMCV_615F35385CE886010A495ED3%40AdobeOrg=*********%7CMCIDTS%7C20215%7CMCMID%7C90345956111187136019096912521314036414%7CMCOPTOUT-1746504159s%7CNONE%7CvVersion%7C5.5.0; _fbp=fb.1.*************.836287074380938432; s_tp=3436; s_ppv=Trustmark%2520-%2520Banking%252C%2520Loans%252C%2520Mortgage%252C%2520and%2520Wealth%2C100%2C31%2C3436%2C3%2C3; s_nr365=*************-New; DaysSinceLastVisit=*************; AMCV_88CA58E65A265B560A495E1E%40AdobeOrg=*********%7CMCIDTS%7C20215%7CMCMID%7C67891950394373699313876841910621437747%7CMCOPTOUT-1746515825s%7CNONE%7CvVersion%7C5.5.0; i18n=en-US; searchSource=external; jrasession=82f901e2-61d0-4be2-91e1-eacc08fd5c92; jasession=s%3A4t58nrNPfa0uCg6Og_P5FMttTnEoFtB_.2Gf%2FaePlbUqU%2FXqPN8TDRUtbVSt2M1bd%2FXStVnmjBxM; OptanonConsent=isGpcEnabled=0&datestamp=Thu+May+08+2025+11%3A05%3A30+GMT%2B0800+(%E4%B8%AD%E5%9B%BD%E6%A0%87%E5%87%86%E6%97%B6%E9%97%B4)&version=202410.1.0&browserGpcFlag=0&isIABGlobal=false&hosts=&consentId=519bdabc-f772-4e25-86fe-d80bac3df691&interactionCount=1&isAnonUser=1&landingPath=NotLandingPage&groups=C0001%3A1%2CC0002%3A1%2CC0004%3A1&AwaitingReconsent=false; _janalytics_id.fc22=e84229b6-54e5-42e9-b4fe-d3a540e6a86b.**********.1.**********.**********.41bd8004-6d2d-4ad2-98ce-eaee24fe685c',
        }

        # 创建 HTTP 客户端
        self.client = HTTPClient()

    @classmethod
    def create_spider(cls, company_name, base_url, company_url, cwiq_code):
        """工厂方法创建 MedtronicSpider 实例"""
        return cls(company_name, base_url, company_url, cwiq_code)

    def make_request(self, method, url, company_name,requset_count, **kwargs):
        """发送请求的通用方法"""
        kwargs['headers'] = self.headers
        return self.client.request(method, url, company_name, requset_count,log_ip=False, **kwargs)

    def get_total_jobs(self):
        try:
            params = {
                'limit': '100',
                'page': '1',
                'sortBy': 'relevance',
                'descending': 'false',
                'internal': 'false',
            }
            response = self.make_request('GET', self.base_url, self.company_name, requset_count=3,params=params)
            total = response.json()['totalCount']
            return total
        except Exception as e:
            logging.error(f"Error getting total jobs: {e} for {self.company_name}")
            return 0

    def parse_job(self, job_data):
        """解析职位数据"""
        pass

    def fetch_jobs(self,max_workers):
        """获取职位信息"""
        try:
            total_jobs = self.get_total_jobs()
            if total_jobs == 0:
                logging.info(f"No jobs found {self.company_name}")
                return [], 0

            logging.info(f"Found {total_jobs} total jobs")
            return [], total_jobs
        except Exception as e:
            logging.error(f"Error in fetch_jobs: {e}:{self.company_name}")
            return [],0

    def __del__(self):
        """确保 HTTP 客户端被正确关闭"""
        if hasattr(self, 'client'):
            self.client.close()
