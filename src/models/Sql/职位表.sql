CREATE TABLE jobs (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '自增主键ID',
    company VARCHAR(255) COMMENT '公司名称',
    cwiq_code VARCHAR(255) COMMENT '公司编码',
    companyUrl VARCHAR(255) COMMENT '公司url',
    jobId VARCHAR(255) COMMENT '职位Id',
    jobTitle LONGTEXT COMMENT '职位名称',
    department VARCHAR(255) COMMENT '职位部门',
    country VARCHAR(255)  COMMENT '国家',
    city VARCHAR(255) COMMENT '城市',
    location LONGTEXT COMMENT '职位所在区域',
    jobDescription LONGTEXT COMMENT '职位描述',
    post_time DATETIME COMMENT '上架时间',
    inactive_time DATETIME COMMENT '下线时间',
    created_time DATETIME COMMENT '创建时间',
    updated_time DATETIME COMMENT '修改时间',
    deleted_time DATETIME COMMENT '删除时间',
    jobRawData VARCHAR(255) COMMENT '原生数据',
    jobUrl LONGTEXT COMMENT '职位url',
    jobStatus INT COMMENT '状态描述，记录各工作职位的状态信息，status中：1表示新增，-1表示删除，0表示未发生变化，-2表示因爬取失败未爬取到的',
    batchId INT COMMENT '批次号',
    scrape_date DATE NOT NULL COMMENT '爬取日期',
    scrape_time TIME NOT NULL COMMENT '爬取时间',
    jobRepost INT COMMENT '历史状态描述，判断新增数据是否历史发布过，jobRepost中：如果新增数据历史发布过为1，否则为0',
    week_jobRepost INT COMMENT '历史状态描述，判断新增数据一周内是否发布过，week_jobRepost中：如果新增数据一周内发布过为1，否则为0',
    day_jobRepost INT COMMENT '历史状态描述，判断新增数据一天内是否发布过，day_jobRepost中：如果新增数据一天内发布过为1，否则为0',
    last_batch_repost INT COMMENT '历史状态描述，判断新增数据前两批次是否发布过，last_batch_repost中：如果新增数据前两批次发布过为1，否则为0'
);

CREATE INDEX idx_job_company_batch_status
ON jobs (companyUrl, batchId, jobStatus);

CREATE INDEX idx_job_id_batch_status
ON jobs (jobId,batchId, jobStatus);

CREATE INDEX idx_job_scrape_date
ON jobs (scrape_date);

CREATE INDEX idx_job_batch_scrape_date
    ON jobs (companyUrl,batchId);