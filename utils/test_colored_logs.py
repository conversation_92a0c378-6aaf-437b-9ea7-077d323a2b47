#!/usr/bin/env python3
"""
彩色日志测试脚本 - test_colored_logs.py

测试 Marzaha 爬虫系统的彩色日志功能
展示不同类型的日志输出效果和颜色分类
"""

import os
import sys
import json
import time

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utils.colored_logger import (
    log_success, log_io_error, log_parse_error, log_info, log_normal,
    log_spider_result, log_database_operation, log_file_operation, log_exception
)


def test_basic_colored_logs():
    """测试基础彩色日志功能"""
    print("\n" + "=" * 60)
    print("测试基础彩色日志功能")
    print("=" * 60)
    
    # 测试成功信息（绿色）
    log_success("爬虫执行成功完成")
    log_success("数据库连接建立成功")
    log_success("文件写入操作完成")
    
    # 测试IO错误（红色）
    log_io_error("数据库连接失败: Connection refused")
    log_io_error("文件写入权限错误: Permission denied")
    log_io_error("磁盘空间不足: No space left on device")
    
    # 测试解析错误（黄色）
    log_parse_error("HTML页面解析失败: Invalid HTML structure")
    log_parse_error("JSON数据解析错误: Unexpected token")
    log_parse_error("XML格式错误: Missing closing tag")

    # 测试正常输出（蓝色）
    log_normal("系统正在初始化...")
    log_normal("开始处理爬虫任务")
    log_normal("等待网络响应")
    
    # 测试一般信息（白色）
    log_info("其他系统信息")
    log_info("调试信息")


def test_spider_result_logs():
    """测试爬虫结果日志"""
    print("\n" + "=" * 60)
    print("测试爬虫结果日志")
    print("=" * 60)
    
    # 测试成功结果
    log_spider_result("Microsoft", "success")
    log_spider_result("Google", "success")
    
    # 测试不同类型的错误
    log_spider_result("Amazon", "error", "database connection failed")
    log_spider_result("Tesla", "error", "html parse error")
    log_spider_result("Apple", "error", "json decode error")
    log_spider_result("Meta", "error", "file write permission denied")
    
    # 测试超时状态
    log_spider_result("Netflix", "timeout", "request timeout after 30s")
    log_spider_result("Spotify", "warning", "slow response detected")


def test_database_operation_logs():
    """测试数据库操作日志"""
    print("\n" + "=" * 60)
    print("测试数据库操作日志")
    print("=" * 60)
    
    # 测试成功的数据库操作
    log_database_operation("insert", True, "Successfully inserted 1000 records")
    log_database_operation("update", True, "Updated 500 job records")
    log_database_operation("delete", True, "Cleaned up old batch data")
    
    # 测试失败的数据库操作
    log_database_operation("connect", False, "Connection timeout")
    log_database_operation("query", False, "Table 'jobs' doesn't exist")
    log_database_operation("backup", False, "Insufficient disk space")


def test_file_operation_logs():
    """测试文件操作日志"""
    print("\n" + "=" * 60)
    print("测试文件操作日志")
    print("=" * 60)
    
    # 测试成功的文件操作
    log_file_operation("create", "spider_results.json", True)
    log_file_operation("upload", "batch_data.parquet", True)
    log_file_operation("backup", "database_dump.sql", True)
    
    # 测试失败的文件操作
    log_file_operation("write", "large_file.dat", False, "Disk space insufficient")
    log_file_operation("read", "missing_file.txt", False, "File not found")


def test_exception_handling():
    """测试异常处理日志"""
    print("\n" + "=" * 60)
    print("测试异常处理日志")
    print("=" * 60)
    
    # 模拟不同类型的异常
    import json
    
    # JSON 错误 - 黄色
    try:
        json.loads("invalid json")
    except json.JSONDecodeError as e:
        log_exception("JSON parsing failed", e)
    
    # 文件错误 - 红色
    try:
        open("/nonexistent/file.txt", "r")
    except FileNotFoundError as e:
        log_exception("File operation failed", e)
    
    # 权限错误 - 红色
    try:
        open("/root/restricted.txt", "w")
    except PermissionError as e:
        log_exception("Permission denied", e)
    
    # SQL 错误 - 黄色（模拟）
    class MockSQLError(Exception):
        pass
    
    try:
        raise MockSQLError("Table 'jobs' doesn't exist")
    except MockSQLError as e:
        log_exception("Database query failed", e)
    
    # 超时错误 - 蓝色（模拟）
    import concurrent.futures
    try:
        raise concurrent.futures.TimeoutError("Request timeout after 30s")
    except concurrent.futures.TimeoutError as e:
        log_exception("Network request timeout", e)


def test_mixed_scenario():
    """测试混合场景"""
    print("\n" + "=" * 60)
    print("测试混合场景 - 模拟真实爬虫执行")
    print("=" * 60)
    
    # 模拟爬虫执行流程
    log_normal("Starting spider batch processing...")
    time.sleep(0.5)
    
    log_success("Loaded 2131 spider configurations")
    time.sleep(0.5)
    
    log_normal("Processing spiders: 1/2131")
    log_normal("Processing spiders: 500/2131")
    log_normal("Processing spiders: 1000/2131")
    time.sleep(0.5)
    
    # 模拟一些错误
    log_parse_error("JSON decode error in response from api.example.com")
    log_io_error("Database connection lost, retrying...")
    log_success("Database connection restored")
    time.sleep(0.5)
    
    log_normal("Processing spiders: 1500/2131")
    log_normal("Processing spiders: 2000/2131")
    log_normal("Processing spiders: 2131/2131")
    time.sleep(0.5)
    
    log_success("All processing completed: 2050/2131 spiders succeeded")


def main():
    """主函数"""
    print("🎨 Marzaha 爬虫系统彩色日志测试")
    print("📋 颜色说明:")
    print("   🔴 红色 - IO错误（文件、数据库、权限等错误）")
    print("   🟢 绿色 - 正确结果")
    print("   🟡 黄色 - SQL和JSON解析错误")
    print("   🔵 蓝色 - 正常输出（进度、状态等）")
    print("   ⚪ 白色 - 其他信息")
    print()
    
    # 运行所有测试
    test_basic_colored_logs()
    test_spider_result_logs()
    test_database_operation_logs()
    test_file_operation_logs()
    test_exception_handling()
    test_mixed_scenario()

    print("\n" + "=" * 60)
    print("✅ 彩色日志测试完成")
    print("💡 提示: 在支持ANSI颜色的终端中可以看到彩色效果")
    print("📁 日志文件: /mnt/data1/leo/spider_colored.log")
    print("=" * 60)


if __name__ == "__main__":
    main()
