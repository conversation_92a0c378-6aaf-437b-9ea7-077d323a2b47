import csv
import os
import time
from datetime import datetime, timedelta

now = datetime.now()
if now.minute < 30:
    start_time = now.replace(minute=0, second=0, microsecond=0)
else:
    start_time = now.replace(minute=30, second=0, microsecond=0)
date_str = start_time.strftime('%Y%m%d')
time_str = start_time.strftime('%H%M')
over_time = start_time + timedelta(minutes=30)
over_time_str = over_time.strftime('%H%M')


def append_to_csv(company_name, cnt_failed_jobs, cnt_failed_page, first_jobs, first_page, second_jobs, second_page,
                  model):
    # 确保目标目录存在
    file_dir = os.path.dirname(f'./static/retry_size{date_str}_{time_str}_{over_time_str}.csv')
    if not os.path.exists(file_dir):
        os.makedirs(file_dir)  # 创建目录

    file_path = f'./static/retry_size{date_str}_{time_str}_{over_time_str}.csv'
    file_exists = os.path.isfile(file_path)

    # 获取现有数据中的最大ID值
    if file_exists:
        with open(file_path, mode='r', newline='', encoding='utf-8') as file:
            reader = csv.reader(file)
            next(reader)  # 跳过表头
            existing_ids = [int(row[0]) for row in reader]
            next_id = max(existing_ids, default=0) + 1  # 如果没有数据，默认ID从1开始
    else:
        next_id = 1  # 如果文件不存在，ID从1开始

    # 将新数据追加到文件
    with open(file_path, mode='a', newline='', encoding='utf-8') as file:
        writer = csv.writer(file)

        # 如果文件不存在，则写入表头
        if not file_exists:
            writer.writerow(
                ['id', 'companyname', 'cnt_failed_jobs', 'cnt_failed_page', 'first_jobs', 'first_page', 'second_jobs',
                 'second_page', 'model'])

        # 写入新的一行数据
        writer.writerow(
            [next_id, company_name, cnt_failed_jobs, cnt_failed_page, first_jobs, first_page, second_jobs, second_page,
             model])

def append_to_detail_csv(company_name, first,  second, final_num, model):
    # 确保目标目录存在
    file_dir = os.path.dirname(f'./static/retry_detail_size{date_str}_{time_str}_{over_time_str}.csv')
    if not os.path.exists(file_dir):
        os.makedirs(file_dir)  # 创建目录

    file_path = f'./static/retry_size{date_str}_{time_str}_{over_time_str}.csv'
    file_exists = os.path.isfile(file_path)

    # 获取现有数据中的最大ID值
    if file_exists:
        with open(file_path, mode='r', newline='', encoding='utf-8') as file:
            reader = csv.reader(file)
            next(reader)  # 跳过表头
            existing_ids = [int(row[0]) for row in reader]
            next_id = max(existing_ids, default=0) + 1  # 如果没有数据，默认ID从1开始
    else:
        next_id = 1  # 如果文件不存在，ID从1开始

    # 将新数据追加到文件
    with open(file_path, mode='a', newline='', encoding='utf-8') as file:
        writer = csv.writer(file)

        # 如果文件不存在，则写入表头
        if not file_exists:
            writer.writerow(
                ['id', 'companyname', 'first', 'second', 'final_num', 'model'])

        # 写入新的一行数据
        writer.writerow(
            [next_id, company_name, first, second, final_num, model])
