#!/usr/bin/env python3
"""
新配置系统测试脚本 - test_new_config.py

测试从 settings.ini 读取配置的功能
"""

import os
import sys

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_config_loader():
    """测试配置加载器"""
    print("🔧 测试配置加载器")
    print("=" * 50)
    
    try:
        from utils.config_loader import ConfigLoader, get_mysql_config, get_logger_config
        
        # 测试配置加载器
        loader = ConfigLoader()
        
        print("✅ 配置加载器导入成功")
        
        # 测试 MySQL 配置
        mysql_config = get_mysql_config()
        print(f"MySQL 配置: {mysql_config['host']}:{mysql_config['port']}")
        
        # 测试日志配置
        logger_config = get_logger_config()
        print(f"日志级别: {logger_config['CONSOLE_LEVEL']}")
        print(f"日志文件: {logger_config['DEFAULT_LOG_FILE']}")
        
        return True
        
    except Exception as e:
        print(f"❌ 配置加载器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_logger_with_config():
    """测试日志系统使用新配置"""
    print("\n🎨 测试日志系统")
    print("=" * 50)
    
    try:
        # 直接导入日志函数
        from utils.colored_logger import log_info, log_success, log_error, log_warn, log_hint
        
        print("✅ 日志系统导入成功")
        
        # 测试各种级别的日志
        log_info("这是 INFO 级别的日志")
        log_hint("这是 HINT 级别的日志")
        log_success("这是 SUCCESS 级别的日志")
        log_warn("这是 WARN 级别的日志")
        log_error("这是 ERROR 级别的日志")
        
        return True
        
    except Exception as e:
        print(f"❌ 日志系统测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_no_settings_import():
    """测试是否避免了直接导入 settings"""
    print("\n🚫 测试避免导入 settings")
    print("=" * 50)
    
    try:
        # 检查是否有直接导入 settings 的模块
        import sys
        settings_modules = [name for name in sys.modules.keys() if 'settings' in name and 'config' in name]
        
        if settings_modules:
            print(f"⚠️  发现 settings 相关模块: {settings_modules}")
        else:
            print("✅ 没有直接导入 config.settings 模块")
        
        return True
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return False

def main():
    """主函数"""
    print("📝 Marzaha 新配置系统测试")
    print("🎯 目标:")
    print("   1. 避免直接导入 config.settings")
    print("   2. 从 config/settings.ini 读取配置")
    print("   3. settings.ini 已添加到 .gitignore")
    print()
    
    success_count = 0
    total_tests = 3
    
    if test_config_loader():
        success_count += 1
    
    if test_logger_with_config():
        success_count += 1
    
    if test_no_settings_import():
        success_count += 1
    
    print("\n" + "=" * 50)
    print(f"测试结果: {success_count}/{total_tests} 通过")
    
    if success_count == total_tests:
        print("✅ 所有测试通过")
        print("💡 新配置系统功能:")
        print("   - 从 settings.ini 读取配置")
        print("   - 避免直接导入 settings 模块")
        print("   - settings.ini 不会被提交到 git")
        print("   - 提供 settings.ini.example 作为模板")
    else:
        print("❌ 部分测试失败")

if __name__ == "__main__":
    main()
