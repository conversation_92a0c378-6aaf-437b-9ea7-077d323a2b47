# utils/proxy_config.py
from dataclasses import dataclass
from typing import Dict, Optional
import os
from urllib.parse import quote


@dataclass
class ProxyCredential:
    host: str
    port: str
    username: str
    password: str

    @property
    def proxy_url(self) -> str:
        """生成带认证的代理URL"""
        auth_string = f"{quote(self.username)}:{quote(self.password)}"
        return f"http://{auth_string}@{self.host}:{self.port}"

    @property
    def proxies(self) -> Dict[str, str]:
        """生成代理配置字典"""
        proxy_url = self.proxy_url
        return {
            "http": proxy_url,
            "https": proxy_url
        }


class ProxyManager:
    """代理配置管理类"""
    # 默认代理配置
    DEFAULT_PROXY = ProxyCredential(
        host="q592.kdlfps.com",
        port="18866",
        username="f2268223529",
        password="p118z73x"
    )

    def __init__(self):
        self._current_proxy = None

    @property
    def current_proxy(self) -> ProxyCredential:
        """获取当前代理配置"""
        if self._current_proxy is None:
            # 优先使用环境变量中的配置
            proxy_host = os.getenv("PROXY_HOST")
            proxy_port = os.getenv("PROXY_PORT")
            proxy_user = os.getenv("PROXY_USERNAME")
            proxy_pass = os.getenv("PROXY_PASSWORD")

            if all([proxy_host, proxy_port, proxy_user, proxy_pass]):
                self._current_proxy = ProxyCredential(
                    host=proxy_host,
                    port=proxy_port,
                    username=proxy_user,
                    password=proxy_pass
                )
            else:
                # 使用默认配置
                self._current_proxy = self.DEFAULT_PROXY

        return self._current_proxy

    def set_proxy(self, credential: ProxyCredential):
        """设置新的代理配置"""
        self._current_proxy = credential

    def get_proxies(self) -> Dict[str, str]:
        """获取代理配置字典"""
        return self.current_proxy.proxies


# 创建全局代理管理器实例
proxy_manager = ProxyManager()