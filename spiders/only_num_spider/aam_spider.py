import json
import random
import time
from datetime import datetime
import logging
from urllib.parse import urlparse
from concurrent.futures import ThreadPoolExecutor, as_completed
from spiders.core_spider.base_spider import BaseSpider
from utils.country_utils import CountryCodeUtils
from utils.http_utils.http_client import HTTPClient
from bs4 import BeautifulSoup
import re
from lxml import etree
class AamSpider(BaseSpider):
    def __init__(self, company_name, base_url, company_url, cwiq_code):
        super().__init__(company_name, base_url, company_url, cwiq_code)
        self.company_name = company_name
        self.company_url = company_url
        self.cwiq_code = cwiq_code
        self.country_utils = CountryCodeUtils()
        self.headers = {
    'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
    'accept-language': 'zh-CN,zh;q=0.9',
    'cache-control': 'max-age=0',
    'priority': 'u=0, i',
    'referer': 'https://careers.aam.com/en',
    'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"',
    'sec-ch-ua-mobile': '?0',
    'sec-ch-ua-platform': '"Windows"',
    'sec-fetch-dest': 'document',
    'sec-fetch-mode': 'navigate',
    'sec-fetch-site': 'same-origin',
    'sec-fetch-user': '?1',
    'upgrade-insecure-requests': '1',
    'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36',
    # 'cookie': 'SearchVisitorId=766e0102-6d2b-a8c9-eb3e-376d118617aa; CookieBehavior=US|Implied; SearchSessionId={%22SearchSessionId%22:%226041e860-ddb1-abf3-fa69-240ce20a90b2%22%2C%22ImpressionParentId%22:%22%22%2C%22ViewParentId%22:%22%22%2C%22GoogleSearchRequestId%22:%22%22%2C%22GoogleJobId%22:%22%22%2C%22Created%22:%221747990026173%22}; CookieConsent=0|1|2|3; searchFormCookie=all locations; PersonalizationCookie=[{%22Locations%22:[{%22Path%22:%226252001-5332921-5368381-5385393%22%2C%22FacetType%22:4%2C%22GeolocationLatitude%22:34.2077%2C%22GeolocationLongitude%22:-118.51%2C%22LocationName%22:%22Quartz%2520Hill%252C%2520CA%22%2C%22GeoType%22:%22ipambientonly%22%2C%22SetByHtml5%22:false}]%2C%22Categories%22:[]%2C%22PersonalizationType%22:0%2C%22DateCreated%22:%222025-05-23T08:47:06.912Z%22%2C%22CustomFacets%22:[]%2C%22TenantId%22:44944%2C%22OnetCode%22:null%2C%22Served%22:true}%2C{%22Locations%22:[{%22Path%22:%22ALL%22%2C%22FacetType%22:4}]%2C%22Categories%22:[]%2C%22PersonalizationType%22:1%2C%22DateCreated%22:%222025-05-23T08:47:18.583Z%22%2C%22CustomFacets%22:[{%22CustomFacetValue%22:%22Full%20time%22%2C%22CustomFacetTerm%22:%22custom_fields.FullTimePartTime%22}]%2C%22TenantId%22:44944}]',
}
        # 创建 HTTP 客户端
        self.client = HTTPClient()

    @classmethod
    def create_spider(cls, company_name, base_url, company_url, cwiq_code):
        """工厂方法创建 MedtronicSpider 实例"""
        return cls(company_name, base_url, company_url, cwiq_code)

    def make_request(self, method, url, company_name, requset_count, **kwargs):
        """发送请求的通用方法"""
        kwargs['headers'] = self.headers
        return self.client.request(method, url, company_name, requset_count, log_ip=False, **kwargs)

    def get_total_jobs(self):
        """获取总职位数"""
        try:
            response = self.make_request('GET', self.base_url, self.company_name, requset_count=3)
            tree = etree.HTML(response.text)
            total = tree.xpath(
                "//body[@id='search']/div[@id='page']/main[@id='content']/div[@id='search-results-wrap']/div/section[@id='search-results']/p//text()")[
                0]
            result = re.findall(r'\d+', total)
            total = int(result[0])
            return total
        except Exception as e:
            logging.error(f"Error getting total jobs: {e} for {self.company_name}")
            return 0

    def parse_job(self, job_data):
        """解析职位数据"""
        pass

    def fetch_jobs(self,max_workers):
        """获取所有职位信息"""
        try:
            total_jobs = self.get_total_jobs()
            if total_jobs == 0:
                logging.info(f"No jobs found {self.company_name}")
                return [], 0

            logging.info(f"Found {total_jobs} total jobs")

            return [], total_jobs

        except Exception as e:
            logging.error(f"Error in fetch_jobs: {e}:{self.company_name}")
            return [], 0


    def __del__(self):
        """确保 HTTP 客户端被正确关闭"""
        if hasattr(self, 'client'):
            self.client.close()

