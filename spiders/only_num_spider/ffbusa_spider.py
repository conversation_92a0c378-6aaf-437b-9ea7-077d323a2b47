import json
import random
import time
from datetime import datetime
import logging
from urllib.parse import urlparse
from concurrent.futures import ThreadPoolExecutor, as_completed
from spiders.core_spider.base_spider import BaseSpider
from utils.country_utils import CountryCodeUtils
from utils.http_utils.http_client import HTTPClient
from bs4 import BeautifulSoup
import re
from lxml import etree
class FfbusaSpider(BaseSpider):
    def __init__(self, company_name, base_url, company_url, cwiq_code):
        super().__init__(company_name, base_url, company_url, cwiq_code)
        self.company_name = company_name
        self.company_url = company_url
        self.cwiq_code = cwiq_code
        self.country_utils = CountryCodeUtils()
        self.headers = {
    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
    'Accept-Language': 'zh-CN,zh;q=0.9',
    'Cache-Control': 'max-age=0',
    'Connection': 'keep-alive',
    'Sec-Fetch-Dest': 'document',
    'Sec-Fetch-Mode': 'navigate',
    'Sec-Fetch-Site': 'none',
    'Sec-Fetch-User': '?1',
    'Upgrade-Insecure-Requests': '1',
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36',
    'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"',
    'sec-ch-ua-mobile': '?0',
    'sec-ch-ua-platform': '"Windows"',
    # 'Cookie': 'JSESSIONID=A8EAF1193FAED0EAA0AF4EE6077339CF; ak_bmsc=E02BB2E06280A0FA3BA2D93179EA52AD~000000000000000000000000000000~YAAQrPTVF++C78aWAQAA09H91xtl1akfGRDFlWBKXKOQldPgIwvkILIYlAk8tjC/BfXNZRTIRQNLoYlLzp+VraBSrGo6qAA9EXMGiTHt5ij8fhgJ4vPypCQOFG+NqhBoKcVA3UBR1LqzIeUMh7c+yWbd5yggL0SetzEyJVAMF6PedWXr1iorWaSidbQXb1f7HqfRvl8NckByGox2a47FjbJQCe72H7Sn7zlP9wR3CxzCiMfKkuyIVpn4ETvfrOIqDX68vkXwHnpdYbv7mveqmqi6qkm1IjnjcQ+Qv04mSzNPOfIT5QiBO5SE940hfvSpJt7Ukg5SpBO5jg+en8/jZzwcXVH/mFko0A/AIM6mpLhyWoOJvSwZwfk0wkZYjBku; bm_sv=534C8114DC866BFA83FD90BECD12B485~YAAQrPTVF4wN8MaWAQAAe6AF2Bt9kgfOlrr5hk6R2W5XZjTYjCjJ7VPF/poVaVTfvGDDW2Hqc4bYZTZxQ1LUc/6LVXBy1mX4Mr52xhAKLV69ahinWjWoPYYmyvTK7l8GheQeh3ytWoD9r9wtCbG3NdRfN1Q3LEW7taOZLlCuEIbNPfusBvUWxs1GD14VZTnJ526DUS8b51GX9ZBCX/IPgVQD6kA+bb/8Vm72ZqtT7W7qdtzDrs3YUyGsYlxra5BA9kVQ~1',
}

        # 创建 HTTP 客户端
        self.client = HTTPClient()

    @classmethod
    def create_spider(cls, company_name, base_url, company_url, cwiq_code):
        """工厂方法创建 MedtronicSpider 实例"""
        return cls(company_name, base_url, company_url, cwiq_code)

    def make_request(self, method, url, company_name, requset_count, **kwargs):
        """发送请求的通用方法"""
        kwargs['headers'] = self.headers
        return self.client.request(method, url, company_name, requset_count, log_ip=False, **kwargs)

    def get_total_jobs(self):
        """获取总职位数"""
        try:
            params = {
                'org': 'FFBUSA',
                'cws': '42',
            }
            if self.company_name == "ARLO TECHNOLOGIES INC":
                params = {
                    'org': 'NETGINC',
                    'cws': '37',
                }
            response = self.make_request('GET', self.base_url, self.company_name, requset_count=3, params=params)
            tree = etree.HTML(response.text)
            total = tree.xpath("//div[@class='panel-body']/span[@class='oracletaleocwsv2-panel-number']//text()")[0]
            return total
        except Exception as e:
            logging.error(f"Error getting total jobs: {e} for {self.company_name}")
            return 0

    def parse_job(self, job_data):
        """解析职位数据"""
        pass

    def fetch_jobs(self,max_workers):
        """获取所有职位信息"""
        try:
            total_jobs = self.get_total_jobs()
            if total_jobs == 0:
                logging.info(f"No jobs found {self.company_name}")
                return [], 0

            logging.info(f"Found {total_jobs} total jobs")

            return [], total_jobs

        except Exception as e:
            logging.error(f"Error in fetch_jobs: {e}:{self.company_name}")
            return [], 0


    def __del__(self):
        """确保 HTTP 客户端被正确关闭"""
        if hasattr(self, 'client'):
            self.client.close()

