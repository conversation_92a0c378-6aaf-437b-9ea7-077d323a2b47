#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Marzaha 爬虫系统备份管理器
自动备份数据库和配置文件
"""

import os
import subprocess
import shutil
import logging
import time
import gzip
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Optional
import yaml

class BackupManager:
    """备份管理器"""
    def __init__(self, config: Optional[Dict] = None):
        self.config = config or self.load_default_config()
        self.backup_dir = Path(self.config.get('backup_dir', '/mnt/data1/backups'))
        self.backup_dir.mkdir(parents=True, exist_ok=True)
        
        # 设置日志
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)
    
    def load_default_config(self) -> Dict:
        """加载默认配置"""
        try:
            # 尝试从配置文件加载数据库配置
            config_files = [
                'config/database.yaml',
                'config/settings.py',
                'config/mysql_config.yaml'
            ]
            
            for config_file in config_files:
                if os.path.exists(config_file):
                    if config_file.endswith('.yaml'):
                        with open(config_file, 'r') as f:
                            return yaml.safe_load(f)
                    elif config_file.endswith('.py'):
                        # 简单解析 Python 配置文件
                        return self.parse_python_config(config_file)
            
            # 默认配置
            return {
                'host': 'localhost',
                'port': 3306,
                'user': 'root',
                'password': '',
                'database': 'marzaha',
                'backup_dir': '/mnt/data1/backups',
                'retention_days': 7
            }
        except Exception as e:
            self.logger.error(f"Error loading config: {e}")
            return {}
    
    def parse_python_config(self, config_file: str) -> Dict:
        """解析 Python 配置文件"""
        config = {}
        try:
            with open(config_file, 'r') as f:
                content = f.read()
                # 简单的变量提取
                for line in content.split('\n'):
                    if '=' in line and not line.strip().startswith('#'):
                        key, value = line.split('=', 1)
                        key = key.strip()
                        value = value.strip().strip('"\'')
                        if key.upper() in ['HOST', 'USER', 'PASSWORD', 'DATABASE', 'PORT']:
                            config[key.lower()] = value
        except Exception as e:
            self.logger.error(f"Error parsing Python config: {e}")
        return config
    
    def create_database_backup(self) -> Optional[str]:
        """创建数据库备份"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_filename = f"marzaha_db_backup_{timestamp}.sql"
            backup_path = self.backup_dir / backup_filename
            
            # 构建 mysqldump 命令
            cmd = [
                "mysqldump",
                f"--host={self.config.get('host', 'localhost')}",
                f"--port={self.config.get('port', 3306)}",
                f"--user={self.config.get('user', 'root')}",
                "--single-transaction",
                "--routines",
                "--triggers",
                "--add-drop-table",
                "--add-locks",
                "--create-options",
                "--disable-keys",
                "--extended-insert",
                "--quick",
                "--lock-tables=false"
            ]
            
            # 添加密码（如果有）
            password = self.config.get('password')
            if password:
                cmd.append(f"--password={password}")
            
            # 添加数据库名
            database = self.config.get('database', 'marzaha')
            cmd.append(database)
            
            self.logger.info(f"Creating database backup: {backup_path}")
            
            # 执行备份
            with open(backup_path, 'w') as f:
                result = subprocess.run(cmd, stdout=f, stderr=subprocess.PIPE, text=True)
            
            if result.returncode != 0:
                self.logger.error(f"mysqldump failed: {result.stderr}")
                if backup_path.exists():
                    backup_path.unlink()
                return None
            
            # 压缩备份文件
            compressed_path = self.compress_file(backup_path)
            if compressed_path:
                backup_path.unlink()  # 删除未压缩的文件
                self.logger.info(f"Database backup created: {compressed_path}")
                return str(compressed_path)
            else:
                self.logger.info(f"Database backup created: {backup_path}")
                return str(backup_path)
                
        except Exception as e:
            self.logger.error(f"Error creating database backup: {e}")
            return None
    
    def create_config_backup(self) -> Optional[str]:
        """创建配置文件备份"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_filename = f"marzaha_config_backup_{timestamp}.tar.gz"
            backup_path = self.backup_dir / backup_filename
            
            # 要备份的目录和文件
            backup_items = [
                'config/',
                'requirements*.txt',
                'Makefile',
                'spider.py',
                'monitor_run.sh'
            ]
            
            # 创建临时目录
            temp_dir = self.backup_dir / f"temp_config_{timestamp}"
            temp_dir.mkdir(exist_ok=True)
            
            try:
                # 复制文件到临时目录
                for item in backup_items:
                    if os.path.exists(item):
                        if os.path.isdir(item):
                            shutil.copytree(item, temp_dir / item, dirs_exist_ok=True)
                        else:
                            shutil.copy2(item, temp_dir / item)
                
                # 创建压缩包
                shutil.make_archive(
                    str(backup_path.with_suffix('')), 
                    'gztar', 
                    str(temp_dir)
                )
                
                self.logger.info(f"Config backup created: {backup_path}")
                return str(backup_path)
                
            finally:
                # 清理临时目录
                if temp_dir.exists():
                    shutil.rmtree(temp_dir)
                    
        except Exception as e:
            self.logger.error(f"Error creating config backup: {e}")
            return None
    
    def compress_file(self, file_path: Path) -> Optional[Path]:
        """压缩文件"""
        try:
            compressed_path = file_path.with_suffix(file_path.suffix + '.gz')
            
            with open(file_path, 'rb') as f_in:
                with gzip.open(compressed_path, 'wb') as f_out:
                    shutil.copyfileobj(f_in, f_out)
            
            return compressed_path
        except Exception as e:
            self.logger.error(f"Error compressing file {file_path}: {e}")
            return None
    
    def create_full_backup(self) -> Dict[str, Optional[str]]:
        """创建完整备份"""
        self.logger.info("Starting full backup...")
        
        results = {
            'database': self.create_database_backup(),
            'config': self.create_config_backup(),
            'timestamp': datetime.now().isoformat()
        }
        
        success_count = sum(1 for v in results.values() if v is not None and v != results['timestamp'])
        self.logger.info(f"Full backup completed: {success_count}/2 successful")
        
        return results
    
    def cleanup_old_backups(self, retention_days: Optional[int] = None) -> int:
        """清理旧备份"""
        if retention_days is None:
            retention_days = self.config.get('retention_days', 7)
        
        cutoff_date = datetime.now() - timedelta(days=retention_days)
        cutoff_timestamp = cutoff_date.timestamp()
        
        deleted_count = 0
        
        try:
            for backup_file in self.backup_dir.glob("marzaha_*_backup_*"):
                if backup_file.stat().st_mtime < cutoff_timestamp:
                    backup_file.unlink()
                    deleted_count += 1
                    self.logger.info(f"Deleted old backup: {backup_file.name}")
            
            self.logger.info(f"Cleanup completed: {deleted_count} old backups deleted")
            
        except Exception as e:
            self.logger.error(f"Error during cleanup: {e}")
        
        return deleted_count
    
    def list_backups(self) -> List[Dict]:
        """列出所有备份"""
        backups = []
        
        try:
            for backup_file in sorted(self.backup_dir.glob("marzaha_*_backup_*")):
                stat = backup_file.stat()
                
                backup_info = {
                    'filename': backup_file.name,
                    'path': str(backup_file),
                    'size': stat.st_size,
                    'size_mb': round(stat.st_size / 1024 / 1024, 2),
                    'created': datetime.fromtimestamp(stat.st_mtime).isoformat(),
                    'age_days': (time.time() - stat.st_mtime) / 86400
                }
                
                # 判断备份类型
                if '_db_backup_' in backup_file.name:
                    backup_info['type'] = 'database'
                elif '_config_backup_' in backup_file.name:
                    backup_info['type'] = 'config'
                else:
                    backup_info['type'] = 'unknown'
                
                backups.append(backup_info)
        
        except Exception as e:
            self.logger.error(f"Error listing backups: {e}")
        
        return backups
    
    def restore_database(self, backup_file: str) -> bool:
        """恢复数据库"""
        try:
            backup_path = Path(backup_file)
            if not backup_path.exists():
                backup_path = self.backup_dir / backup_file
            
            if not backup_path.exists():
                self.logger.error(f"Backup file not found: {backup_file}")
                return False
            
            # 如果是压缩文件，先解压
            if backup_path.suffix == '.gz':
                temp_file = backup_path.with_suffix('')
                with gzip.open(backup_path, 'rb') as f_in:
                    with open(temp_file, 'wb') as f_out:
                        shutil.copyfileobj(f_in, f_out)
                sql_file = temp_file
            else:
                sql_file = backup_path
            
            # 构建 mysql 命令
            cmd = [
                "mysql",
                f"--host={self.config.get('host', 'localhost')}",
                f"--port={self.config.get('port', 3306)}",
                f"--user={self.config.get('user', 'root')}",
                self.config.get('database', 'marzaha')
            ]
            
            # 添加密码（如果有）
            password = self.config.get('password')
            if password:
                cmd.append(f"--password={password}")
            
            self.logger.info(f"Restoring database from: {sql_file}")
            
            # 执行恢复
            with open(sql_file, 'r') as f:
                result = subprocess.run(cmd, stdin=f, stderr=subprocess.PIPE, text=True)
            
            # 清理临时文件
            if sql_file != backup_path and sql_file.exists():
                sql_file.unlink()
            
            if result.returncode == 0:
                self.logger.info("Database restore completed successfully")
                return True
            else:
                self.logger.error(f"Database restore failed: {result.stderr}")
                return False
                
        except Exception as e:
            self.logger.error(f"Error restoring database: {e}")
            return False
    
    def get_backup_status(self) -> Dict:
        """获取备份状态"""
        backups = self.list_backups()
        
        # 统计信息
        total_size = sum(b['size'] for b in backups)
        db_backups = [b for b in backups if b['type'] == 'database']
        config_backups = [b for b in backups if b['type'] == 'config']
        
        # 最新备份
        latest_db = max(db_backups, key=lambda x: x['created']) if db_backups else None
        latest_config = max(config_backups, key=lambda x: x['created']) if config_backups else None
        
        return {
            'backup_dir': str(self.backup_dir),
            'total_backups': len(backups),
            'total_size_mb': round(total_size / 1024 / 1024, 2),
            'database_backups': len(db_backups),
            'config_backups': len(config_backups),
            'latest_database_backup': latest_db,
            'latest_config_backup': latest_config,
            'retention_days': self.config.get('retention_days', 7)
        }

def main():
    """主函数 - 可以作为独立脚本运行"""
    import argparse
    
    parser = argparse.ArgumentParser(description='Marzaha Backup Manager')
    parser.add_argument('action', choices=['backup', 'list', 'cleanup', 'status', 'restore'], 
                       help='Action to perform')
    parser.add_argument('--type', choices=['database', 'config', 'full'], default='full',
                       help='Backup type')
    parser.add_argument('--file', help='Backup file for restore')
    parser.add_argument('--days', type=int, help='Retention days for cleanup')
    
    args = parser.parse_args()
    
    backup_manager = BackupManager()
    
    if args.action == 'backup':
        if args.type == 'database':
            result = backup_manager.create_database_backup()
            print(f"Database backup: {result}")
        elif args.type == 'config':
            result = backup_manager.create_config_backup()
            print(f"Config backup: {result}")
        else:
            result = backup_manager.create_full_backup()
            print(f"Full backup: {result}")
    
    elif args.action == 'list':
        backups = backup_manager.list_backups()
        print(f"{'Type':<10} {'Filename':<40} {'Size (MB)':<10} {'Created':<20}")
        print("-" * 80)
        for backup in backups:
            print(f"{backup['type']:<10} {backup['filename']:<40} {backup['size_mb']:<10} {backup['created']:<20}")
    
    elif args.action == 'cleanup':
        deleted = backup_manager.cleanup_old_backups(args.days)
        print(f"Deleted {deleted} old backups")
    
    elif args.action == 'status':
        status = backup_manager.get_backup_status()
        print("Backup Status:")
        for key, value in status.items():
            print(f"  {key}: {value}")
    
    elif args.action == 'restore':
        if not args.file:
            print("Error: --file required for restore")
            return
        success = backup_manager.restore_database(args.file)
        print(f"Restore {'successful' if success else 'failed'}")

if __name__ == "__main__":
    main()
