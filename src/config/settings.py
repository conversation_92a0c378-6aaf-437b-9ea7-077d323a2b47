import logging

BROKER_URL = 'redis://localhost:6379/0'
CELERY_RESULT_BACKEND = 'redis://localhost:6379/1'

MYSQL_CONFIG = {
    'host': 'localhost',
    'user': 'root',
    'password': '123456',
    'database': 'crawler_leo',
    'port': 3306
}

# Logger 配置
LOGGER_CONFIG = {
    # 自定义日志级别配置 (从低到高: INFO -> HINT -> WARN -> ERROR)
    'CONSOLE_LEVEL': 'INFO',           # 控制台输出级别（INFO及以上会输出到屏幕）
    'FILE_LEVEL': 'INFO',              # 文件输出级别（INFO及以上写入文件）

    # 默认日志文件路径
    'DEFAULT_LOG_FILE': '/mnt/data1/leo/spider_colored.log',

    # 日志格式配置
    'LOG_FORMAT': '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    'DATE_FORMAT': '%Y-%m-%d %H:%M:%S',

    # 文件模式配置
    'DEFAULT_LOG_ADD': False,          # 默认重新创建文件（False）还是追加（True）

    # 自定义日志级别映射 (数值越大级别越高)
    'CUSTOM_LEVELS': {
        'INFO': 10,      # 一般信息 - 白色
        'HINT': 20,      # 正常输出 - 蓝色
        'WARN': 30,      # 解析错误 - 黄色
        'ERROR': 40,     # IO错误 - 红色
        'SUCCESS': 25,   # 成功信息 - 绿色 (总是显示)
    },

    # 级别说明
    'LEVEL_DESCRIPTION': {
        'INFO': 'INFO - 一般信息（白色）',
        'HINT': 'HINT - 正常输出（蓝色）',
        'WARN': 'WARN - 解析错误（黄色）',
        'ERROR': 'ERROR - IO错误（红色）',
        'SUCCESS': 'SUCCESS - 成功信息（绿色，总是显示）'
    }
}
