import logging

import boto3
from botocore.exceptions import ClientError
import os
from typing import List, Dict, Optional


class S3Uploader:
    def __init__(self):
        """初始化 S3 上传工具类"""
        self.bucket = "time-series-quant-east"
        self.s3_client = boto3.client(
            's3',
            aws_access_key_id="********************",
            aws_secret_access_key="bS7KEqfDRv0NhB6HIYfVuUMfVor7r6ki9O0LWzyU",
            region_name="us-east-1"
        )

    def upload_file(self, file_path: str, s3_folder: Optional[str] = None) -> bool:
        """
        上传文件到S3指定文件夹

        Args:
            file_path: 本地文件路径
            s3_folder: S3目标文件夹，例如 'all_companies_info'

        Returns:
            bool: 上传是否成功
        """
        try:
            if not os.path.exists(file_path):
                logging.info(f"文件不存在：{file_path}")
                return False

            file_name = os.path.basename(file_path)
            # 构建S3路径
            s3_key = f"{s3_folder}/{file_name}" if s3_folder else file_name

            self.s3_client.upload_file(file_path, self.bucket, s3_key)
            logging.info(f"文件上传成功：{s3_key}")
            return True
        except ClientError as e:
            logging.info(f"上传失败：{e}")
            return False

    def list_files(self, folder: Optional[str] = None) -> List[Dict]:
        """
        列出存储桶或指定文件夹中的文件

        Args:
            folder: S3文件夹路径，例如 'all_companies_info'

        Returns:
            List[Dict]: 文件信息列表
        """
        try:
            # 构建查询参数
            params = {'Bucket': self.bucket}
            if folder:
                params['Prefix'] = f"{folder}/"

            response = self.s3_client.list_objects_v2(**params)
            files = []
            if 'Contents' in response:
                for obj in response['Contents']:
                    files.append({
                        'key': obj['Key'],
                        'size': obj['Size'],
                        'last_modified': obj['LastModified']
                    })
                    logging.info(f"- {obj['Key']} ({obj['Size']} 字节)")
            return files
        except ClientError as e:
            logging.info(f"无法列出文件：{e}")
            return []

    def list_folders(self) -> List[str]:
        """
        列出存储桶中的所有文件夹

        Returns:
            List[str]: 文件夹列表
        """
        try:
            response = self.s3_client.list_objects_v2(
                Bucket=self.bucket,
                Delimiter='/'
            )
            folders = []

            # 获取公共前缀（文件夹）
            if 'CommonPrefixes' in response:
                for prefix in response['CommonPrefixes']:
                    folder_name = prefix['Prefix'].rstrip('/')
                    folders.append(folder_name)
                    print(f"- {folder_name}/")

            return folders
        except ClientError as e:
            logging.info(f"无法列出文件夹：{e}")
            return []

if __name__ == "__main__":
    # 配置日志
    logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s")

    # 初始化 S3Uploader
    s3_uploader = S3Uploader()

    # 创建本地测试文件
    test_file = "test_upload.txt"
    with open(test_file, "w") as f:
        f.write("This is a test file for S3 upload.")

    # 上传文件到 S3 test_folder 目录
    s3_folder = "crawl_data/test_folder"
    upload_success = s3_uploader.upload_file(test_file, s3_folder)

    if upload_success:
        logging.info(f"✅ 文件 {test_file} 成功上传到 S3 文件夹 {s3_folder}")
    else:
        logging.error(f"❌ 文件 {test_file} 上传失败")

    # 清理本地测试文件
    os.remove(test_file)
