import json
import random
import time
from datetime import datetime
import logging
from urllib.parse import urlparse
from concurrent.futures import ThreadPoolExecutor, as_completed
from spiders.core_spider.base_spider import BaseSpider
from utils.country_utils import CountryCodeUtils
from utils.http_utils.http_client import HTTPClient
from bs4 import BeautifulSoup
import re
from lxml import etree
class ConsolidatedSpider(BaseSpider):
    def __init__(self, company_name, base_url, company_url, cwiq_code):
        super().__init__(company_name, base_url, company_url, cwiq_code)
        self.company_name = company_name
        self.company_url = company_url
        self.cwiq_code = cwiq_code
        self.country_utils = CountryCodeUtils()
        self.headers = {
    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
    'Accept-Language': 'zh-CN,zh;q=0.9',
    'Cache-Control': 'max-age=0',
    'Connection': 'keep-alive',
    'Referer': 'https://careers.consolidated.com/psc/CAREERS/EMPLOYEE/CAREERS/c/HRS_HRAM_FL.HRS_CG_SEARCH_FL.GBL?Page=HRS_APP_SCHJOB_FL&Action=U',
    'Sec-Fetch-Dest': 'document',
    'Sec-Fetch-Mode': 'navigate',
    'Sec-Fetch-Site': 'same-origin',
    'Sec-Fetch-User': '?1',
    'Upgrade-Insecure-Requests': '1',
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36',
    'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"',
    'sec-ch-ua-mobile': '?0',
    'sec-ch-ua-platform': '"Windows"',
    # 'Cookie': 'ca1psuxwsp03-8100-PORTAL-PSJSESSIONID=tmTWzydsTwYHbEyNajne5GoANGP0rIlZ!-1803692770; ExpirePage=https://careers.consolidated.com/psc/CAREERS/; PS_LOGINLIST=https://careers.consolidated.com/CAREERS; PS_TOKEN=qgAAAAQDAgEBAAAAvAIAAAAAAAAsAAAABABTaGRyAk4Adwg4AC4AMQAwABQVgLaVzc3AkuDrgWS8SS27g4rdkWoAAAAFAFNkYXRhXnicLYpLDkBAEAXLEEtLtyAzjHABn5UI9iLiAkRczuH0iH559WrRNxD4yvNkH8V38cbKwS45OLmkzsKanpZoYKJhZqFjxGZoMgoSWUfzsyIVK4WW/HP3Z4QaXpCxDlw=; PS_TokenSite=https://careers.consolidated.com/psc/CAREERS/?ca1psuxwsp03-8100-PORTAL-PSJSESSIONID; SignOnDefault=; PS_LASTSITE=https://careers.consolidated.com/psc/CAREERS/; ps_theme=node:HRMS portal:EMPLOYEE theme_id:DEFAULT_THEME_FLUID_GUEST_CG css:XPT_DEFAULT_THEME_FLUID_CG css_f:XPT_CG_FLUID_TEMPLATE accessibility:N macroset:XPT_CG_MACROSET formfactor:3 piamode:2; PS_DEVICEFEATURES=width:2560 height:1440 pixelratio:1 touch:0 geolocation:1 websockets:1 webworkers:1 datepicker:1 dtpicker:1 timepicker:1 dnd:1 sessionstorage:1 localstorage:1 history:1 canvas:1 svg:1 postmessage:1 hc:0 maf:0; PS_TOKENEXPIRE=16_May_2025_01:57:05_GMT; psback=%22%22url%22%3A%22https%3A%2F%2Fcareers.consolidated.com%2Fpsc%2FCAREERS%2FEMPLOYEE%2FCAREERS%2Fc%2FHRS_HRAM_FL.HRS_CG_SEARCH_FL.GBL%3Fpage%3DHRS_APP_SCHJOB_FL%22%20%22label%22%3A%22Search%20Jobs%22%20%22origin%22%3A%22PIA%22%20%22layout%22%3A%221%22%20%22refurl%22%3A%22https%3A%2F%2Fcareers.consolidated.com%2Fpsc%2FCAREERS%2FEMPLOYEE%2FCAREERS%22%22',
}

        # 创建 HTTP 客户端
        self.client = HTTPClient()

    @classmethod
    def create_spider(cls, company_name, base_url, company_url, cwiq_code):
        """工厂方法创建 MedtronicSpider 实例"""
        return cls(company_name, base_url, company_url, cwiq_code)

    def make_request(self, method, url, company_name, requset_count, **kwargs):
        """发送请求的通用方法"""
        kwargs['headers'] = self.headers
        return self.client.request(method, url, company_name, requset_count, log_ip=False, **kwargs)

    def get_total_jobs(self):
        """获取总职位数"""
        try:
            params = {
                'Page': 'HRS_APP_SCHJOB_FL',
                'Action': 'U',
            }
            response = self.make_request('GET', self.base_url, self.company_name, requset_count=3,params=params)
            tree = etree.HTML(response.text)
            total = tree.xpath(
                "//div[@id='win0div$ICField16']/div[@id='win0div$ICField28']/div[@id='win0div$ICField55']/div[@id='win0divHRS_SCH_WRK_FLU_HRS_GRPBOX_18']/div[@id='win0divHRS_SCH_WRK_FLU_HRS_SES_CNTS_MSG']/div[@class='ps-htmlarea']/b//text()")[
                0]
            return total
        except Exception as e:
            logging.error(f"Error getting total jobs: {e} for {self.company_name}")
            return 0

    def parse_job(self, job_data):
        """解析职位数据"""
        pass

    def fetch_jobs(self,max_workers):
        """获取所有职位信息"""
        try:
            total_jobs = self.get_total_jobs()
            if total_jobs == 0:
                logging.info(f"No jobs found {self.company_name}")
                return [], 0

            logging.info(f"Found {total_jobs} total jobs")

            return [], total_jobs

        except Exception as e:
            logging.error(f"Error in fetch_jobs: {e}:{self.company_name}")
            return [], 0


    def __del__(self):
        """确保 HTTP 客户端被正确关闭"""
        if hasattr(self, 'client'):
            self.client.close()

