from urllib.parse import urlparse, urljoin

from spiders.core_spider.base_spider import Base<PERSON>pider
from utils.country_utils import CountryCodeUtils
import logging
from concurrent.futures import ThreadPoolExecutor, as_completed
import random
import time
from datetime import datetime

from utils.http_utils.http_client import HTTPClient

class PfizerSpider(BaseSpider):
    def __init__(self, company_name, base_url, company_url, cwiq_code):
        super().__init__(company_name, base_url, company_url, cwiq_code)
        self.company_name = company_name
        self.company_url = company_url
        self.cwiq_code = cwiq_code
        self.country_utils = CountryCodeUtils()

        self.headers = {
            'accept': '*/*',
            'accept-language': 'zh-CN,zh;q=0.9',
            'content-type': 'application/json',
            'origin': 'https://www.pfizer.com',
            'priority': 'u=1, i',
            'referer': 'https://www.pfizer.com/about/careers/search-results',
            'sec-ch-ua': '"Not(A:Brand";v="99", "Microsoft Edge";v="133", "Chromium";v="133"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"Windows"',
            'sec-fetch-dest': 'empty',
            'sec-fetch-mode': 'cors',
            'sec-fetch-site': 'same-origin',
            'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
        }
        self.url = "https://my.adp.com/myadp_prefix/mycareer/public/staffing/v1/job-requisitions/apply-custom-filters?$select=reqId,jobTitle,publishedJobTitle,type,jobDescription,jobQualifications,workLocations,workLevelCode,clientRequisitionID,postingDate,requisitionLocations"
        self.apply_base_url = "https://www.pfizer.com"
        # 创建 HTTP 客户端，使用全局代理配置
        self.client = HTTPClient()

    @classmethod
    def create_spider(cls, company_name, base_url, company_url, cwiq_code):
        """工厂方法创建 MedtronicSpider 实例"""
        return cls(company_name, base_url, company_url, cwiq_code)

    def make_request(self, method, url,company_name, requset_count, **kwargs):
        """发送请求的通用方法"""
        kwargs['headers'] = self.headers
        return self.client.request(method, url, company_name, requset_count, log_ip=False, **kwargs)

    def get_total_pages(self):
        """获取总翻页数"""
        json = {
            'keywords': '',
            'pager': '10',
            'sort': 'latest',
            'page': 1,
            'langcode': 'en',
        }
        try:
            response = self.make_request('POST', self.base_url, self.company_name, requset_count=3, json=json)
            return response.json()['data']['total_count']
        except Exception as e:
            logging.error(f"Error getting total pages: {e} for {self.company_name}")
            return 0

    def parse_job(self, job_data):
        """解析职位数据"""
        # 解析地点信息
        try:
            if job_data:
                location = job_data.get("locations", "")
                location = location.split(';')[-1].strip()
                # 替换 en dash（–）为标准的 hyphen（-）
                location = location.replace("–", "-")
                # 如果包含 "-"，则按 "-" 分割
                if "-" in location:
                    parts = location.split("-")
                else:
                    # 如果没有 "-"，假设只有 country 或 country + city
                    parts = [location]
                # 处理分割后的情况
                if len(parts) == 3:
                    country = parts[0].strip()
                    city = parts[1].strip()
                    state = parts[2].strip()
                elif len(parts) == 2:
                    country = parts[0].strip()
                    city = parts[1].strip()
                    state = ""
                else:
                    country = parts[0].strip()
                    city = ""
                    state = ""
                country_code = self.country_utils.get_alpha3(country)

                return {
                    "jobId": job_data.get("jobRequisitionId", ""),
                    "company": self.company_name,
                    "cwiq_code": self.cwiq_code,
                    "companyUrl": self.company_url,
                    "country": country_code,
                    "city": city,
                    "jobTitle": job_data.get("title", ""),
                    # "department": job_info.get("hiringOrganization", {}).get("name", ""),
                    "location": state,
                    "jobDescription": job_data.get("jobDescription", ""),
                    "jobUrl": self.apply_base_url + job_data.get("detailPageUrl", ""),
                    "created_time": None,
                    "updated_time": None,
                    "post_time": datetime.now(),
                }
        except Exception as e:
            logging.error(f"Error parsing job data: {e} for {self.company_name}")
            return None

    def fetch_jobs(self,max_workers):
        """获取所有职位信息"""
        try:
            # 获取翻页总数量
            total_pages = self.get_total_pages()
            if total_pages == 0:
                logging.info(f"No jobs found {self.company_name}")
                return [],0

            logging.info(f"Found {total_pages} total pages")

            # 遍历每一页
            all_jobs = []
            processed_jobs = []

            with ThreadPoolExecutor(max_workers=25) as executor:
                futures = []
                # 提交所有请求任务
                for i in range(total_pages):
                    json_payload = {
                        'keywords': '',
                        'pager': '10',
                        'sort': 'latest',
                        'page': i + 1,
                        'langcode': 'en',
                    }
                    futures.append(
                        executor.submit(
                            self.make_request,
                            'POST',
                            self.base_url,
                            self.company_name,
                            requset_count=3,
                            json=json_payload
                        )
                    )

                # 处理所有任务的结果
                completed_pages = 0
                for future in as_completed(futures):
                    try:
                        response = future.result()
                        jobs = response.json()['data']['data']
                        all_jobs.extend(jobs)

                        # 解析每条数据
                        for job in jobs:
                            parsed_job = self.parse_job(job)
                            if parsed_job:
                                processed_jobs.append(parsed_job)

                        completed_pages += 1
                        progress = (completed_pages / total_pages) * 100
                        logging.info(f"Progress: {completed_pages}/{total_pages} pages fetched ({progress:.2f}%)")
                    except Exception as e:
                        logging.error(f"Error processing job batch for {self.company_name}: {e}")

            logging.info(f"Successfully fetched {len(processed_jobs)} jobs")
            return processed_jobs,"no total_jobs"

        except Exception as e:
            logging.error(f"Error in fetch_jobs: {e}:{self.company_name}")
            return [],0

    def __del__(self):
        """确保 HTTP 客户端被正确关闭"""
        if hasattr(self, 'client'):
            self.client.close()
