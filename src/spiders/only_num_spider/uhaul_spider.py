import json
import random
import time
from datetime import datetime
import logging
from urllib.parse import urlparse
from concurrent.futures import ThreadPoolExecutor, as_completed
from spiders.core_spider.base_spider import BaseSpider
from utils.country_utils import CountryCodeUtils
from utils.http_utils.http_client import HTTPClient
from bs4 import BeautifulSoup
import re
from lxml import etree
class UhaulSpider(BaseSpider):
    def __init__(self, company_name, base_url, company_url, cwiq_code):
        super().__init__(company_name, base_url, company_url, cwiq_code)
        self.company_name = company_name
        self.company_url = company_url
        self.cwiq_code = cwiq_code
        self.country_utils = CountryCodeUtils()
        self.headers = {
    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
    'Accept-Language': 'zh-CN,zh;q=0.9',
    'Cache-Control': 'no-cache',
    'Connection': 'keep-alive',
    'Content-Type': 'multipart/form-data; boundary=----WebKitFormBoundaryXkbqVAZAk7Pv2Hwp',
    'Origin': 'https://jobs.uhaul.com',
    'Pragma': 'no-cache',
    'Referer': 'https://jobs.uhaul.com/OpenJobs',
    'Sec-Fetch-Dest': 'document',
    'Sec-Fetch-Mode': 'navigate',
    'Sec-Fetch-Site': 'same-origin',
    'Upgrade-Insecure-Requests': '1',
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"',
    'sec-ch-ua-mobile': '?0',
    'sec-ch-ua-platform': '"Windows"',
    # 'Cookie': 'ASP.NET_SessionId=eupxtzdhoxppvsp5xo1a0wch; rxVisitor=17482491301880IEI8OAA9TR1RC4GDAUIEBR5INBJ0UT0; __RequestVerificationToken=59T0E47SedrEFv2G1JhFPaa0cv0gqaKxmsqrZvw-0UbZUSxnYlUar_yoCdVXsr2v0ZRkeLtPfq_9Y8Rtk__L1tO3XVwXQElU4yGmp8PV9S81; volumeControl_volumeValue=0; dtCookie=v_4_srv_2_sn_6FEB6BC996459D1EF5B26594F3AC8199_perc_100000_ol_0_mul_1_app-3A2f20672b91e39dfc_1_app-3A404a5fe93e2fd6e6_1; dtPC=2$450642453_120h1vFGRHKVDPKURHBLMCDNLLPFNMWTFABERI-0e0; rxvt=1748252442459|1748249130191; BIGipServerUHIHRWEB.uhaul.net_80=!FOL/PO6yS0oQvbvtCDnGuz25/V+A6k5FS1R/06opuWLDFOGCSosuCx08Cd586iNfuy8WnSVGPZU7BEc=; dtSa=true%7CS%7C-1%7C-%7C-%7C1748250642806%7C450642453_120%7Chttps%3A%2F%2Fjobs.uhaul.com%2FOpenJobs%3Fpage%3D2%26PageChange%3Dtrue%26IsRemote%3DFalse%26JobCategory%3DAny%26JobTypes%3DAny%26SortOrder%3DRelevance%26SearchDistance%3D20%7C%7C%7C%7C',
}

        # 创建 HTTP 客户端
        self.client = HTTPClient()

    @classmethod
    def create_spider(cls, company_name, base_url, company_url, cwiq_code):
        """工厂方法创建 MedtronicSpider 实例"""
        return cls(company_name, base_url, company_url, cwiq_code)

    def make_request(self, method, url, company_name, requset_count, **kwargs):
        """发送请求的通用方法"""
        kwargs['headers'] = self.headers
        return self.client.request(method, url, company_name, requset_count, log_ip=False, **kwargs)

    def get_total_jobs(self):
        """获取总职位数"""
        try:
            files = {
                'SearchTerms': (None, ''),
                'Location': (None, ''),
                'IsRemote': (None, 'false'),
                'JobTypes': (None, 'Any'),
                'JobCategory': (None, 'Any'),
                'cachedSubCategory': (None, ''),
                'SubCategory': (None, ''),
                'HiddenAction': (None, '/OpenJobs/SubCategories'),
                'SearchDistance': (None, '20'),
                'page': (None, '1'),
                'SortOrder': (None, 'Relevance'),
                'changeSorting': (None, 'true'),
            }
            response = self.make_request('POST', self.base_url, self.company_name, requset_count=3,files=files,)
            tree = etree.HTML(response.text)
            total = \
            tree.xpath("//div[@class='medium-8 columns']/div[@class='jobs-container']/div[@class='job-count']//text()")[
                0]
            m = re.search(r'of\s+(\d+)', total)
            total = int(m.group(1))
            return total
        except Exception as e:
            logging.error(f"Error getting total jobs: {e} for {self.company_name}")
            return 0

    def parse_job(self, job_data):
        """解析职位数据"""
        pass

    def fetch_jobs(self,max_workers):
        """获取所有职位信息"""
        try:
            total_jobs = self.get_total_jobs()
            if total_jobs == 0:
                logging.info(f"No jobs found {self.company_name}")
                return [], 0

            logging.info(f"Found {total_jobs} total jobs")

            return [], total_jobs

        except Exception as e:
            logging.error(f"Error in fetch_jobs: {e}:{self.company_name}")
            return [], 0


    def __del__(self):
        """确保 HTTP 客户端被正确关闭"""
        if hasattr(self, 'client'):
            self.client.close()

