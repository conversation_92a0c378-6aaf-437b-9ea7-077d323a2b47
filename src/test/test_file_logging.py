#!/usr/bin/env python3
"""
文件日志测试脚本 - test_file_logging.py

测试文件日志功能
"""

import os
import sys
import tempfile

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_file_logging():
    """测试文件日志功能"""
    print("🎨 文件日志功能测试")
    print("=" * 50)
    
    try:
        from utils.colored_logger import (
            set_global_log_file, log_info, log_hint, log_warn, log_error, log_success
        )
        
        # 创建临时文件
        temp_file = tempfile.mktemp(suffix='.log', prefix='test_file_')
        print(f"测试文件: {temp_file}")
        
        # 设置文件日志，控制台级别为 WARN
        print("设置控制台级别为 WARN，文件记录所有级别")
        set_global_log_file(temp_file, log_add=False, console_level='WARN')
        
        print("\n测试输出（控制台只显示 WARN、ERROR 和 SUCCESS）:")
        log_info("INFO 级别 - 只写入文件")
        log_hint("HINT 级别 - 只写入文件")
        log_success("SUCCESS 级别 - 控制台和文件都有")
        log_warn("WARN 级别 - 控制台和文件都有")
        log_error("ERROR 级别 - 控制台和文件都有")
        
        print(f"\n文件内容 ({temp_file}):")
        with open(temp_file, 'r', encoding='utf-8') as f:
            lines = f.readlines()
            for i, line in enumerate(lines, 1):
                print(f"  {i:2d}: {line.strip()}")
        
        print(f"\n文件共有 {len(lines)} 行日志")
        
        # 测试追加模式
        print("\n测试追加模式:")
        set_global_log_file(temp_file, log_add=True, console_level='INFO')
        log_info("追加的 INFO 日志")
        log_error("追加的 ERROR 日志")
        
        print(f"\n追加后文件内容:")
        with open(temp_file, 'r', encoding='utf-8') as f:
            lines = f.readlines()
            for i, line in enumerate(lines, 1):
                print(f"  {i:2d}: {line.strip()}")
        
        print(f"\n文件现在共有 {len(lines)} 行日志")
        
        # 清理
        os.unlink(temp_file)
        print("✅ 文件日志功能测试成功")
        
    except Exception as e:
        print(f"❌ 文件日志功能测试失败: {e}")
        import traceback
        traceback.print_exc()

def test_file_write_failure():
    """测试文件写入失败的处理"""
    print("\n" + "=" * 50)
    print("测试文件写入失败处理")
    print("=" * 50)
    
    try:
        from utils.colored_logger import set_global_log_file, log_info, log_error
        
        # 尝试写入到不存在的目录
        invalid_file = "/nonexistent/directory/test.log"
        print(f"尝试设置无效文件: {invalid_file}")
        print("预期行为: 显示警告信息，继续使用控制台日志")
        
        set_global_log_file(invalid_file, log_add=False, console_level='INFO')
        
        print("\n测试日志输出:")
        log_info("测试信息 - 应该只在控制台显示")
        log_error("测试错误 - 应该只在控制台显示")
        
        print("✅ 文件写入失败处理测试成功")
        
    except Exception as e:
        print(f"❌ 文件写入失败处理测试失败: {e}")
        import traceback
        traceback.print_exc()

def main():
    """主函数"""
    test_file_logging()
    test_file_write_failure()
    
    print("\n" + "=" * 50)
    print("✅ 所有文件日志测试完成")
    print("💡 功能特性:")
    print("   - 支持文件和控制台双重输出")
    print("   - 控制台和文件可以设置不同的级别")
    print("   - 支持追加和重新创建模式")
    print("   - 文件写入失败时优雅降级")

if __name__ == "__main__":
    main()
