import json

from bs4 import BeautifulSoup

from spiders.core_spider.base_spider import Base<PERSON>pider
from utils.country_utils import CountryCodeUtils
import logging
from concurrent.futures import ThreadPoolExecutor, as_completed
import random
import time
from datetime import datetime
from urllib.parse import urlparse, parse_qs

from utils.http_utils.http_client import HTTPClient

def _simplify_job_data(job):
    """
    将原始职位数据简化为只包含必要信息的格式

    Args:
        job (dict): 原始职位数据

    Returns:
        dict: 简化后的职位数据，只包含ID和路径信息
    """
    try:
        externalPath = job.get("Link", "")
        parsed_url = urlparse(externalPath)
        params = parse_qs(parsed_url.query)
        jobid = params.get("jobid", [""])[0]
    except Exception as e:
        logging.error(f"Error parsing job data{e}: {job}")
        return {
            'id': None,  # REQ号作为ID
            'externalPath': None
        }
    return {
        'id': jobid,
        'externalPath': externalPath
    }


class BrassringSpider(BaseSpider):
    def __init__(self, company_name, base_url, company_url, cwiq_code):
        super().__init__(company_name, base_url, company_url, cwiq_code)
        self.company_name = company_name
        self.base_url = base_url
        self.company_url = company_url
        self.cwiq_code = cwiq_code
        self.country_utils = CountryCodeUtils()
        self.headers = {
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9',
            'Connection': 'keep-alive',
            'Content-Type': 'application/json;charset=UTF-8',
            'Sec-Fetch-Dest': 'empty',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Site': 'same-origin',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/134.0.0.0 Safari/537.36 Edg/134.0.0.0',
            'sec-ch-ua': '"Chromium";v="134", "Not:A-Brand";v="24", "Microsoft Edge";v="134"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"Windows"',
        }
        parsed_url = urlparse(self.company_url)
        query_params = parse_qs(parsed_url.query)

        self.partnerid = query_params.get("partnerid", [""])[0]
        self.siteid = query_params.get("siteid", [""])[0]

        # 创建 HTTP 客户端，使用全局代理配置
        self.client = HTTPClient()

    @classmethod
    def create_spider(cls, company_name, base_url, company_url, cwiq_code):
        """工厂方法创建 MedtronicSpider 实例"""
        return cls(company_name, base_url, company_url, cwiq_code)

    def make_request(self, method, url,company_name, requset_count, **kwargs):
        """发送请求的通用方法"""
        kwargs['headers'] = self.headers
        return self.client.request(method, url, company_name, requset_count, log_ip=False, **kwargs)

    def get_total_jobs(self):
        """获取总职位数"""
        try:
            json = {
                'partnerId': self.partnerid,
                'siteId': self.siteid,
                'keyword': '',
                'location': '',
                'keywordCustomSolrFields': 'FORMTEXT12,JobTitle,Location,AutoReq',
                'locationCustomSolrFields': 'Location',
                'linkId': '',
                'Latitude': 0,
                'Longitude': 0,
                'facetfilterfields': {
                    'Facet': [],
                },
                'powersearchoptions': {
                    'PowerSearchOption': [],
                },
                'SortType': 'LastUpdated',
                'pageNumber': 1,
                'encryptedSessionValue': '^A79xaodIcBuc50J1nbsNqHEal7mZVRL7QWUo5hNc_slp_rhc_8eAK3gfFZJOyLrgu5RReM2W6xhk8oyifgjyuZQv3LSJh_slp_rhc_uFhxXV5A2MoDgLqcYikAg=',
            }
            response = self.make_request('POST', self.base_url, self.company_name, requset_count=3, json=json)
            return response.json()['JobsCount']
        except Exception as e:
            logging.error(f"Error getting total jobs: {e} for {self.company_name}")
            return 0

    def parse_job(self, job_data):
        """解析职位数据"""
        try:
            if job_data:
                job_questions = job_data["JobDetailQuestions"]
                title_list = ["Title", "Job Title", "External Job Title", "Position Title", "Posting Title",
                              "Industry Job Title"]
                state_list = ["State", "State:", "State/Region/Province", "(For Retail Field Jobs Only) Store State",
                              "State/Province", "Site Location - State"]
                city_list = ["City", "Work Location - City", "(For Retail Field Jobs Only) Store City",
                             "City (or Nearest City)", "Site Location - City"]
                country_list = ["Country", "Country/Region"]
                description_list = ["Job Description", "Job Purpose", "Company Description", "Key Job Elements",
                                    "Education / Training", "Qualifications and Experience", "Benefits", "EEO",
                                    "Job Category", "Job Summary", "Minimum Job Qualifications",
                                    "Preferred Qualifications", "Disclaimer", "<!-- About ADM -->",
                                    "<!-- LinkedIn Location -->", "<!--Diversity & EEO Information-->\t",
                                    "<!--Benefits Information-->", "<!--Salaried Pay Range-->", "<!--Pay Range-->",
                                    "Company Summary", "Business Summary", "Position Responsibilities",
                                    "Qualifications", "Travel Requirements", "Applicant Privacy Notice",
                                    "Compensation", "Required Qualifications", "EEO Statement", "Competency Summary",
                                    "Company Overview", "Job Posting", "<b>About H&R Block...</b>",
                                    "<b>What you'll bring to the team...</b>", "Qualifications Required (Skills)",
                                    "Division Description", "Benefit Highlights", "Salary Range",
                                    "Basic Job Requirements", "Other Job Requirements", "To be Considered Candidates:",
                                    "Notice to Candidates:", "Experience Required", "Base Pay", "Type of Work",
                                    "Please Note:", "Pay Rate Type", "Employment Status", "Minimum Job Requirements",
                                    "About AMC", "Estimated Work Hours", "Benefit Summary", "Compensation Information",
                                    "Fair Chance Initiative for Hiring Ordinance", "Salary Minimum", "Salary Maximum",
                                    "-", "Accommodations", "Make an Impact", "Who We Are", "Your Skills",
                                    "Accommodations Statement", "Brand Image", "The Perks"]
                job_id = ""
                country = ""
                city = ""
                state = ""
                title = ""
                description = ""

                for item in job_questions:
                    if item.get("VerityZone") == "reqid":
                        job_id = item.get("AnswerValue", "")
                    if item.get("QuestionName") in title_list:
                        title = item.get("AnswerValue", "")
                    if item.get("QuestionName") in country_list:
                        country = item.get("AnswerValue", "")
                    if item.get("QuestionName") in city_list:
                        city = item.get("AnswerValue", "")
                    if item.get("QuestionName") in state_list:
                        state = item.get("AnswerValue", "")
                    if item.get("QuestionName") == "City and State":
                        location = item.get("AnswerValue", "")
                        parts = [p.strip() for p in location.split(",")]
                        city = parts[0] if len(parts) > 0 else ""
                        state = parts[1] if len(parts) > 1 else ""
                    if item.get("QuestionName") == "Job Location":
                        location = item.get("AnswerValue", "")
                        parts = [p.strip() for p in location.split(",")]
                        city = parts[0] if len(parts) > 0 else ""
                    if item.get("QuestionName") == "Work Location":
                        location = item.get("AnswerValue", "")
                        # 筛除工作方式
                        parts = [p.strip() for p in location.split("-")]
                        location = parts[0] if len(parts) > 0 else ""

                        parts = [p.strip() for p in location.split(",")]
                        locations = parts[0] if len(parts) > 0 else ""
                        part = [p.strip() for p in locations.split(" ")]
                        if len(part) > 1:
                            country = part[0]
                            city = part[1]
                            state = parts[1] if len(parts) > 1 else ""
                        else:
                            country = parts[0] if len(parts) > 0 else ""
                            city = parts[1] if len(parts) > 1 else ""
                    if item.get("QuestionName") == "Location - City, Region or Area":
                        location = item.get("AnswerValue", "")
                        # 筛除工作方式
                        parts = [p.strip() for p in location.split("-")]
                        if len(parts) > 1:
                            location = parts[1]
                            part = [p.strip() for p in location.split(",")]
                            city = part[0] if len(part) > 0 else ""
                            state = part[1] if len(part) > 1 else ""
                    if item.get("QuestionName") == "Store Address":
                        location = item.get("AnswerValue", "")
                        # 先以 " - " 分割，提取街道地址和地点信息
                        parts = [p.strip() for p in location.split(" - ")]
                        if len(parts) > 1:
                            location_info = parts[1]
                            # 对地点信息按逗号分割，得到城市和州+邮编部分
                            loc_parts = [p.strip() for p in location_info.split(",")]
                            city = loc_parts[0]
                            state_zip = loc_parts[1] if len(loc_parts) > 1 else ""
                            # 对州+邮编部分再按空格分割
                            state_zip_parts = state_zip.split()
                            state = state_zip_parts[0] if len(state_zip_parts) > 0 else ""
                    if item.get("QuestionName") == "Location":
                        location = item.get("AnswerValue", "")
                        parts = [p.strip() for p in location.split(" - ")]
                        if len(parts) > 1:
                            state = parts[0]
                            city = parts[1]
                        else:
                            city = parts[0]
                    if item.get("QuestionName") in description_list:
                        part_description = item.get("QuestionName") + ":" + item.get("AnswerValue", "")
                        description += part_description
                country_code = self.country_utils.get_alpha3(country)
                return {
                    "jobId": job_id,
                    "company": self.company_name,
                    "cwiq_code": self.cwiq_code,
                    "companyUrl": self.company_url,
                    "country": country_code,
                    "city": city,
                    "jobTitle": title,
                    # "department": job_info.get("hiringOrganization", {}).get("name", ""),
                    "location": state,
                    "jobDescription": description,
                    "jobUrl": job_data.get("Link", ""),
                    "created_time": None,
                    "updated_time": None,
                    "post_time": datetime.now(),
                }
        except Exception as e:
            logging.error(f"Error parsing job data: {e} for {self.company_name}")
            return None

    def fetch_job_details(self, externalPath):
        """获取职位详细信息"""
        try:
            time.sleep(random.uniform(0.5, 1))
            response = self.make_request('GET', externalPath, self.company_name, requset_count=3)
            soup = BeautifulSoup(response.text, 'html.parser')
            input_tag = soup.find('input', id='preLoadJSON')
            json_data = json.loads(input_tag['value'])
            job_details = json_data['Jobdetails']
            return job_details
        except Exception as e:
            logging.error(f"Error fetching job details for {self.company_name}: {e}")
            return None

    def fetch_jobs(self,max_workers):
        """获取所有职位信息"""
        try:
            total_jobs = self.get_total_jobs()
            if total_jobs == 0:
                logging.info(f"No jobs found {self.company_name}")
                return [],0

            logging.info(f"Found {total_jobs} total jobs")

            # 获取上一批次的职位数据
            latest_batch = self.db_ops.get_latest_batch(self.company_url)
            batch_id = latest_batch.batchId if latest_batch else 0
            previous_job_ids = set(self.db_ops.get_job_ids_by_batch(
                self.company_url,
                batch_id,
                status=[0, 1]
            ))

            all_jobs = self._fetch_job_listings(total_jobs)

            # 从现有职位中提取所有job_ids
            current_job_ids = {
                job.get('id', '')
                for job in all_jobs
            }

            # 找出重复的job_ids
            repeated_job_ids = current_job_ids.intersection(previous_job_ids)
            new_job_ids = current_job_ids - previous_job_ids

            # 一次性获取所有重复职位的数据
            repeated_jobs_dict = self.db_ops.get_jobs_by_ids(list(repeated_job_ids))

            # 分离新职位和重复职位
            new_jobs = [
                job for job in all_jobs
                if job.get('id', '') in new_job_ids
            ]
            repeated_jobs = list(repeated_jobs_dict.values())

            logging.info(f"Found {len(new_jobs)} new jobs and {len(repeated_jobs)} repeated jobs")

            # 只处理新职位的详细信息
            processed_jobs = []
            if new_jobs:
                with ThreadPoolExecutor(max_workers=max_workers) as executor:
                    future_to_job = {
                        executor.submit(self.fetch_job_details, job.get('externalPath', '')): job
                        for job in new_jobs
                    }

                    completed = 0
                    total_new = len(new_jobs)
                    for future in as_completed(future_to_job):
                        job_details = future.result()
                        if job_details:
                            parsed_job = self.parse_job(job_details)
                            if parsed_job:
                                processed_jobs.append(parsed_job)
                        completed += 1
                        progress = (completed / total_new) * 100
                        logging.info(f"Processing new job details: {completed}/{total_new} ({progress:.2f}%)")

            # 合并新处理的职位和重复的职位
            all_processed_jobs = processed_jobs + repeated_jobs
            logging.info(f"Successfully processed {len(all_processed_jobs)} total jobs")
            return all_processed_jobs,total_jobs

        except Exception as e:
            logging.error(f"Error in fetch_jobs: :{self.company_name}: {str(e)}")
            return [],0

    def _fetch_job_listings(self, total_jobs):
        """获取所有职位列表"""
        try:
            jobs_per_request = 50
            total_requests = (total_jobs // jobs_per_request) + (1 if total_jobs % jobs_per_request else 0)
            all_jobs = []

            with ThreadPoolExecutor(max_workers=25) as executor:
                futures = []
                for i in range(total_requests):
                    time.sleep(3)
                    json = {
                        'partnerId': self.partnerid,
                        'siteId': self.siteid,
                        'keyword': '',
                        'location': '',
                        'keywordCustomSolrFields': 'FORMTEXT12,JobTitle,Location,AutoReq',
                        'locationCustomSolrFields': 'Location',
                        'linkId': '',
                        'Latitude': 0,
                        'Longitude': 0,
                        'facetfilterfields': {
                            'Facet': [],
                        },
                        'powersearchoptions': {
                            'PowerSearchOption': [],
                        },
                        'SortType': 'LastUpdated',
                        'pageNumber': i+1,
                        'encryptedSessionValue': '^A79xaodIcBuc50J1nbsNqHEal7mZVRL7QWUo5hNc_slp_rhc_8eAK3gfFZJOyLrgu5RReM2W6xhk8oyifgjyuZQv3LSJh_slp_rhc_uFhxXV5A2MoDgLqcYikAg=',
                    }
                    futures.append(
                        executor.submit(
                            self.make_request,
                            'POST',
                            self.base_url,
                            self.company_name,
                            requset_count=3,
                            json=json
                        )
                    )

                for future in as_completed(futures):
                    try:
                        response = future.result()

                        jobs = response.json()['Jobs']['Job']
                        # 使用新函数处理每个职位数据
                        simplified_jobs = [_simplify_job_data(job) for job in jobs]
                        all_jobs.extend(simplified_jobs)
                        logging.info(f"Progress: {len(all_jobs)}/{total_jobs} jobs fetched")
                    except Exception as e:
                        logging.error(f"Error processing job batch：{self.company_name}: {str(e)}")

            return all_jobs
        except Exception as e:
            logging.error(f"Error in fetch_job_listings: {self.company_name}: {str(e)}")
            return []

    def __del__(self):
        """确保 HTTP 客户端被正确关闭"""
        if hasattr(self, 'client'):
            self.client.close()
