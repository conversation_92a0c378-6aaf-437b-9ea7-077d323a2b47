# models/data_models.py
from sqlalchemy import create_engine, Column, Integer, String, Text, DateTime, Date, Time
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.sql import func
from datetime import datetime
from config.settings import MYSQL_CONFIG

Base = declarative_base()


class Batch(Base):
    """批次表：记录爬取时间，工作数量"""
    __tablename__ = 'batch_tables'

    id = Column(Integer, primary_key=True)
    batchId = Column(Integer)
    scrape_date = Column(Date)
    scrape_time = Column(Time)
    batchSize = Column(Integer)
    company = Column(String(255))
    companyUrl = Column(String(255))
    cwiq_code = Column(String(255))
    total_jobs = Column(String(255))
    success_rate = Column(String(255))

    def to_dict(self):
        return {column.name: getattr(self, column.name)
                for column in self.__table__.columns
                if column.name != "id"}


class Job(Base):
    """功能表：判断重复，新增，删除数据"""
    __tablename__ = 'jobs'

    id = Column(Integer, primary_key=True)
    company = Column(String(255))
    cwiq_code = Column(String(255))
    companyUrl = Column(String(255))
    jobId = Column(String(255))
    jobTitle = Column(Text)
    department = Column(String(255))
    country = Column(String(255))
    city = Column(String(255))
    location = Column(Text)
    jobDescription = Column(Text)
    post_time = Column(DateTime)
    inactive_time = Column(DateTime)
    created_time = Column(DateTime, default=func.now())
    updated_time = Column(DateTime, onupdate=func.now())
    deleted_time = Column(DateTime)
    jobRawData = Column(String(255))
    jobUrl = Column(Text)
    jobStatus = Column(Integer)
    batchId = Column(Integer)
    scrape_date = Column(Date)
    scrape_time = Column(Time)
    jobRepost = Column(Integer)
    week_jobRepost = Column(Integer)
    day_jobRepost = Column(Integer)
    last_batch_repost = Column(Integer)

    def to_dict(self):
        return {column.name: getattr(self, column.name)
                for column in self.__table__.columns
                if column.name != "id"}


class NoJob(Base):
    """异常为空或本身网站无职位信息"""
    __tablename__ = 'no_jobs'

    id = Column(Integer, primary_key=True)
    company = Column(String(255))
    companyUrl = Column(String(255))
    batchId = Column(Integer)

    def to_dict(self):
        return {column.name: getattr(self, column.name)
                for column in self.__table__.columns
                if column.name != "id"}


class HistoryJob(Base):
    """历史发布职位职位"""
    __tablename__ = 'history_jobs'

    id = Column(Integer, primary_key=True)
    companyUrl = Column(String(255))
    jobId = Column(String(255))
    post_time = Column(DateTime)

    def to_dict(self):
        return {column.name: getattr(self, column.name)
                for column in self.__table__.columns
                if column.name != "id"}


class ActiveJob(Base):
    """当前在线职位"""
    __tablename__ = 'active_jobs'

    id = Column(Integer, primary_key=True, autoincrement=True, comment="自增主键ID")
    jobId = Column(String(255), comment="职位Id")
    jobTitle = Column(Text, comment="职位名称")
    company = Column(String(255), comment="公司名称")
    city = Column(String(255), comment="所在城市")
    jobDescription = Column(Text, comment="职位描述")
    jobUrl = Column(Text, comment="职位URL")
    companyUrl = Column(String(255), comment="公司URL")
    activeTime = Column(DateTime, default=func.now())

    def to_dict(self):
        return {column.name: getattr(self, column.name)
                for column in self.__table__.columns
                if column.name != "id"}
