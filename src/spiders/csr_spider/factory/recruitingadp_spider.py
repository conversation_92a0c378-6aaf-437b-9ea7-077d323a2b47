from urllib.parse import urlparse, urljoin

from spiders.core_spider.base_spider import BaseSpider
from utils.country_utils import CountryCodeUtils
import logging
from concurrent.futures import ThreadPoolExecutor, as_completed
import random
import time
from datetime import datetime

from utils.http_utils.http_client import HTTPClient

import requests
from selenium import webdriver


# recruitingadp的模板方法
class RecruitingAdpSpider(BaseSpider):
    def __init__(self, company_name, base_url, company_url, cwiq_code):
        super().__init__(company_name, base_url, company_url, cwiq_code)
        self.company_name = company_name
        self.company_url = company_url
        self.cwiq_code = cwiq_code
        self.country_utils = CountryCodeUtils()
        # POST 请求的请求体（根据接口需求调整）
        data = {
            "filters": [
                {
                    "name": "state",
                    "label": "State"
                },
                {
                    "name": "city",
                    "label": "City"
                },
                {
                    "name": "grp",
                    "label": "Area of Interest"
                },
                {
                    "name": "typeOfFulltime",
                    "label": "Full Time/Part Time"
                }
            ],
            "results": {
                "pageTitle": "Search Results",
                "zeroResultsMessage": "We're sorry but we have no job openings at this time that match your search criteria. Please try another search.",
                "searchFailureMessage": "Oops! Something went wrong.  Search has encountered a problem. Try searching again",
                "resultsFoundLabel": "results found",
                "bookmarkText": "Bookmark This",
                "pageSize": "100",
                "sortOrder": "00001000",
                "shareText": "Share",
                "fields": [
                    {
                        "name": "ptitle",
                        "label": "Published Job Title"
                    },
                    {
                        "name": "num",
                        "label": "Req Num"
                    },
                    {
                        "name": "location",
                        "label": "Location"
                    },
                    {
                        "name": "postingLocationCode",
                        "label": "Posting Location"
                    }
                ]
            },
            "pagefilter": {
                "page": 1
            },
            "rl": "enUS"
        }

        # 发送 POST 请求
        response = requests.post(self.base_url, json=data, allow_redirects=True)

        # 获取服务器返回的 Cookie
        cookies_dict = response.cookies.get_dict()
        print(cookies_dict)

        # 转换 Cookie 为 Header 格式
        formatted_cookies = "; ".join([f"{key}={value}" for key, value in cookies_dict.items()])
        print(formatted_cookies)

        # driver = webdriver.Chrome()
        # # 打开目标网址
        # driver.get(self.base_url)
        #
        # # 执行动态加载
        # driver.implicitly_wait(10)
        #
        # # 获取完整 Cookies
        # selenium_cookies = driver.get_cookies()
        # formatted_cookies = "; ".join([f"{cookie['name']}={cookie['value']}" for cookie in selenium_cookies])
        # print(formatted_cookies)
        #
        # driver.quit()

        self.headers = {
            "Content-Type": "application/json; charset=UTF-8",
            "Accept": "application/json, text/plain, */*",
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/131.0.0.0 Safari/537.36 Edg/131.0.0.0",
            "Cookie": "com.airs.srccar.Controller.tx=enUS; JSESSIONID=7D4F9DB7501F8046685CE455FCB9314D; _gcl_au=1.1.1902322803.1732759318; _clck=1jqvfoa%7C2%7Cfr9%7C0%7C1793; _ga_HWMYGFEK56=GS1.1.1732759318.1.1.1732759379.60.0.0; _evga_99c9={%22uuid%22:%225a65a8b2f481aced%22}; _sfid_7580={%22anonymousId%22:%225a65a8b2f481aced%22%2C%22consents%22:[]}; LZH6034950464=RTI; k8Ksj346=A4U1nouTAQAAEetjN3hkRq5Qhg5CZTFIo5M4ktvyjfO5acoKEg700Rf-11m0AUg0YkoXTu_LwH8AAEB3AAAAAA|1|0|974a9d0e42d0610a1aaa71a81af855d5c515544f; _ga=GA1.2.1841913069.1732759319; _gid=GA1.2.708176218.1734400586; _ga_5PYY1XGWM6=GS1.2.1734400586.2.0.1734400586.60.0.0; bm_mi=CB903A9BC15F1CF7A02326646FE11DF6~YAAQU17WFwso1beTAQAA/lXh0xreH9yruwYH8HRLGt9bjp8wix4zyEb8P+IHhAlEo2uSiWUCeybeVGat+WiAXrr5EjPaSY46DgJ8LXjgMAwjh8jGMpWKcmW80PiG34m6CYTkEnn3YbzVBpXGyhJAVBUrTZMN3VctZTBOG+XT1oiXcSuD6WzAencq/qVEV6RjaD8Etq2dsWzZdPfvuyNCPn/NZxyenbLFS7rSAmaIaqzV2+7/g2h5p2y1y2mb/w2iSEEUlTvJekgNHVOr5WlsZoSHVkJTyaDMovSfUBChNKO1mchqwMSvS8Wn/HJQJ7fWSoW0pJ/mC/GVb+gNP/lynrjXq011TT2yxlJ4Q+VZ~1; ak_bmsc=54BA11DF9E86AEC849DE0156A87753D9~000000000000000000000000000000~YAAQU17WF2Mo1beTAQAAOFnh0xpMNlVUDeexFcemhV+CnW1caCqG+itnjt7sdDtonF8gmKqvaDse5SkxsiAXPoS0Lg7wXeM8GczJqLw2A6VwQOgpy4wOVVuqYOgwSZC7MnrFMU+J5NJtysX1NAybjah+hE4zrC1DEX5w7jpjXqL67qTzmWDaTow2VUQdgTduPNd6wLh4IiD9uuAs9zhPlZPvJfaISGK1vZ9AqgabHCYpuNxBMMn6X1BJXQqyyk45gRJPoIhj0ui28MkxcWXM/5FjzvaWMRjSYqdq7A36e6A5WwHWneRwG9PP7Zii7r8BngFi9qPhG17qbgO4G2Xu4DEYgaG/RrJsOrH+UvFm03OghAxGHAp6yFzzp8llHtPYUJwKFZdRYVWRe57tL3xQzvn29BvnPhdaGC4/AhyIfHf5H+EvhIW4oGpRaePtYIt0YD7IufRYhi4YY7LpUaNmKCfWAWxOR2Xs/4G7n5baIcI7fr2YMCk=; BIGipServer~TrafficGroup2~p_recruiting.adp.com_srccar=!Q3rR04l2D/CRVEI4uS/PNTenYaRl89ExNOm3/IhkXM+qxqP8Ztir131hg1Vr+pi9nhW4qtJnHM1m/A==; BIGipServerp_recruiting.adp.com=!7J1ln1A9nWPwO3A4uS/PNTenYaRl8yyThP7VRnYYYxnx4Cs4Wu2pZ6lcry4BOrxO8SOKdV8TVTHQ; BIGipServer~TrafficGroup2~p_recruiting.adp.com_POD2=!MqmVVuTg92oBKWA4uS/PNTenYaRl8//ach011lblcC08n27LRLVWRL2t/4GyT5g++2JslEVbHPfWlg==; BIGipServer~TrafficGroup2~p_recruiting.adp.com_srccar_POD4=!36QeVSLHEdNmnnA4uS/PNTenYaRl8z+vwMdLX9iZ2kWjP6tm4oDgPaHFRkdPv04YHACYJotRVzfyBI0=; BIGipServer~TrafficGroup2~p_recruiting.adp.com_POD4=!pq+CfrOLt5d3gT84uS/PNTenYaRl8wBpQm0zvSCmkCUq85Q7r/75dmpNU5Kctg/2/WDFFaTMkYvq17w=; BIGipServer~TrafficGroup2~p_recruiting.adp.com=!VFQd3hENdDZM+UA4uS/PNTenYaRl8+xPMmh6bHzabkMFKqj7K2F+UysgDez4Q7AO7bFmTdgoVH80zw==; BIGipServer~TrafficGroup2~p_recruiting.adp.com_POD1=!kja4t6m+CHXQMLc4uS/PNTenYaRl82L+qjsplXZ0zMrwbD13heisGKND/yaMAsnHXS4Cqw4/ndG7Lg==; ADPRMPODROUTING=RMPOD3;"
        }

        # 创建 HTTP 客户端，使用全局代理配置
        self.client = HTTPClient()

    @classmethod
    def create_spider(cls, company_name, base_url, company_url, cwiq_code):
        """工厂方法创建 MedtronicSpider 实例"""
        return cls(company_name, base_url, company_url, cwiq_code)

    def make_request(self, method, url, company_name,requset_count, **kwargs):
        """发送请求的通用方法"""
        kwargs['headers'] = self.headers
        return self.client.request(method, url, company_name, requset_count,log_ip=False, **kwargs)

    def get_total_jobs(self):
        """获取总职位数"""
        try:
            payload = {
                "filters": [
                    {
                        "name": "state",
                        "label": "State"
                    },
                    {
                        "name": "city",
                        "label": "City"
                    },
                    {
                        "name": "grp",
                        "label": "Area of Interest"
                    },
                    {
                        "name": "typeOfFulltime",
                        "label": "Full Time/Part Time"
                    }
                ],
                "results": {
                    "pageTitle": "Search Results",
                    "zeroResultsMessage": "We're sorry but we have no job openings at this time that match your search criteria. Please try another search.",
                    "searchFailureMessage": "Oops! Something went wrong.  Search has encountered a problem. Try searching again",
                    "resultsFoundLabel": "results found",
                    "bookmarkText": "Bookmark This",
                    "pageSize": "100",
                    "sortOrder": "00001000",
                    "shareText": "Share",
                    "fields": [
                        {
                            "name": "ptitle",
                            "label": "Published Job Title"
                        },
                        {
                            "name": "num",
                            "label": "Req Num"
                        },
                        {
                            "name": "location",
                            "label": "Location"
                        },
                        {
                            "name": "postingLocationCode",
                            "label": "Posting Location"
                        }
                    ]
                },
                "pagefilter": {
                    "page": 1
                },
                "rl": "enUS"
            }
            response = self.make_request('POST', self.base_url, self.company_name,requset_count=3, json=payload)
            return response.json()['count']
        except Exception as e:
            logging.error(f"Error getting total jobs: {e} for {self.company_name}")
            return 0

    def parse_job(self, job_data):
        """解析职位数据"""
        try:
            if job_data:
                # 解析地点信息
                locationaddress = job_data.get("locationaddress", "")[0]
                # 按逗号分隔
                address_parts = locationaddress.split(",")
                country_name = address_parts[-1].strip().split(" ")[0]
                country_code = self.country_utils.get_alpha3(country_name)
                return {
                    "jobId": job_data.get("id", ""),
                    "company": self.company_name,
                    "cwiq_code": self.cwiq_code,
                    "companyUrl": self.company_url,
                    "country": country_code,
                    "city": job_data.get("city", ""),
                    "jobTitle": job_data.get("ptitle", ""),
                    # "department": job_info.get("hiringOrganization", {}).get("name", ""),
                    "location": job_data.get("state", ""),
                    "jobDescription": job_data.get("description", ""),
                    "jobUrl": job_data.get("url", ""),
                    "created_time": None,
                    "updated_time": None,
                    "post_time": datetime.now(),
                }
        except Exception as e:
            logging.error(f"Error parsing job data: {e} for {self.company_name}")
            return None

    def fetch_jobs(self, max_workers):
        """获取所有职位信息"""
        try:
            # 获取工作总数量
            total_jobs = self.get_total_jobs()
            if total_jobs == 0:
                logging.info(f"No jobs found {self.company_name}")
                return [],0

            logging.info(f"Found {total_jobs} total jobs")

            # 遍历每一页
            jobs_per_request = 10
            total_requests = (total_jobs // jobs_per_request) + (1 if total_jobs % jobs_per_request else 0)
            all_jobs = []
            processed_jobs = []

            for i in range(total_requests):
                payload = {
                    "filters": [
                        {
                            "name": "state",
                            "label": "State"
                        },
                        {
                            "name": "city",
                            "label": "City"
                        },
                        {
                            "name": "grp",
                            "label": "Area of Interest"
                        },
                        {
                            "name": "typeOfFulltime",
                            "label": "Full Time/Part Time"
                        }
                    ],
                    "results": {
                        "pageTitle": "Search Results",
                        "zeroResultsMessage": "We're sorry but we have no job openings at this time that match your search criteria. Please try another search.",
                        "searchFailureMessage": "Oops! Something went wrong.  Search has encountered a problem. Try searching again",
                        "resultsFoundLabel": "results found",
                        "bookmarkText": "Bookmark This",
                        "pageSize": "100",
                        "sortOrder": "00001000",
                        "shareText": "Share",
                        "fields": [
                            {
                                "name": "ptitle",
                                "label": "Published Job Title"
                            },
                            {
                                "name": "locationCode",
                                "label": "Location Code"
                            }
                        ]
                    },
                    "pagefilter": {
                        "page": i + 1
                    },
                    "rl": "enUS"
                }

                # 发送请求
                try:
                    response = self.make_request('POST', self.base_url, self.company_name, requset_count=3,json=payload)
                    jobs = response.json().get('jobs', [])
                    all_jobs.extend(jobs)

                    # 解析每条数据
                    for job in jobs:
                        parsed_job = self.parse_job(job)
                        if parsed_job:
                            processed_jobs.append(parsed_job)

                    # 显示进度
                    if total_jobs > 0:  # 避免除以零
                        progress = (len(all_jobs) / total_jobs) * 100
                        logging.info(f"Progress: {len(all_jobs)}/{total_jobs} jobs fetched ({progress:.2f}%)")

                except Exception as e:
                    logging.error(f"Error processing job batch ：{self.company_name}{i + 1}: {e}")

            logging.info(f"Successfully fetched {len(processed_jobs)} Medtronic jobs")
            return processed_jobs,total_jobs

        except Exception as e:
            logging.error(f"Error in fetch_jobs: {e}:{self.company_name}")
            return [],0


    def __del__(self):
        """确保 HTTP 客户端被正确关闭"""
        if hasattr(self, 'client'):
            self.client.close()
