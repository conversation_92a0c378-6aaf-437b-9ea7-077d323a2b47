# Marzaha 爬虫项目 - 核心运行时依赖
# 基于源码分析的最小化依赖集合

# 任务调度 (spider.py)
APScheduler==3.10.4

# 数据库操作 (utils/mysql_handler.py, models/data_models.py)
PyMySQL==1.1.0
SQLAlchemy==2.0.23

# 数据处理 (utils/db_operations.py)
pandas==2.1.4
pyarrow==14.0.2

# HTTP请求 (utils/http_utils/http_client.py)
requests==2.31.0
urllib3==2.1.0

# 网页解析 (所有爬虫文件)
beautifulsoup4==4.12.2
lxml==4.9.4

# 配置文件 (config/spider_config.py)
PyYAML==6.0.1

# 地理位置 (utils/country_utils.py)
pycountry==24.6.1
geopy==2.4.1

# 云存储 (utils/s3/, utils/gcp/)
boto3==1.34.0
google-cloud-storage==2.10.0

# 系统监控 (utils/monitor.py)
psutil==5.9.6
